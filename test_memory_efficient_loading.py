#!/usr/bin/env python3
"""
Test script for memory-efficient multi-file loading.
This loads only a single sensor from multiple files.
"""

import sys
import numpy as np
import h5py
from pathlib import Path
import psutil
import os

# Add src directory to path
sys.path.append('src')

from chronos_loader import DASChronosDataset

def get_memory_usage():
    """Get current memory usage in MB."""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def load_single_sensor_from_file(file_path, sensor_idx):
    """Memory-efficient loading of single sensor."""
    with h5py.File(file_path, 'r') as f:
        das_name = list(f.keys())[0]
        zone_path = f"{das_name}/Source1/Zone1/"
        sr_dataset_path = f"{zone_path}SR_Valencia"
        
        if sr_dataset_path not in f:
            raise ValueError(f"Could not find strain rate data at {sr_dataset_path}")
        
        sr_dataset = f[sr_dataset_path]
        dims = sr_dataset.shape
        
        nb_block = dims[0]
        sampling_frequency = 250
        
        # Extract only the target sensor from all blocks
        sensor_data_blocks = []
        
        for tt in range(nb_block):
            block_data = sr_dataset[tt, :sampling_frequency, sensor_idx]
            sensor_data_blocks.append(block_data)
        
        sensor_data = np.concatenate(sensor_data_blocks)
        
    return sensor_data.astype(np.float32)

def test_memory_efficient_loading():
    """Test memory-efficient loading vs traditional loading."""
    print("=== Memory-Efficient Multi-File Loading Test ===")
    
    # Find H5 files
    data_dir = Path("Data")
    h5_files = sorted(list(data_dir.rglob("*.h5")))
    
    print(f"Found {len(h5_files)} H5 files")
    if len(h5_files) < 3:
        print("Need at least 3 files for meaningful test")
        return False
    
    # Test 1: Find active sensor from first file
    print(f"\n=== Step 1: Finding Active Sensor ===")
    initial_memory = get_memory_usage()
    print(f"Initial memory usage: {initial_memory:.1f} MB")
    
    # Load first file to find active sensor
    dataset = DASChronosDataset(
        data_path=h5_files[0],
        context_length=128,
        prediction_length=32,
        stride=16,
        normalize=False,
        apply_filtering=False
    )
    
    after_load_memory = get_memory_usage()
    print(f"Memory after loading first file: {after_load_memory:.1f} MB (+{after_load_memory - initial_memory:.1f} MB)")
    print(f"First file shape: {dataset.data.shape}")
    
    # Find active sensor
    active_sensor = None
    for i in range(min(100, dataset.data.shape[0])):
        sensor_data = dataset.data[i, :5000]
        if np.std(sensor_data) > 1.0:
            active_sensor = i
            break
    
    if active_sensor is None:
        print("No active sensor found")
        return False
    
    print(f"Selected active sensor: {active_sensor}")
    
    # Clear memory
    del dataset
    
    # Test 2: Memory-efficient loading of multiple files
    print(f"\n=== Step 2: Memory-Efficient Multi-File Loading ===")
    before_multiload_memory = get_memory_usage()
    print(f"Memory before multi-file loading: {before_multiload_memory:.1f} MB")
    
    all_sensor_data = []
    file_info = []
    
    for i, file_path in enumerate(h5_files[:6]):  # Load up to 6 files
        print(f"  Loading sensor {active_sensor} from file {i+1}: {file_path.name}")
        
        try:
            sensor_data = load_single_sensor_from_file(file_path, active_sensor)
            all_sensor_data.append(sensor_data)
            
            file_info.append({
                'filename': file_path.name,
                'length': len(sensor_data),
                'duration_seconds': len(sensor_data) / 250
            })
            
            current_memory = get_memory_usage()
            print(f"    Loaded {len(sensor_data)} samples ({len(sensor_data)/250:.1f}s), Memory: {current_memory:.1f} MB")
            
        except Exception as e:
            print(f"    Failed to load {file_path.name}: {e}")
            continue
    
    if not all_sensor_data:
        print("No files loaded successfully")
        return False
    
    # Concatenate all sensor data
    concatenated_data = np.concatenate(all_sensor_data)
    final_memory = get_memory_usage()
    
    print(f"\n=== Results ===")
    print(f"Files loaded: {len(all_sensor_data)}")
    print(f"Total samples: {len(concatenated_data)}")
    print(f"Total duration: {len(concatenated_data)/250:.1f} seconds ({len(concatenated_data)/250/60:.1f} minutes)")
    print(f"Final memory usage: {final_memory:.1f} MB")
    print(f"Memory increase: {final_memory - initial_memory:.1f} MB")
    print(f"Data size: {concatenated_data.nbytes / 1024 / 1024:.1f} MB")
    
    # Test normalization
    print(f"\n=== Step 3: Data Normalization ===")
    print(f"Raw data range: [{np.min(concatenated_data):.2f}, {np.max(concatenated_data):.2f}]")
    
    # Normalize
    normalized_data = (concatenated_data - np.mean(concatenated_data)) / np.std(concatenated_data)
    print(f"Normalized range: [{np.min(normalized_data):.3f}, {np.max(normalized_data):.3f}]")
    
    # Memory efficiency comparison
    print(f"\n=== Memory Efficiency Analysis ===")
    single_sensor_size = concatenated_data.nbytes / 1024 / 1024
    if len(h5_files) > 0:
        # Estimate full dataset size (all sensors from all files)
        estimated_full_size = single_sensor_size * dataset.data.shape[0] * len(all_sensor_data)
        print(f"Single sensor data: {single_sensor_size:.1f} MB")
        print(f"Estimated full dataset size: {estimated_full_size:.1f} MB ({estimated_full_size/1024:.1f} GB)")
        print(f"Memory savings: {100 * (1 - single_sensor_size/estimated_full_size):.1f}%")
    
    print(f"\n✓ Memory-efficient loading test PASSED!")
    print(f"Ready for anomaly analysis with {len(concatenated_data)} samples from {len(all_sensor_data)} files")
    
    return True

if __name__ == "__main__":
    try:
        success = test_memory_efficient_loading()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
