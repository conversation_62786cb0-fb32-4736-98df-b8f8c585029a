#!/usr/bin/env python3
"""
Complete End-to-End Pipeline for DAS Data Analysis with Chronos T5
This script combines DAS data loading, preprocessing, and Chronos T5 forecasting
into a single comprehensive pipeline.
"""

import torch
import numpy as np
import h5py
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CompleteDASChronosPipeline:
    """
    Complete pipeline for DAS data analysis using Chronos T5 model.
    """
    
    def __init__(self, 
                 model_name: str = "amazon/chronos-t5-small",
                 context_length: int = 512,
                 prediction_length: int = 64,
                 device: str = 'auto'):
        """
        Initialize the complete DAS-Chronos pipeline.
        
        Parameters:
        -----------
        model_name : str
            Chronos T5 model variant to use
        context_length : int
            Length of input context window
        prediction_length : int
            Length of prediction horizon
        device : str
            Device to run computations on
        """
        
        self.model_name = model_name
        self.context_length = context_length
        self.prediction_length = prediction_length
        self.device = device if device != 'auto' else ('cuda' if torch.cuda.is_available() else 'cpu')
        
        logger.info(f"Initializing DAS-Chronos Pipeline")
        logger.info(f"Model: {model_name}")
        logger.info(f"Context length: {context_length}")
        logger.info(f"Prediction length: {prediction_length}")
        logger.info(f"Device: {self.device}")
        
        # Initialize components
        self.das_data = None
        self.dataset = None
        self.predictor = None
        self.results = {}
        
    def load_das_data(self, 
                     data_path: Optional[str] = None,
                     data_array: Optional[np.ndarray] = None,
                     **preprocessing_kwargs) -> np.ndarray:
        """
        Load and preprocess DAS data.
        
        Parameters:
        -----------
        data_path : str, optional
            Path to HDF5 DAS file
        data_array : np.ndarray, optional
            Pre-loaded DAS data
        **preprocessing_kwargs : dict
            Additional preprocessing parameters
            
        Returns:
        --------
        processed_data : np.ndarray
            Preprocessed DAS data
        """
        
        logger.info("Loading DAS data...")
        
        if data_path is not None:
            self.das_data = self._load_from_hdf5(data_path)
        elif data_array is not None:
            self.das_data = np.array(data_array, dtype=np.float32)
        else:
            # Create synthetic data for demonstration
            logger.info("No data provided, creating synthetic DAS data for demo...")
            self.das_data = self._create_synthetic_das_data()
        
        # Apply preprocessing
        self.das_data = self._preprocess_das_data(self.das_data, **preprocessing_kwargs)
        
        logger.info(f"DAS data loaded: {self.das_data.shape} (sensors x timesteps)")
        return self.das_data
    
    def _load_from_hdf5(self, data_path: str) -> np.ndarray:
        """Load DAS data from HDF5 file."""
        data_path = Path(data_path)
        
        if not data_path.exists():
            raise FileNotFoundError(f"HDF5 file not found: {data_path}")
        
        with h5py.File(data_path, 'r') as f:
            # Try common DAS dataset names
            possible_names = ['strain_rate', 'StrainRate', 'data', 'SR_Valencia']
            
            for name in possible_names:
                if name in f:
                    return np.array(f[name][:], dtype=np.float32)
            
            # If not found, search recursively
            def find_largest_dataset(name, obj):
                if isinstance(obj, h5py.Dataset):
                    return obj
                return None
            
            datasets = []
            f.visititems(lambda name, obj: datasets.append(obj) if isinstance(obj, h5py.Dataset) else None)
            
            if datasets:
                # Use largest dataset
                largest = max(datasets, key=lambda x: x.size)
                logger.info(f"Using dataset: {largest.name}")
                return np.array(largest[:], dtype=np.float32)
            
            raise ValueError("Could not find suitable dataset in HDF5 file")
    
    def _create_synthetic_das_data(self, 
                                  n_sensors: int = 100, 
                                  n_timesteps: int = 5000) -> np.ndarray:
        """Create synthetic DAS data for demonstration."""
        
        # Generate realistic DAS-like synthetic data
        data = np.random.randn(n_sensors, n_timesteps).astype(np.float32) * 0.1
        
        # Add spatial and temporal patterns
        for i in range(n_sensors):
            t = np.arange(n_timesteps)
            
            # Add seismic-like events at random times
            num_events = np.random.randint(2, 8)
            for _ in range(num_events):
                event_time = np.random.randint(100, n_timesteps - 100)
                event_duration = np.random.randint(10, 50)
                event_amplitude = np.random.uniform(2, 8)
                
                # Gaussian pulse for event
                event_window = np.exp(-0.5 * ((t - event_time) / (event_duration/3))**2)
                data[i, :] += event_amplitude * event_window
            
            # Add continuous low-frequency noise
            noise_freq = 0.001 + i * 0.0001  # Slight frequency variation per sensor
            data[i, :] += 0.5 * np.sin(2 * np.pi * noise_freq * t)
            
            # Add spatial correlation (neighboring sensors are similar)
            if i > 0:
                correlation = 0.3
                data[i, :] += correlation * data[i-1, :]
        
        return data
    
    def _preprocess_das_data(self, 
                           data: np.ndarray,
                           normalize: bool = True,
                           filter_outliers: bool = True,
                           outlier_threshold: float = 5.0) -> np.ndarray:
        """Preprocess DAS data."""
        
        logger.info("Preprocessing DAS data...")
        
        # Handle non-finite values
        data = np.where(np.isfinite(data), data, 0)
        
        # Filter outliers
        if filter_outliers:
            for i in range(data.shape[0]):
                series = data[i, :]
                mean_val = np.mean(series)
                std_val = np.std(series)
                
                if std_val > 0:
                    z_scores = np.abs((series - mean_val) / std_val)
                    outlier_mask = z_scores > outlier_threshold
                    
                    if np.any(outlier_mask):
                        median_val = np.median(series[~outlier_mask])
                        data[i, outlier_mask] = median_val
        
        # Normalize data
        if normalize:
            self.normalization_stats = {}
            for i in range(data.shape[0]):
                series = data[i, :]
                mean_val = np.mean(series)
                std_val = np.std(series)
                
                if std_val > 0:
                    data[i, :] = (series - mean_val) / std_val
                    self.normalization_stats[i] = {'mean': mean_val, 'std': std_val}
                else:
                    self.normalization_stats[i] = {'mean': mean_val, 'std': 1.0}
        
        return data
    
    def create_dataset(self, stride: int = 32, **dataset_kwargs):
        """Create dataset from loaded DAS data."""
        
        if self.das_data is None:
            raise ValueError("DAS data not loaded. Call load_das_data() first.")
        
        logger.info("Creating dataset...")
        
        # Import here to avoid circular imports
        from das_chronos_dataloader import DASChronosDataset
        
        self.dataset = DASChronosDataset(
            data_array=self.das_data,
            context_length=self.context_length,
            prediction_length=self.prediction_length,
            stride=stride,
            normalize=False,  # Already normalized in preprocessing
            **dataset_kwargs
        )
        
        logger.info(f"Dataset created with {len(self.dataset)} samples")
        return self.dataset
    
    def load_model(self, **model_kwargs):
        """Load and initialize the Chronos T5 model."""
        
        logger.info("Loading Chronos T5 model...")
        
        # Import here to avoid circular imports  
        from chronos_t5_model import ChronosDASPredictor
        
        self.predictor = ChronosDASPredictor(
            model_name=self.model_name,
            device=self.device,
            **model_kwargs
        )
        
        logger.info("Chronos T5 model loaded successfully")
        return self.predictor
    
    def run_forecasting(self, 
                       batch_size: int = 16,
                       num_samples: int = 50,
                       num_batches: Optional[int] = None,
                       save_results: bool = True) -> Dict:
        """
        Run time series forecasting on the dataset.
        
        Parameters:
        -----------
        batch_size : int
            Batch size for inference
        num_samples : int
            Number of prediction samples to generate
        num_batches : int, optional
            Limit number of batches to process (for testing)
        save_results : bool
            Whether to save results
            
        Returns:
        --------
        results : Dict
            Forecasting results and metrics
        """
        
        if self.dataset is None:
            raise ValueError("Dataset not created. Call create_dataset() first.")
        if self.predictor is None:
            raise ValueError("Model not loaded. Call load_model() first.")
        
        logger.info("Starting forecasting...")
        
        # Import here to avoid circular imports
        from das_chronos_dataloader import DASChronosDataLoader
        
        # Create data loader
        dataloader = DASChronosDataLoader.create_val_loader(
            self.dataset, 
            batch_size=batch_size
        )
        
        # Limit batches if specified (for testing)
        if num_batches is not None:
            dataloader = torch.utils.data.DataLoader(
                self.dataset,
                batch_size=batch_size,
                shuffle=False,
                num_workers=0,
                sampler=torch.utils.data.SubsetRandomSampler(range(min(num_batches * batch_size, len(self.dataset))))
            )
        
        # Run predictions
        results = self.predictor.predict_batch(
            dataloader=dataloader,
            prediction_length=self.prediction_length,
            num_samples=num_samples,
            save_results=save_results,
            results_path=f"das_chronos_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pt"
        )
        
        # Evaluate results
        evaluation = self.predictor.evaluate_predictions(
            predictions=results['predictions'],
            targets=results['targets'],
            metrics=['mse', 'mae', 'rmse', 'mape', 'coverage_80', 'interval_width']
        )
        
        results['evaluation'] = evaluation
        self.results = results
        
        logger.info("Forecasting completed!")
        self._print_evaluation_results(evaluation)
        
        return results
    
    def _print_evaluation_results(self, evaluation: Dict):
        """Print evaluation results in a formatted way."""
        
        print("\n" + "="*50)
        print("📊 FORECASTING EVALUATION RESULTS")
        print("="*50)
        
        print(f"📈 Point Prediction Metrics:")
        print(f"   MSE:  {evaluation.get('mse', 0):.4f}")
        print(f"   MAE:  {evaluation.get('mae', 0):.4f}")
        print(f"   RMSE: {evaluation.get('rmse', 0):.4f}")
        print(f"   MAPE: {evaluation.get('mape', 0):.2f}%")
        
        print(f"\n🎯 Probabilistic Prediction Metrics:")
        print(f"   Coverage (80%): {evaluation.get('coverage_80', 0):.1%}")
        print(f"   Interval Width: {evaluation.get('interval_width', 0):.4f}")
        
        print("="*50)
    
    def visualize_results(self, 
                         num_examples: int = 4,
                         sensors_to_plot: Optional[List[int]] = None,
                         save_plots: bool = True,
                         plot_dir: str = "das_chronos_plots"):
        """
        Create visualizations of the forecasting results.
        
        Parameters:
        -----------
        num_examples : int
            Number of examples to visualize
        sensors_to_plot : List[int], optional
            Specific sensors to plot (None = random selection)
        save_plots : bool
            Whether to save plots to files
        plot_dir : str
            Directory to save plots
        """
        
        if not self.results:
            raise ValueError("No results available. Run run_forecasting() first.")
        
        logger.info(f"Creating {num_examples} visualization plots...")
        
        if save_plots:
            plot_path = Path(plot_dir)
            plot_path.mkdir(exist_ok=True)
        
        predictions = self.results['predictions']
        targets = self.results['targets']
        past_values = self.results['past_values']
        
        # Import visualization function
        from chronos_t5_model import visualize_predictions
        
        # Select samples to visualize
        total_samples = predictions.shape[0]
        if sensors_to_plot is None:
            sample_indices = np.random.choice(total_samples, min(num_examples, total_samples), replace=False)
        else:
            sample_indices = sensors_to_plot[:num_examples]
        
        for i, sample_idx in enumerate(sample_indices):
            plt.figure(figsize=(15, 8))
            
            # Create visualization
            save_path = f"{plot_dir}/prediction_sample_{i+1}.png" if save_plots else None
            
            visualize_predictions(
                predictions=predictions,
                targets=targets,
                past_values=past_values,
                sample_idx=sample_idx,
                num_prediction_samples=20,
                save_path=save_path
            )
        
        logger.info(f"Visualizations created and saved to {plot_dir}/")
    
    def export_results(self, export_path: str = "das_chronos_export"):
        """
        Export all results and configurations.
        
        Parameters:
        -----------
        export_path : str
            Base path for export files
        """
        
        if not self.results:
            raise ValueError("No results to export. Run run_forecasting() first.")
        
        logger.info("Exporting results...")
        
        export_path = Path(export_path)
        export_path.mkdir(exist_ok=True)
        
        # Export configuration
        config = {
            'model_name': self.model_name,
            'context_length': self.context_length,
            'prediction_length': self.prediction_length,
            'device': self.device,
            'data_shape': self.das_data.shape if self.das_data is not None else None,
            'dataset_size': len(self.dataset) if self.dataset is not None else None,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(export_path / 'config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        # Export results
        torch.save(self.results, export_path / 'results.pt')
        
        # Export evaluation metrics
        if 'evaluation' in self.results:
            with open(export_path / 'evaluation.json', 'w') as f:
                json.dump(self.results['evaluation'], f, indent=2)
        
        logger.info(f"Results exported to {export_path}/")
    
    def run_complete_pipeline(self, 
                             data_path: Optional[str] = None,
                             data_array: Optional[np.ndarray] = None,
                             batch_size: int = 16,
                             num_samples: int = 50,
                             visualize: bool = True,
                             export: bool = True) -> Dict:
        """
        Run the complete DAS-Chronos pipeline from data loading to visualization.
        
        Parameters:
        -----------
        data_path : str, optional
            Path to DAS HDF5 file
        data_array : np.ndarray, optional  
            Pre-loaded DAS data
        batch_size : int
            Batch size for inference
        num_samples : int
            Number of prediction samples
        visualize : bool
            Whether to create visualizations
        export : bool
            Whether to export results
            
        Returns:
        --------
        results : Dict
            Complete pipeline results
        """
        
        print("\n🚀 Starting Complete DAS-Chronos T5 Pipeline")
        print("="*60)
        
        # Step 1: Load data
        print("\n📂 Step 1/5: Loading DAS data...")
        self.load_das_data(data_path=data_path, data_array=data_array)
        
        # Step 2: Create dataset
        print("\n📊 Step 2/5: Creating dataset...")
        self.create_dataset(stride=64)  # Less overlap for faster processing
        
        # Step 3: Load model
        print("\n🤖 Step 3/5: Loading Chronos T5 model...")
        self.load_model()
        
        # Step 4: Run forecasting
        print("\n🔮 Step 4/5: Running forecasting...")
        results = self.run_forecasting(
            batch_size=batch_size,
            num_samples=num_samples,
            num_batches=10  # Limit for demo
        )
        
        # Step 5: Visualize and export
        print("\n📈 Step 5/5: Creating visualizations and exports...")
        
        if visualize:
            self.visualize_results(num_examples=3)
        
        if export:
            self.export_results()
        
        print("\n✅ Complete pipeline finished successfully!")
        print(f"📊 Processed {self.das_data.shape[0]} sensors with {self.das_data.shape[1]} timesteps")
        print(f"🎯 Generated predictions for {results['predictions'].shape[0]} samples")
        
        return results


def main():
    """Main function demonstrating the complete pipeline."""
    
    # Initialize pipeline
    pipeline = CompleteDASChronosPipeline(
        model_name="amazon/chronos-t5-small",  # Use small model for demo
        context_length=256,  # Shorter for faster demo
        prediction_length=32,
        device='auto'
    )
    
    # Run complete pipeline with synthetic data
    results = pipeline.run_complete_pipeline(
        data_path=None,  # Will create synthetic data
        batch_size=8,
        num_samples=30,
        visualize=True,
        export=True
    )
    
    print(f"\n🎉 Pipeline demo completed!")
    print(f"Check the 'das_chronos_plots' and 'das_chronos_export' directories for outputs.")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"Pipeline failed: {str(e)}")
        print("\n❌ Pipeline failed. Common issues:")
        print("1. Missing dependencies: pip install transformers torch matplotlib")
        print("2. Insufficient GPU memory: try smaller model or batch size")
        print("3. Network issues: ensure internet connection for model download")
