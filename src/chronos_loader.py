#!/usr/bin/env python3
"""
PyTorch DataLoader for loading DAS (Distributed Acoustic Sensing) data 
into the Chronos T5 time series forecasting model.

This module provides Dataset and DataLoader classes optimized for:
- Loading HDF5 DAS files or preprocessed numpy arrays
- Windowing time series data for forecasting tasks
- Proper normalization and formatting for Chronos T5
- Handling multiple sensors/channels simultaneously
"""

import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
from typing import Tuple, Optional, Union, List, Dict
import warnings
from pathlib import Path
from scipy.signal import butter, filtfilt


class DASChronosDataset(Dataset):
    """
    PyTorch Dataset for DAS data compatible with Chronos T5 model.
    
    This dataset handles:
    - Loading DAS strain rate data from HDF5 files or numpy arrays
    - Creating sliding windows for time series forecasting
    - Normalization and preprocessing for Chronos T5
    - Multiple sensor channels
    """
    
    def __init__(self,
                 data_path: Optional[Union[str, Path]] = None,
                 data_array: Optional[np.ndarray] = None,
                 context_length: int = 512,
                 prediction_length: int = 64,
                 stride: int = 1,
                 normalize: bool = True,
                 normalization_method: str = 'z_score',
                 sensor_indices: Optional[List[int]] = None,
                 min_series_length: Optional[int] = None,
                 filter_outliers: bool = True,
                 outlier_threshold: float = 5.0,
                 apply_filtering: bool = False):
        """
        Initialize the DAS Dataset for Chronos T5.

        Parameters:
        -----------
        data_path : str or Path, optional
            Path to HDF5 file containing DAS data
        data_array : np.ndarray, optional
            Pre-loaded DAS data array of shape (n_sensors, n_timesteps)
        context_length : int
            Length of input context window (default: 512)
        prediction_length : int
            Length of prediction horizon (default: 64)
        stride : int
            Stride for sliding window (default: 1)
        normalize : bool
            Whether to normalize the data (default: True)
        normalization_method : str
            'z_score', 'min_max', or 'robust' (default: 'z_score')
        sensor_indices : List[int], optional
            Specific sensor indices to use (None = use all)
        min_series_length : int, optional
            Minimum required series length (default: context_length + prediction_length)
        filter_outliers : bool
            Whether to filter outliers (default: True)
        outlier_threshold : float
            Threshold for outlier detection in std devs (default: 5.0)
        apply_filtering : bool
            Whether to apply DAS-specific filtering (low-pass 10Hz, high-pass 0.5Hz) (default: False)
        """
        
        # Validate inputs
        if data_path is None and data_array is None:
            raise ValueError("Must provide either data_path or data_array")
        if data_path is not None and data_array is not None:
            raise ValueError("Cannot provide both data_path and data_array")
            
        self.context_length = context_length
        self.prediction_length = prediction_length
        self.stride = stride
        self.normalize = normalize
        self.normalization_method = normalization_method
        self.filter_outliers = filter_outliers
        self.outlier_threshold = outlier_threshold
        self.apply_filtering = apply_filtering
        
        if min_series_length is None:
            self.min_series_length = context_length + prediction_length
        else:
            self.min_series_length = max(min_series_length, context_length + prediction_length)
        
        # Load data
        if data_path is not None:
            self.data = self._load_das_hdf5(data_path)
        else:
            self.data = np.array(data_array, dtype=np.float32)
            
        if self.data.ndim != 2:
            raise ValueError("Data must be 2D array with shape (n_sensors, n_timesteps)")
            
        # Select sensors
        if sensor_indices is not None:
            self.data = self.data[sensor_indices, :]
            
        n_sensors, n_timesteps = self.data.shape
        print(f"Loaded data: {n_sensors} sensors, {n_timesteps} timesteps")
        
        # Filter by minimum length
        if n_timesteps < self.min_series_length:
            raise ValueError(f"Time series too short: {n_timesteps} < {self.min_series_length}")
            
        # Preprocessing
        self.normalization_stats = {}
        self._preprocess_data()
        
        # Create windows
        self._create_windows()
        
        print(f"Created {len(self.windows)} windows for training")
    
    def _load_das_hdf5(self, data_path: Union[str, Path]) -> np.ndarray:
        """Load DAS data from HDF5 file using the correct Valencia DAS format."""
        data_path = Path(data_path)
        if not data_path.exists():
            raise FileNotFoundError(f"HDF5 file not found: {data_path}")

        with h5py.File(data_path, 'r') as f:
            # Get DAS device name (first group)
            das_name = list(f.keys())[0]
            print(f"Loading DAS device: {das_name}")

            # Read attributes for proper data extraction
            source1_path = f"{das_name}/Source1/"
            zone_path = f"{source1_path}Zone1/"  # Default to Zone1

            # Get dataset path
            sr_dataset_path = f"{zone_path}SR_Valencia"
            if sr_dataset_path not in f:
                raise ValueError(f"Could not find strain rate data at {sr_dataset_path}")

            sr_dataset = f[sr_dataset_path]
            dims = sr_dataset.shape
            print(f"Dataset shape: {dims}")

            nb_block = dims[0]      # Number of blocks
            block_time_size = dims[1]  # Time samples per block
            block_space_size = dims[2]  # Spatial samples

            # Read metadata
            try:
                frequency = float(f[source1_path].attrs['PulseRateFreq'])  # Hz
                freq_res = float(f[source1_path].attrs['FreqRes']) / 250  # Hz
                spacing = f[zone_path].attrs['Spacing']
                print(f"Sampling frequency: {frequency} Hz, Freq resolution: {freq_res} Hz")
                print(f"Spatial spacing: {spacing[0]} m, Temporal spacing: {spacing[1]} ms")
            except KeyError as e:
                print(f"Warning: Could not read metadata: {e}")
                frequency = 250  # Default sampling frequency

            # Extract data properly following the original read_DAS.py logic
            print(f"Extracting {nb_block} blocks...")

            # Use only first 250 samples per block (as in original code)
            sampling_frequency = min(250, block_time_size)
            strain_rate_list = []

            for tt in range(nb_block):
                if tt % 50 == 0:  # Progress indicator
                    print(f"  Processing block {tt+1}/{nb_block}")

                # Read data block - shape will be (sampling_frequency, block_space_size)
                strain_rate_block = sr_dataset[tt, :sampling_frequency, :]

                # Transpose to get (space, time) format for concatenation
                strain_rate_list.append(strain_rate_block.T)

            # Concatenate all blocks along time axis
            # Result shape: (space/sensors, total_time)
            strain_rate = np.concatenate(strain_rate_list, axis=1)
            print(f"Concatenated data shape: {strain_rate.shape}")

            # Apply basic filtering if the data seems to need it
            # (Optional - can be disabled for raw data analysis)
            if hasattr(self, 'apply_filtering') and self.apply_filtering:
                print("Applying filters...")
                strain_rate = self._apply_das_filters(strain_rate, sampling_frequency)

        return np.array(strain_rate, dtype=np.float32)

    def _apply_das_filters(self, data: np.ndarray, sampling_frequency: float = 250) -> np.ndarray:
        """Apply DAS-specific filtering: low-pass (10 Hz) then high-pass (0.5 Hz)."""
        try:
            from scipy.signal import butter, filtfilt
        except ImportError:
            warnings.warn("scipy not available, skipping filtering")
            return data

        print("Applying low-pass (10 Hz) and high-pass (0.5 Hz) filters...")
        filtered_data = data.copy()

        # Apply filters to each sensor
        for ii in range(filtered_data.shape[0]):
            if ii % 50 == 0:  # Progress indicator
                print(f"  Filtering sensor {ii+1}/{filtered_data.shape[0]}")

            # Low-pass filter (10 Hz)
            nyquist = sampling_frequency / 2
            normalized_fc = 10 / nyquist
            b, a = butter(4, normalized_fc, btype='low')
            filtered_data[ii, :] = filtfilt(b, a, filtered_data[ii, :].astype(float))

            # High-pass filter (0.5 Hz)
            normalized_fc = 0.5 / nyquist
            b, a = butter(4, normalized_fc, btype='high')
            filtered_data[ii, :] = filtfilt(b, a, filtered_data[ii, :].astype(float))

        return filtered_data

    def _preprocess_data(self):
        """Preprocess the DAS data."""
        # Handle NaN/inf values
        if np.any(~np.isfinite(self.data)):
            warnings.warn("Found non-finite values in data, replacing with 0")
            self.data = np.where(np.isfinite(self.data), self.data, 0)
        
        # Filter outliers if requested
        if self.filter_outliers:
            self._filter_outliers()
        
        # Normalize data if requested
        if self.normalize:
            self._normalize_data()
    
    def _filter_outliers(self):
        """Filter outliers using z-score method."""
        for i in range(self.data.shape[0]):
            series = self.data[i, :]
            mean_val = np.mean(series)
            std_val = np.std(series)
            
            if std_val > 0:
                z_scores = np.abs((series - mean_val) / std_val)
                outlier_mask = z_scores > self.outlier_threshold
                
                if np.any(outlier_mask):
                    # Replace outliers with median value
                    median_val = np.median(series[~outlier_mask]) if np.any(~outlier_mask) else mean_val
                    self.data[i, outlier_mask] = median_val
    
    def _normalize_data(self):
        """Normalize data using specified method."""
        n_sensors = self.data.shape[0]
        
        for i in range(n_sensors):
            series = self.data[i, :]
            
            if self.normalization_method == 'z_score':
                mean_val = np.mean(series)
                std_val = np.std(series)
                if std_val > 0:
                    self.data[i, :] = (series - mean_val) / std_val
                    self.normalization_stats[i] = {'mean': mean_val, 'std': std_val}
                else:
                    self.normalization_stats[i] = {'mean': mean_val, 'std': 1.0}
                    
            elif self.normalization_method == 'min_max':
                min_val = np.min(series)
                max_val = np.max(series)
                if max_val > min_val:
                    self.data[i, :] = (series - min_val) / (max_val - min_val)
                    self.normalization_stats[i] = {'min': min_val, 'max': max_val}
                else:
                    self.normalization_stats[i] = {'min': min_val, 'max': min_val + 1.0}
                    
            elif self.normalization_method == 'robust':
                median_val = np.median(series)
                mad = np.median(np.abs(series - median_val))
                if mad > 0:
                    self.data[i, :] = (series - median_val) / mad
                    self.normalization_stats[i] = {'median': median_val, 'mad': mad}
                else:
                    self.normalization_stats[i] = {'median': median_val, 'mad': 1.0}
    
    def _create_windows(self):
        """Create sliding windows for time series forecasting."""
        self.windows = []
        n_sensors, n_timesteps = self.data.shape
        
        window_size = self.context_length + self.prediction_length
        
        for sensor_idx in range(n_sensors):
            series = self.data[sensor_idx, :]
            
            # Create sliding windows with specified stride
            for start_idx in range(0, n_timesteps - window_size + 1, self.stride):
                end_idx = start_idx + window_size
                
                context = series[start_idx:start_idx + self.context_length]
                target = series[start_idx + self.context_length:end_idx]
                
                self.windows.append({
                    'sensor_idx': sensor_idx,
                    'context': context.copy(),
                    'target': target.copy(),
                    'start_idx': start_idx
                })
    
    def __len__(self) -> int:
        """Return number of windows in the dataset."""
        return len(self.windows)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a single sample from the dataset.
        
        Returns:
        --------
        Dict with keys:
        - 'past_values': Context time series [context_length]
        - 'future_values': Target time series [prediction_length] 
        - 'sensor_idx': Sensor index
        """
        window = self.windows[idx]
        
        return {
            'past_values': torch.tensor(window['context'], dtype=torch.float32),
            'future_values': torch.tensor(window['target'], dtype=torch.float32),
            'sensor_idx': torch.tensor(window['sensor_idx'], dtype=torch.long)
        }
    
    def get_normalization_stats(self) -> Dict:
        """Return normalization statistics for denormalization."""
        return self.normalization_stats
    
    def denormalize(self, data: torch.Tensor, sensor_idx: int) -> torch.Tensor:
        """Denormalize data back to original scale."""
        if not self.normalize or sensor_idx not in self.normalization_stats:
            return data
            
        stats = self.normalization_stats[sensor_idx]
        
        if self.normalization_method == 'z_score':
            return data * stats['std'] + stats['mean']
        elif self.normalization_method == 'min_max':
            return data * (stats['max'] - stats['min']) + stats['min']
        elif self.normalization_method == 'robust':
            return data * stats['mad'] + stats['median']
            
        return data


class DASChronosDataLoader:
    """
    Wrapper class for creating DataLoader with optimal settings for Chronos T5.
    """
    
    @staticmethod
    def create_train_loader(dataset: DASChronosDataset, 
                          batch_size: int = 32,
                          shuffle: bool = True,
                          num_workers: int = 4,
                          pin_memory: bool = True) -> DataLoader:
        """Create training DataLoader."""
        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=pin_memory,
            drop_last=True  # Ensure consistent batch sizes
        )
    
    @staticmethod
    def create_val_loader(dataset: DASChronosDataset,
                         batch_size: int = 64,
                         num_workers: int = 4,
                         pin_memory: bool = True) -> DataLoader:
        """Create validation DataLoader."""
        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=pin_memory,
            drop_last=False
        )


# Example usage and utility functions
def collate_fn_chronos(batch: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
    """
    Custom collate function for Chronos T5 that handles batching properly.
    """
    past_values = torch.stack([item['past_values'] for item in batch])
    future_values = torch.stack([item['future_values'] for item in batch])
    sensor_indices = torch.stack([item['sensor_idx'] for item in batch])
    
    return {
        'past_values': past_values,
        'future_values': future_values,
        'sensor_idx': sensor_indices
    }


def create_chronos_dataloaders(data_path: Optional[str] = None,
                              data_array: Optional[np.ndarray] = None,
                              context_length: int = 512,
                              prediction_length: int = 64,
                              train_split: float = 0.8,
                              batch_size: int = 32,
                              **dataset_kwargs) -> Tuple[DataLoader, DataLoader]:
    """
    Create train and validation DataLoaders for Chronos T5.
    
    Parameters:
    -----------
    data_path : str, optional
        Path to HDF5 file
    data_array : np.ndarray, optional
        Pre-loaded data array
    context_length : int
        Context window length
    prediction_length : int
        Prediction horizon length
    train_split : float
        Fraction of data to use for training
    batch_size : int
        Batch size for training
    **dataset_kwargs : dict
        Additional arguments for DASChronosDataset
        
    Returns:
    --------
    train_loader, val_loader : Tuple[DataLoader, DataLoader]
    """
    
    # Create full dataset
    full_dataset = DASChronosDataset(
        data_path=data_path,
        data_array=data_array,
        context_length=context_length,
        prediction_length=prediction_length,
        **dataset_kwargs
    )
    
    # Split dataset
    dataset_size = len(full_dataset)
    train_size = int(train_split * dataset_size)
    val_size = dataset_size - train_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(
        full_dataset, [train_size, val_size]
    )
    
    # Create DataLoaders
    train_loader = DASChronosDataLoader.create_train_loader(
        train_dataset, batch_size=batch_size
    )
    
    val_loader = DASChronosDataLoader.create_val_loader(
        val_dataset, batch_size=batch_size * 2  # Larger batch size for validation
    )
    
    return train_loader, val_loader


# Example usage
def example_usage():
    """Example of how to use the DAS Chronos DataLoader."""
    
    print("Example: Creating DAS DataLoader for Chronos T5")
    
    # Option 1: Load from HDF5 file
    try:
        dataset = DASChronosDataset(
            data_path="path_to_your_das_file.h5",
            context_length=512,
            prediction_length=64,
            stride=32,  # Overlapping windows
            normalize=True,
            normalization_method='z_score'
        )
        
        # Create DataLoaders
        train_loader, val_loader = create_chronos_dataloaders(
            data_path="path_to_your_das_file.h5",
            context_length=512,
            prediction_length=64,
            batch_size=16
        )
        
        print(f"Train batches: {len(train_loader)}")
        print(f"Val batches: {len(val_loader)}")
        
        # Test a batch
        for batch in train_loader:
            print(f"Batch shapes:")
            print(f"  Past values: {batch['past_values'].shape}")
            print(f"  Future values: {batch['future_values'].shape}")
            print(f"  Sensor indices: {batch['sensor_idx'].shape}")
            break
            
    except FileNotFoundError:
        print("HDF5 file not found - creating synthetic example instead")
        
        # Option 2: Use synthetic data
        # Generate synthetic DAS-like data
        n_sensors = 100
        n_timesteps = 10000
        synthetic_data = np.random.randn(n_sensors, n_timesteps).astype(np.float32)
        
        # Add some structure to make it more realistic
        for i in range(n_sensors):
            # Add trend and seasonality
            t = np.arange(n_timesteps)
            trend = 0.001 * t
            seasonality = 2 * np.sin(2 * np.pi * t / 1000)
            synthetic_data[i, :] += trend + seasonality
        
        dataset = DASChronosDataset(
            data_array=synthetic_data,
            context_length=512,
            prediction_length=64,
            stride=64,
            normalize=True
        )
        
        train_loader = DASChronosDataLoader.create_train_loader(dataset, batch_size=8)
        
        print(f"Synthetic dataset created: {len(dataset)} samples")
        print(f"Train loader: {len(train_loader)} batches")
        
        # Test batch
        for batch in train_loader:
            print(f"Batch shapes:")
            print(f"  Past values: {batch['past_values'].shape}")
            print(f"  Future values: {batch['future_values'].shape}")
            break


if __name__ == "__main__":
    example_usage()
