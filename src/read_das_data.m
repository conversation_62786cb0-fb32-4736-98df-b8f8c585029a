%%% Extraction Strain rate from HDF5
%%% Version Han 202203
%%% 
clc
clear
%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%% Parametring %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% File %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

pathname = './'; %To fill

filename = 'SR_Valencia_2020-09-02_21-32-00_UTC_10mins';

disp(['/// ' filename ' \\\'])
Zone_to_Plot = 1; % Put 1 if only one zone otherwise choose the zone

%%%%%%%%%%%%%%%%%%%%%%%%%%%% Time option %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
Section_time = 'yes'; % Put yes if choosing a section of time
From_Time = 1; %s
To_Time = 300; %s

%%%%%%%%%%%%%%%%%%%%%%%%%%%% Space option %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
Section_space = 'no'; % Put yes if choosing a section of space
From_Position = 1.00; %Km
To_Position = 2.0; %Km

%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%% Initialization %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

disp(' ')
disp('> Initialization')
disp([pathname filename '.h5'])
fid = H5F.open([pathname filename '.h5']);
info_HDF5 = h5info([pathname filename '.h5'],'/');
DAS_name = info_HDF5.Groups.Name; % Name of the DAS unit
disp([info_HDF5])
Frequency = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/'],'PulseRateFreq')); %Hz - Pulse periode
Freq_res = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/'],'FreqRes'))/250; %Hz - Pulse periode
Acquisition_length = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/'],'FiberLength')); %m - Distance of fiber for acquistion
Sampling_Res = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/'],'SamplingRes')); %cm - Sampling resolution
Origin = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/'],'Origin'));
Spacing = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/'],'Spacing'));
Extent = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/'],'Extent'));
ZI_start = Origin(1)+(Extent(1))*Spacing(1); %m - Start distance of the zone
ZI_end = Origin(1)+(Extent(2))*Spacing(1); %m - End distance of the zone
Distance_fiber = ZI_start:Spacing(1):ZI_end; %m - Distance vector of the fiber 
clear Origin, clear Extent, clear info_HDF5
dset_id_1 = H5D.open(fid,[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/SR_Valencia']);
space_id_1 = H5D.get_space(dset_id_1);
[ndims,dims] = H5S.get_simple_extent_dims(space_id_1); %Get the data dimension
nb_Block = dims(1); %Number of blocks
Block_Time_size = dims(2); %Time size
Block_Space_size = dims(3); %Distance size
Time_0 = double(h5read([pathname filename '.h5'],[DAS_name '/Source1/time'])); %s - Time stamp of each blocks
disp(['     Device : ' DAS_name])
disp(['     Fiber length : ' num2str(Acquisition_length) ' m'])
disp(['     Zone of interest : ' num2str(Zone_to_Plot) ' from ' num2str(ZI_start) ' m to ' num2str(ZI_end) ' m'])
disp(['     Total time of acquisition : ' num2str((nb_Block)*Freq_res) ' s '])
disp(['     Spatial resolution : ' num2str(Spacing(1)) ' m'])
disp(['     Temporal resolution : ' num2str(Spacing(2)) ' ms'])
disp(['     PRF original : ' num2str(Frequency) ' Hz'])
disp(['     Sampling resolution original : ' num2str(Sampling_Res) ' cm'])
clear ZI_start, clear ZI_end, clear Sampling_Res, clear Frequency, clear Acquisition_length 

%%% Initialize blocks and index limits
if strcmp(Section_time,'no') == 1
    First_Block = 0; 
    Last_Block = nb_Block-1;
    First_record = Block_Time_size/4;
    Last_record = 3*Block_Time_size/4; 
    Total_time_size = nb_Block*Block_Time_size/2;  
    Total_time = (0:(Spacing(2)*1e-3):(Total_time_size-1)*(Spacing(2)*1e-3));
else
    Freq_res
    From_Time
    First_Block = fix(From_Time*Freq_res);
    Last_Block = fix(To_Time*Freq_res); 
    First_record = Block_Time_size/4+(From_Time-First_Block)/(Spacing(2)*1e-3);
    Last_record = Block_Time_size/4+(To_Time-Last_Block)/(Spacing(2)*1e-3); 
    Total_time_size = 3*Block_Time_size/4-First_record + ((Last_Block-1)-(First_Block))*Block_Time_size/2 + Last_record-Block_Time_size/4;
    Total_time = (0:((Spacing(2)*1e-3)):(Total_time_size-1)*((Spacing(2)*1e-3)))+From_Time;
end
if strcmp(Section_space,'no') == 1
    First_point = 0;
    Last_point = Block_Space_size;
    Total_space_size = Block_Space_size;
else
    First_point = find(abs(Distance_fiber-From_Position*1000) == min(abs(Distance_fiber-From_Position*1000)))-1;
    Last_point = find(abs(Distance_fiber-To_Position*1000) == min(abs(Distance_fiber-To_Position*1000)))-1;
    First_point = First_point(1);
    Last_point = Last_point(1);
    Total_space_size = (Last_point-First_point)+1;
    Distance_fiber = Distance_fiber(First_point:Last_point);
end

%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%% Extraction and concatenation %%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

disp(' ')
disp('> Data extraction')
Frequency=250;

for tt = First_Block:Last_Block
    disp(['     ' num2str(tt-First_Block+1) '/' num2str(Last_Block-First_Block+1) ' blocks'])

        if tt == First_Block
StrainRate_0=h5read([pathname filename,'.h5'],[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/SR_Valencia'],[First_point+1, 1, tt+1],...
[Last_point-First_point,Frequency,1],[1,1,1]);
StrainRate = StrainRate_0;
        elseif tt<Last_Block
StrainRate_0=h5read([pathname filename,'.h5'],[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/SR_Valencia'],[First_point+1, 1, tt+1],...
[Last_point-First_point,Frequency,1],[1,1,1]);
StrainRate(:,size(StrainRate,2)+1:size(StrainRate,2)+size(StrainRate_0,2)) = StrainRate_0;

end
end

for ii=1:size(StrainRate,1)

StrainRate(ii,:) = filter1('lp',double(StrainRate(ii,:)),'fs',250,'fc',10);

end

for ii=1:size(StrainRate,1)

StrainRate(ii,:) = filter1('hp',double(StrainRate(ii,:)),'fs',250,'fc',0.5);

end
%%           
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Plotting %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

figure;
imagesc([1:size(StrainRate,2)]/Frequency,[1:size(StrainRate,1)]*Spacing(1),StrainRate)
colormap(jet)
xlabel('Time (s)'), ylabel('Distance (m)')
title('Strain rate (0.5-10 Hz)')
set(gca,'fontsize',15)
caxis([-20 20])


H5S.close(space_id_1);
H5D.close(dset_id_1);
H5F.close(fid);   
clear Block_Space_size, clear Block_Time_size, clear First_Block, clear Last_Block, clear First_record, clear Last_record 
clear block, clear nb_Block, clear space_id_1, clear dset_id_1, clear fid, clear mem_space_id, clear dims, clear ndims, clear DAS_name
clear tt, clear offset, clear From_Time, clear To_Time, clear Section_time, clear Total_time_size
clear Total_space_size, clear First_point, clear Last_point, clear From_Position, clear To_Position, clear Section_space, clear Zone_to_Plot
     

