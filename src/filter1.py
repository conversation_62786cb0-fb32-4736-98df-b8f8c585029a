#!/usr/bin/env python3
"""
filter1 performs frequency or wavelength filtering on a 1D array 
using zero-phase Butterworth filtering.

Translated from MATLAB version by <PERSON> (UTIG, October 2015)
Python version 
"""

import numpy as np
from scipy.signal import butter, filtfilt
from typing import Union, Tuple, Optional, List

def filter1(filtertype: str, 
           y: np.ndarray, 
           fc: Optional[Union[float, List[float]]] = None,
           lambdac: Optional[Union[float, List[float]]] = None,
           fs: Optional[float] = None,
           x: Optional[np.ndarray] = None,
           Ts: Optional[float] = None,
           order: int = 1) -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray, np.ndarray]]:
    """
    Performs frequency or wavelength filtering on a 1D array using zero-phase Butterworth filtering.
    
    Parameters:
    -----------
    filtertype : str
        Type of filter. Options:
        - 'hp' or 'high': high-pass with scalar cutoff frequency
        - 'lp' or 'low': low-pass with scalar cutoff frequency  
        - 'bp' or 'bandpass': band-pass with two-element cutoff frequencies
        - 'bs' or 'stop': band-stop with two-element cutoff frequencies
        
    y : array_like
        Input 1D signal to be filtered
        
    fc : float or list of floats, optional
        Cutoff frequency(ies). For hp/lp filters, must be scalar. 
        For bp/bs filters, must be two-element list [low_freq, high_freq]
        
    lambdac : float or list of floats, optional
        Cutoff wavelength(s). Alternative to fc where lambda = 1/f.
        
    fs : float, optional
        Sampling frequency. Default is 1.0 if no sampling info provided.
        
    x : array_like, optional
        Vector of monotonically-increasing, equally-spaced sampling times
        or locations corresponding to y. Used to determine sampling frequency.
        
    Ts : float, optional
        Sampling period such that fs = 1/Ts
        
    order : int, optional
        Order of the Butterworth filter. Default is 1.
        
    Returns:
    --------
    yfilt : ndarray
        Filtered signal
        
    filtb : ndarray (optional)
        Filter numerator coefficients (if requested)
        
    filta : ndarray (optional)  
        Filter denominator coefficients (if requested)
        
    Examples:
    ---------
    >>> import numpy as np
    >>> from scipy.io import wavfile
    
    # Create a test signal with noise
    >>> t = np.linspace(0, 1, 1000)
    >>> signal = np.sin(2*np.pi*10*t) + 0.5*np.sin(2*np.pi*50*t) + 0.1*np.random.randn(1000)
    
    # High-pass filter keeping frequencies above 20 Hz
    >>> signal_hp = filter1('hp', signal, fc=20, fs=1000)
    
    # Low-pass filter keeping frequencies below 30 Hz with 5th order rolloff
    >>> signal_lp = filter1('lp', signal, fc=30, x=t, order=5)
    
    # Band-stop filter from 40-60 Hz
    >>> signal_bs = filter1('bs', signal, fc=[40, 60], fs=1000, order=3)
    """
    
    # Initial error checks
    if not isinstance(y, (list, np.ndarray)):
        raise ValueError("Input y must be a vector/array")
    
    y = np.asarray(y)
    if y.ndim != 1:
        raise ValueError("Input y must be a 1D array")
    
    # Check that exactly one of fc or lambdac is specified
    if (fc is None) == (lambdac is None):
        raise ValueError("Must specify exactly one of 'fc' or 'lambdac'")
    
    # Check sampling rate specification (only one allowed)
    sampling_specs = sum([x is not None for x in [fs, x, Ts]])
    if sampling_specs > 1:
        raise ValueError("Cannot specify more than one of: fs, x, Ts")
    
    # Parse filter type
    filtertype_map = {
        'hp': 'high',
        'lp': 'low', 
        'bp': 'bandpass',
        'bs': 'stop',
        'high': 'high',
        'low': 'low',
        'bandpass': 'bandpass',
        'stop': 'stop'
    }
    
    if filtertype.lower() not in filtertype_map:
        raise ValueError("Filter type must be 'hp', 'lp', 'bp', 'bs', 'high', 'low', 'bandpass', or 'stop'")
    
    filtertype_normalized = filtertype_map[filtertype.lower()]
    
    # Determine sampling frequency
    if fs is not None:
        Fs = float(fs)
    elif Ts is not None:
        Fs = 1.0 / float(Ts)
    elif x is not None:
        x = np.asarray(x)
        if x.ndim != 1:
            raise ValueError("Input x must be a 1D array")
        if len(x) != len(y):
            raise ValueError("Dimensions of x must match dimensions of y")
        
        # Check if x is equally spaced
        dx = np.diff(x)
        if not np.allclose(dx, dx[0], rtol=1e-10):
            raise ValueError("Input vector x must be equally spaced")
        
        Ts = dx[0]
        Fs = 1.0 / Ts
    else:
        Fs = 1.0  # Default sampling frequency
    
    # Determine cutoff frequencies
    if fc is not None:
        cutoff_freqs = np.asarray(fc)
    else:  # lambdac is specified
        lambdac = np.asarray(lambdac)
        cutoff_freqs = 1.0 / lambdac
    
    # Validate cutoff frequencies based on filter type
    if filtertype_normalized in ['low', 'high']:
        if not np.isscalar(cutoff_freqs):
            raise ValueError("Low-pass and high-pass filters require a scalar cutoff frequency")
    elif filtertype_normalized in ['stop', 'bandpass']:
        if cutoff_freqs.size != 2:
            raise ValueError("Bandpass and bandstop filters require exactly two cutoff frequencies")
        cutoff_freqs = np.sort(cutoff_freqs)
    
    # Validate inputs
    if not np.isscalar(Fs) or not np.isfinite(Fs) or Fs <= 0:
        raise ValueError("Sampling frequency must be a positive scalar")
    
    if not isinstance(order, int) or order < 1:
        raise ValueError("Filter order must be a positive integer")
    
    # Check if cutoff frequencies are valid
    nyquist_freq = Fs / 2
    if np.any(cutoff_freqs >= nyquist_freq):
        raise ValueError(f"Cutoff frequency must be less than Nyquist frequency ({nyquist_freq} Hz)")
    if np.any(cutoff_freqs <= 0):
        raise ValueError("Cutoff frequency must be positive")
    
    # Construct filter
    Wn = cutoff_freqs / nyquist_freq  # Normalized frequency
    
    try:
        filtb, filta = butter(order, Wn, btype=filtertype_normalized)
    except Exception as e:
        raise ValueError(f"Filter design failed: {str(e)}")
    
    # Apply zero-phase filtering
    yfilt = filtfilt(filtb, filta, y)
    
    return yfilt, filtb, filta


def filter1_simple(filtertype: str, 
                  y: np.ndarray, 
                  fc: Union[float, List[float]] = None,
                  fs: float = 250,
                  order: int = 4) -> np.ndarray:
    """
    Simplified version of filter1 for basic filtering tasks.
    
    Parameters:
    -----------
    filtertype : str
        'lp', 'hp', 'bp', or 'bs'
    y : array_like
        Input signal
    fc : float or list
        Cutoff frequency(ies) 
    fs : float
        Sampling frequency (default: 250)
    order : int
        Filter order (default: 4)
        
    Returns:
    --------
    yfilt : ndarray
        Filtered signal
    """
    yfilt, _, _ = filter1(filtertype, y, fc=fc, fs=fs, order=order)
    return yfilt


# Example usage and test function
def test_filter1():
    """Test function demonstrating filter1 usage"""
    
    print("Testing filter1 function...")
    
    # Create test signal: 10 Hz + 50 Hz sine waves + noise
    fs = 1000  # Sampling frequency
    t = np.linspace(0, 2, 2*fs, endpoint=False)
    signal = (np.sin(2*np.pi*10*t) + 
              0.5*np.sin(2*np.pi*50*t) + 
              0.1*np.random.randn(len(t)))
    
    print(f"Original signal: {len(signal)} samples at {fs} Hz")
    
    # Test high-pass filter
    signal_hp = filter1('hp', signal, fc=20, fs=fs, order=4)[0]
    print("✓ High-pass filter (fc=20 Hz) applied")
    
    # Test low-pass filter  
    signal_lp = filter1('lp', signal, fc=30, fs=fs, order=4)[0]
    print("✓ Low-pass filter (fc=30 Hz) applied")
    
    # Test band-pass filter
    signal_bp = filter1('bp', signal, fc=[5, 15], fs=fs, order=2)[0]
    print("✓ Band-pass filter (fc=5-15 Hz) applied")
    
    # Test band-stop filter
    signal_bs = filter1('bs', signal, fc=[45, 55], fs=fs, order=3)[0]
    print("✓ Band-stop filter (fc=45-55 Hz) applied")
    
    # Test with time vector instead of sampling frequency
    signal_t = filter1('lp', signal, fc=25, x=t, order=5)[0]
    print("✓ Low-pass filter using time vector applied")
    
    # Test wavelength specification
    signal_lambda = filter1('hp', signal, lambdac=1/20, fs=fs)[0]
    print("✓ High-pass filter using wavelength specification applied")
    
    print("\nAll tests passed! ✅")
    
    return signal, signal_hp, signal_lp, signal_bp, signal_bs


if __name__ == "__main__":
    # Run tests
    test_results = test_filter1()
    print("\nfilter1 function is ready to use!")
