#!/usr/bin/env python3
"""
Extraction Strain rate from HDF5
Translated from MATLAB version Han 202203
Python version 
"""

import numpy as np
import h5py
import matplotlib.pyplot as plt
from scipy.signal import butter, filtfilt
import os

def apply_filter(data, fs, filter_type, fc):
    """
    Apply butterworth filter to data
    
    Parameters:
    data: input signal
    fs: sampling frequency
    filter_type: 'lp' for lowpass, 'hp' for highpass
    fc: cutoff frequency
    """
    nyquist = fs / 2
    normalized_fc = fc / nyquist
    
    if filter_type == 'lp':
        b, a = butter(4, normalized_fc, btype='low')
    elif filter_type == 'hp':
        b, a = butter(4, normalized_fc, btype='high')
    else:
        raise ValueError("filter_type must be 'lp' or 'hp'")
    
    return filtfilt(b, a, data)

def read_das_data():
    """Main function to read and process DAS data"""
    
    print("DAS Data Reader - Python Version")
    
    ###########################################################################
    ############################# Parameters #################################
    ###########################################################################
    
    ################################ File ####################################
    pathname = './'  # To fill
    filename = 'SR_Valencia_2020-09-02_21-32-00_UTC_10mins'
    
    print(f'/// {filename} \\\\\\')
    zone_to_plot = 1  # Put 1 if only one zone otherwise choose the zone
    
    ############################# Time option ################################
    section_time = 'yes'  # Put yes if choosing a section of time
    from_time = 1  # s
    to_time = 300  # s
    
    ############################ Space option ################################
    section_space = 'no'  # Put yes if choosing a section of space
    from_position = 1.00  # Km
    to_position = 2.0  # Km
    
    ###########################################################################
    ########################### Initialization ###############################
    ###########################################################################
    
    print('\n> Initialization')
    filepath = os.path.join(pathname, filename + '.h5')
    print(filepath)
    
    with h5py.File(filepath, 'r') as f:
        # Get DAS name (first group)
        das_name = list(f.keys())[0]
        print(f"Device: {das_name}")
        
        # Read attributes
        source1_path = f"{das_name}/Source1/"
        frequency = float(f[source1_path].attrs['PulseRateFreq'])  # Hz
        freq_res = float(f[source1_path].attrs['FreqRes']) / 250  # Hz
        acquisition_length = float(f[source1_path].attrs['FiberLength'])  # m
        sampling_res = float(f[source1_path].attrs['SamplingRes'])  # cm
        
        zone_path = f"{source1_path}Zone{zone_to_plot}/"
        origin = f[zone_path].attrs['Origin']
        spacing = f[zone_path].attrs['Spacing']
        extent = f[zone_path].attrs['Extent']
        
        zi_start = origin[0] + extent[0] * spacing[0]  # m
        zi_end = origin[0] + extent[1] * spacing[0]  # m
        distance_fiber = np.arange(zi_start, zi_end + spacing[0], spacing[0])  # m
        
        # Get dataset dimensions
        sr_dataset_path = f"{zone_path}SR_Valencia"
        sr_dataset = f[sr_dataset_path]
        dims = sr_dataset.shape
        
        nb_block = dims[0]  # Number of blocks
        block_time_size = dims[1]  # Time size
        block_space_size = dims[2]  # Distance size
        
        # Read time stamps
        time_0 = f[f"{source1_path}time"][:]  # s
        
        print(f'     Device : {das_name}')
        print(f'     Fiber length : {acquisition_length} m')
        print(f'     Zone of interest : {zone_to_plot} from {zi_start} m to {zi_end} m')
        print(f'     Total time of acquisition : {nb_block * freq_res} s')
        print(f'     Spatial resolution : {spacing[0]} m')
        print(f'     Temporal resolution : {spacing[1]} ms')
        print(f'     PRF original : {frequency} Hz')
        print(f'     Sampling resolution original : {sampling_res} cm')
        
        # Initialize blocks and index limits
        if section_time == 'no':
            first_block = 0
            last_block = nb_block - 1
            first_record = block_time_size // 4
            last_record = 3 * block_time_size // 4
            total_time_size = nb_block * block_time_size // 2
            total_time = np.arange(0, total_time_size) * (spacing[1] * 1e-3)
        else:
            print(f'Freq_res: {freq_res}')
            print(f'From_Time: {from_time}')
            first_block = int(from_time * freq_res)
            last_block = int(to_time * freq_res)
            first_record = block_time_size // 4 + (from_time - first_block) / (spacing[1] * 1e-3)
            last_record = block_time_size // 4 + (to_time - last_block) / (spacing[1] * 1e-3)
            total_time_size = (3 * block_time_size // 4 - first_record + 
                             ((last_block - 1) - first_block) * block_time_size // 2 + 
                             last_record - block_time_size // 4)
            total_time = np.arange(0, total_time_size) * (spacing[1] * 1e-3) + from_time
        
        if section_space == 'no':
            first_point = 0
            last_point = block_space_size
            total_space_size = block_space_size
        else:
            first_point = np.argmin(np.abs(distance_fiber - from_position * 1000))
            last_point = np.argmin(np.abs(distance_fiber - to_position * 1000))
            total_space_size = (last_point - first_point) + 1
            distance_fiber = distance_fiber[first_point:last_point + 1]
        
        ###########################################################################
        ################### Extraction and concatenation #########################
        ###########################################################################
        
        print('\n> Data extraction')
        sampling_frequency = 250
        
        strain_rate_list = []
        
        for tt in range(first_block, last_block + 1):
            print(f'     {tt - first_block + 1}/{last_block - first_block + 1} blocks')
            
            # Read data block
            strain_rate_block = sr_dataset[tt, :sampling_frequency, first_point:last_point]
            strain_rate_list.append(strain_rate_block.T)  # Transpose to match MATLAB indexing
        
        # Concatenate all blocks
        strain_rate = np.concatenate(strain_rate_list, axis=1)
        
        print('\n> Applying filters')
        # Apply low-pass filter (10 Hz) then high-pass filter (0.5 Hz)
        for ii in range(strain_rate.shape[0]):
            # Low-pass filter
            strain_rate[ii, :] = apply_filter(strain_rate[ii, :].astype(float), 
                                            sampling_frequency, 'lp', 10)
            # High-pass filter
            strain_rate[ii, :] = apply_filter(strain_rate[ii, :].astype(float), 
                                            sampling_frequency, 'hp', 0.5)
        
        ###########################################################################
        ############################# Plotting ###################################
        ###########################################################################
        
        print('\n> Creating plot')
        plt.figure(figsize=(12, 8))
        
        time_axis = np.arange(strain_rate.shape[1]) / sampling_frequency
        distance_axis = np.arange(strain_rate.shape[0]) * spacing[0]
        
        plt.imshow(strain_rate, 
                  extent=[time_axis[0], time_axis[-1], 
                         distance_axis[-1], distance_axis[0]],
                  aspect='auto', 
                  cmap='jet',
                  vmin=-20, vmax=20)
        
        plt.colorbar(label='Strain Rate')
        plt.xlabel('Time (s)', fontsize=15)
        plt.ylabel('Distance (m)', fontsize=15)
        plt.title('Strain rate (0.5-10 Hz)', fontsize=15)
        plt.tick_params(labelsize=15)
        
        plt.tight_layout()
        plt.show()
        
        print('\n> Processing complete!')
        
        return strain_rate, time_axis, distance_axis

if __name__ == "__main__":
    try:
        strain_rate_data, time_axis, distance_axis = read_das_data()
        print("Data processing completed successfully!")
    except FileNotFoundError:
        print("Error: HDF5 file not found. Please check the file path and name.")
    except Exception as e:
        print(f"An error occurred: {str(e)}")