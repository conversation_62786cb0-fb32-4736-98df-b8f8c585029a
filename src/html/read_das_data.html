
<!DOCTYPE html
  PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html><head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
   <!--
This HTML was auto-generated from MATLAB code.
To make changes, update the MATLAB code and republish this document.
      --><title>read_das_data</title><meta name="generator" content="MATLAB 9.5"><link rel="schema.DC" href="http://purl.org/dc/elements/1.1/"><meta name="DC.date" content="2022-03-16"><meta name="DC.source" content="read_das_data.m"><style type="text/css">
html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,font,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td{margin:0;padding:0;border:0;outline:0;font-size:100%;vertical-align:baseline;background:transparent}body{line-height:1}ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none}:focus{outine:0}ins{text-decoration:none}del{text-decoration:line-through}table{border-collapse:collapse;border-spacing:0}

html { min-height:100%; margin-bottom:1px; }
html body { height:100%; margin:0px; font-family:Arial, Helvetica, sans-serif; font-size:10px; color:#000; line-height:140%; background:#fff none; overflow-y:scroll; }
html body td { vertical-align:top; text-align:left; }

h1 { padding:0px; margin:0px 0px 25px; font-family:Arial, Helvetica, sans-serif; font-size:1.5em; color:#d55000; line-height:100%; font-weight:normal; }
h2 { padding:0px; margin:0px 0px 8px; font-family:Arial, Helvetica, sans-serif; font-size:1.2em; color:#000; font-weight:bold; line-height:140%; border-bottom:1px solid #d6d4d4; display:block; }
h3 { padding:0px; margin:0px 0px 5px; font-family:Arial, Helvetica, sans-serif; font-size:1.1em; color:#000; font-weight:bold; line-height:140%; }

a { color:#005fce; text-decoration:none; }
a:hover { color:#005fce; text-decoration:underline; }
a:visited { color:#004aa0; text-decoration:none; }

p { padding:0px; margin:0px 0px 20px; }
img { padding:0px; margin:0px 0px 20px; border:none; }
p img, pre img, tt img, li img, h1 img, h2 img { margin-bottom:0px; } 

ul { padding:0px; margin:0px 0px 20px 23px; list-style:square; }
ul li { padding:0px; margin:0px 0px 7px 0px; }
ul li ul { padding:5px 0px 0px; margin:0px 0px 7px 23px; }
ul li ol li { list-style:decimal; }
ol { padding:0px; margin:0px 0px 20px 0px; list-style:decimal; }
ol li { padding:0px; margin:0px 0px 7px 23px; list-style-type:decimal; }
ol li ol { padding:5px 0px 0px; margin:0px 0px 7px 0px; }
ol li ol li { list-style-type:lower-alpha; }
ol li ul { padding-top:7px; }
ol li ul li { list-style:square; }

.content { font-size:1.2em; line-height:140%; padding: 20px; }

pre, code { font-size:12px; }
tt { font-size: 1.2em; }
pre { margin:0px 0px 20px; }
pre.codeinput { padding:10px; border:1px solid #d3d3d3; background:#f7f7f7; }
pre.codeoutput { padding:10px 11px; margin:0px 0px 20px; color:#4c4c4c; }
pre.error { color:red; }

@media print { pre.codeinput, pre.codeoutput { word-wrap:break-word; width:100%; } }

span.keyword { color:#0000FF }
span.comment { color:#228B22 }
span.string { color:#A020F0 }
span.untermstring { color:#B20000 }
span.syscmd { color:#B28C00 }

.footer { width:auto; padding:10px 0px; margin:25px 0px 0px; border-top:1px dotted #878787; font-size:0.8em; line-height:140%; font-style:italic; color:#878787; text-align:left; float:none; }
.footer p { margin:0px; }
.footer a { color:#878787; }
.footer a:hover { color:#878787; text-decoration:underline; }
.footer a:visited { color:#878787; }

table th { padding:7px 5px; text-align:left; vertical-align:middle; border: 1px solid #d6d4d4; font-weight:bold; }
table td { padding:7px 5px; text-align:left; vertical-align:top; border:1px solid #d6d4d4; }





  </style></head><body><div class="content"><h1></h1><!--introduction--><!--/introduction--><h2>Contents</h2><div><ul><li><a href="#1">Extraction Strain rate from HDF5</a></li><li><a href="#2">Version Han 202203</a></li><li><a href="#6">Initialize blocks and index limits</a></li></ul></div><h2 id="1">Extraction Strain rate from HDF5</h2><h2 id="2">Version Han 202203</h2><pre class="codeinput">clc
clear
</pre><pre class="codeinput"><span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>
<span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%% Parametring %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>
<span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>

<span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% File %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>

pathname = <span class="string">'./'</span>; <span class="comment">%To fill</span>

filename = <span class="string">'SR_Valencia_2020-09-02_21-32-00_UTC_10mins'</span>;

disp([<span class="string">'/// '</span> filename <span class="string">' \\\'</span>])
Zone_to_Plot = 1; <span class="comment">% Put 1 if only one zone otherwise choose the zone</span>

<span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%% Time option %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>
Section_time = <span class="string">'yes'</span>; <span class="comment">% Put yes if choosing a section of time</span>
From_Time = 1; <span class="comment">%s</span>
To_Time = 300; <span class="comment">%s</span>

<span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%% Space option %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>
Section_space = <span class="string">'no'</span>; <span class="comment">% Put yes if choosing a section of space</span>
From_Position = 1.00; <span class="comment">%Km</span>
To_Position = 2.0; <span class="comment">%Km</span>
</pre><pre class="codeoutput">/// SR_Valencia_2020-09-02_21-32-00_UTC_10mins \\\
</pre><pre class="codeinput"><span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>
<span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%% Initialization %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>
<span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>

disp(<span class="string">' '</span>)
disp(<span class="string">'&gt; Initialization'</span>)
disp([pathname filename <span class="string">'.h5'</span>])
fid = H5F.open([pathname filename <span class="string">'.h5'</span>]);
info_HDF5 = h5info([pathname filename <span class="string">'.h5'</span>],<span class="string">'/'</span>);
DAS_name = info_HDF5.Groups.Name; <span class="comment">% Name of the DAS unit</span>
disp([info_HDF5])
Frequency = double(h5readatt([pathname filename <span class="string">'.h5'</span>],[DAS_name <span class="string">'/Source1/'</span>],<span class="string">'PulseRateFreq'</span>)); <span class="comment">%Hz - Pulse periode</span>
Freq_res = double(h5readatt([pathname filename <span class="string">'.h5'</span>],[DAS_name <span class="string">'/Source1/'</span>],<span class="string">'FreqRes'</span>))/250; <span class="comment">%Hz - Pulse periode</span>
Acquisition_length = double(h5readatt([pathname filename <span class="string">'.h5'</span>],[DAS_name <span class="string">'/Source1/'</span>],<span class="string">'FiberLength'</span>)); <span class="comment">%m - Distance of fiber for acquistion</span>
Sampling_Res = double(h5readatt([pathname filename <span class="string">'.h5'</span>],[DAS_name <span class="string">'/Source1/'</span>],<span class="string">'SamplingRes'</span>)); <span class="comment">%cm - Sampling resolution</span>
Origin = double(h5readatt([pathname filename <span class="string">'.h5'</span>],[DAS_name <span class="string">'/Source1/Zone'</span> num2str(Zone_to_Plot) <span class="string">'/'</span>],<span class="string">'Origin'</span>));
Spacing = double(h5readatt([pathname filename <span class="string">'.h5'</span>],[DAS_name <span class="string">'/Source1/Zone'</span> num2str(Zone_to_Plot) <span class="string">'/'</span>],<span class="string">'Spacing'</span>));
Extent = double(h5readatt([pathname filename <span class="string">'.h5'</span>],[DAS_name <span class="string">'/Source1/Zone'</span> num2str(Zone_to_Plot) <span class="string">'/'</span>],<span class="string">'Extent'</span>));
ZI_start = Origin(1)+(Extent(1))*Spacing(1); <span class="comment">%m - Start distance of the zone</span>
ZI_end = Origin(1)+(Extent(2))*Spacing(1); <span class="comment">%m - End distance of the zone</span>
Distance_fiber = ZI_start:Spacing(1):ZI_end; <span class="comment">%m - Distance vector of the fiber</span>
clear <span class="string">Origin</span>, clear <span class="string">Extent</span>, clear <span class="string">info_HDF5</span>
dset_id_1 = H5D.open(fid,[DAS_name <span class="string">'/Source1/Zone'</span> num2str(Zone_to_Plot) <span class="string">'/SR_Valencia'</span>]);
space_id_1 = H5D.get_space(dset_id_1);
[ndims,dims] = H5S.get_simple_extent_dims(space_id_1); <span class="comment">%Get the data dimension</span>
nb_Block = dims(1); <span class="comment">%Number of blocks</span>
Block_Time_size = dims(2); <span class="comment">%Time size</span>
Block_Space_size = dims(3); <span class="comment">%Distance size</span>
Time_0 = double(h5read([pathname filename <span class="string">'.h5'</span>],[DAS_name <span class="string">'/Source1/time'</span>])); <span class="comment">%s - Time stamp of each blocks</span>
disp([<span class="string">'     Device : '</span> DAS_name])
disp([<span class="string">'     Fiber length : '</span> num2str(Acquisition_length) <span class="string">' m'</span>])
disp([<span class="string">'     Zone of interest : '</span> num2str(Zone_to_Plot) <span class="string">' from '</span> num2str(ZI_start) <span class="string">' m to '</span> num2str(ZI_end) <span class="string">' m'</span>])
disp([<span class="string">'     Total time of acquisition : '</span> num2str((nb_Block)*Freq_res) <span class="string">' s '</span>])
disp([<span class="string">'     Spatial resolution : '</span> num2str(Spacing(1)) <span class="string">' m'</span>])
disp([<span class="string">'     Temporal resolution : '</span> num2str(Spacing(2)) <span class="string">' ms'</span>])
disp([<span class="string">'     PRF original : '</span> num2str(Frequency) <span class="string">' Hz'</span>])
disp([<span class="string">'     Sampling resolution original : '</span> num2str(Sampling_Res) <span class="string">' cm'</span>])
clear <span class="string">ZI_start</span>, clear <span class="string">ZI_end</span>, clear <span class="string">Sampling_Res</span>, clear <span class="string">Frequency</span>, clear <span class="string">Acquisition_length</span>
</pre><h2 id="6">Initialize blocks and index limits</h2><pre class="codeinput"><span class="keyword">if</span> strcmp(Section_time,<span class="string">'no'</span>) == 1
    First_Block = 0;
    Last_Block = nb_Block-1;
    First_record = Block_Time_size/4;
    Last_record = 3*Block_Time_size/4;
    Total_time_size = nb_Block*Block_Time_size/2;
    Total_time = (0:(Spacing(2)*1e-3):(Total_time_size-1)*(Spacing(2)*1e-3));
<span class="keyword">else</span>
    Freq_res
    From_Time
    First_Block = fix(From_Time*Freq_res);
    Last_Block = fix(To_Time*Freq_res);
    First_record = Block_Time_size/4+(From_Time-First_Block)/(Spacing(2)*1e-3);
    Last_record = Block_Time_size/4+(To_Time-Last_Block)/(Spacing(2)*1e-3);
    Total_time_size = 3*Block_Time_size/4-First_record + ((Last_Block-1)-(First_Block))*Block_Time_size/2 + Last_record-Block_Time_size/4;
    Total_time = (0:((Spacing(2)*1e-3)):(Total_time_size-1)*((Spacing(2)*1e-3)))+From_Time;
<span class="keyword">end</span>
<span class="keyword">if</span> strcmp(Section_space,<span class="string">'no'</span>) == 1
    First_point = 0;
    Last_point = Block_Space_size;
    Total_space_size = Block_Space_size;
<span class="keyword">else</span>
    First_point = find(abs(Distance_fiber-From_Position*1000) == min(abs(Distance_fiber-From_Position*1000)))-1;
    Last_point = find(abs(Distance_fiber-To_Position*1000) == min(abs(Distance_fiber-To_Position*1000)))-1;
    First_point = First_point(1);
    Last_point = Last_point(1);
    Total_space_size = (Last_point-First_point)+1;
    Distance_fiber = Distance_fiber(First_point:Last_point);
<span class="keyword">end</span>
</pre><pre class="codeoutput"> 
&gt; Initialization
./SR_Valencia_2020-09-02_21-32-00_UTC_10mins.h5
      Filename: '/home/<USER>/Desktop/html/SR_Valencia_2020-09-02_21-32-00_UTC_10mins.h5'
          Name: '/'
        Groups: [1&times;1 struct]
      Datasets: []
     Datatypes: []
         Links: []
    Attributes: []

     Device : /fa1-20050027
     Fiber length : 50000 m
     Zone of interest : 1 from 0 m to 49996.8 m
     Total time of acquisition : 601 s 
     Spatial resolution : 16.8 m
     Temporal resolution : 2 ms
     PRF original : 1000000 Hz
     Sampling resolution original : 80 cm

Freq_res =

     1


From_Time =

     1

</pre><pre class="codeinput"><span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>
<span class="comment">%%%%%%%%%%%%%%%%%%%% Extraction and concatenation %%%%%%%%%%%%%%%%%%%%%%%%%</span>
<span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>

disp(<span class="string">' '</span>)
disp(<span class="string">'&gt; Data extraction'</span>)
Frequency=250;

<span class="keyword">for</span> tt = First_Block:Last_Block
    disp([<span class="string">'     '</span> num2str(tt-First_Block+1) <span class="string">'/'</span> num2str(Last_Block-First_Block+1) <span class="string">' blocks'</span>])

        <span class="keyword">if</span> tt == First_Block
StrainRate_0=h5read([pathname filename,<span class="string">'.h5'</span>],[DAS_name <span class="string">'/Source1/Zone'</span> num2str(Zone_to_Plot) <span class="string">'/SR_Valencia'</span>],[First_point+1, 1, tt+1],<span class="keyword">...</span>
[Last_point-First_point,Frequency,1],[1,1,1]);
StrainRate = StrainRate_0;
        <span class="keyword">elseif</span> tt&lt;Last_Block
StrainRate_0=h5read([pathname filename,<span class="string">'.h5'</span>],[DAS_name <span class="string">'/Source1/Zone'</span> num2str(Zone_to_Plot) <span class="string">'/SR_Valencia'</span>],[First_point+1, 1, tt+1],<span class="keyword">...</span>
[Last_point-First_point,Frequency,1],[1,1,1]);
StrainRate(:,size(StrainRate,2)+1:size(StrainRate,2)+size(StrainRate_0,2)) = StrainRate_0;

<span class="keyword">end</span>
<span class="keyword">end</span>

<span class="keyword">for</span> ii=1:size(StrainRate,1)

StrainRate(ii,:) = filter1(<span class="string">'lp'</span>,double(StrainRate(ii,:)),<span class="string">'fs'</span>,250,<span class="string">'fc'</span>,10);

<span class="keyword">end</span>

<span class="keyword">for</span> ii=1:size(StrainRate,1)

StrainRate(ii,:) = filter1(<span class="string">'hp'</span>,double(StrainRate(ii,:)),<span class="string">'fs'</span>,250,<span class="string">'fc'</span>,0.5);

<span class="keyword">end</span>
</pre><pre class="codeoutput"> 
&gt; Data extraction
     1/300 blocks
     2/300 blocks
     3/300 blocks
     4/300 blocks
     5/300 blocks
     6/300 blocks
     7/300 blocks
     8/300 blocks
     9/300 blocks
     10/300 blocks
     11/300 blocks
     12/300 blocks
     13/300 blocks
     14/300 blocks
     15/300 blocks
     16/300 blocks
     17/300 blocks
     18/300 blocks
     19/300 blocks
     20/300 blocks
     21/300 blocks
     22/300 blocks
     23/300 blocks
     24/300 blocks
     25/300 blocks
     26/300 blocks
     27/300 blocks
     28/300 blocks
     29/300 blocks
     30/300 blocks
     31/300 blocks
     32/300 blocks
     33/300 blocks
     34/300 blocks
     35/300 blocks
     36/300 blocks
     37/300 blocks
     38/300 blocks
     39/300 blocks
     40/300 blocks
     41/300 blocks
     42/300 blocks
     43/300 blocks
     44/300 blocks
     45/300 blocks
     46/300 blocks
     47/300 blocks
     48/300 blocks
     49/300 blocks
     50/300 blocks
     51/300 blocks
     52/300 blocks
     53/300 blocks
     54/300 blocks
     55/300 blocks
     56/300 blocks
     57/300 blocks
     58/300 blocks
     59/300 blocks
     60/300 blocks
     61/300 blocks
     62/300 blocks
     63/300 blocks
     64/300 blocks
     65/300 blocks
     66/300 blocks
     67/300 blocks
     68/300 blocks
     69/300 blocks
     70/300 blocks
     71/300 blocks
     72/300 blocks
     73/300 blocks
     74/300 blocks
     75/300 blocks
     76/300 blocks
     77/300 blocks
     78/300 blocks
     79/300 blocks
     80/300 blocks
     81/300 blocks
     82/300 blocks
     83/300 blocks
     84/300 blocks
     85/300 blocks
     86/300 blocks
     87/300 blocks
     88/300 blocks
     89/300 blocks
     90/300 blocks
     91/300 blocks
     92/300 blocks
     93/300 blocks
     94/300 blocks
     95/300 blocks
     96/300 blocks
     97/300 blocks
     98/300 blocks
     99/300 blocks
     100/300 blocks
     101/300 blocks
     102/300 blocks
     103/300 blocks
     104/300 blocks
     105/300 blocks
     106/300 blocks
     107/300 blocks
     108/300 blocks
     109/300 blocks
     110/300 blocks
     111/300 blocks
     112/300 blocks
     113/300 blocks
     114/300 blocks
     115/300 blocks
     116/300 blocks
     117/300 blocks
     118/300 blocks
     119/300 blocks
     120/300 blocks
     121/300 blocks
     122/300 blocks
     123/300 blocks
     124/300 blocks
     125/300 blocks
     126/300 blocks
     127/300 blocks
     128/300 blocks
     129/300 blocks
     130/300 blocks
     131/300 blocks
     132/300 blocks
     133/300 blocks
     134/300 blocks
     135/300 blocks
     136/300 blocks
     137/300 blocks
     138/300 blocks
     139/300 blocks
     140/300 blocks
     141/300 blocks
     142/300 blocks
     143/300 blocks
     144/300 blocks
     145/300 blocks
     146/300 blocks
     147/300 blocks
     148/300 blocks
     149/300 blocks
     150/300 blocks
     151/300 blocks
     152/300 blocks
     153/300 blocks
     154/300 blocks
     155/300 blocks
     156/300 blocks
     157/300 blocks
     158/300 blocks
     159/300 blocks
     160/300 blocks
     161/300 blocks
     162/300 blocks
     163/300 blocks
     164/300 blocks
     165/300 blocks
     166/300 blocks
     167/300 blocks
     168/300 blocks
     169/300 blocks
     170/300 blocks
     171/300 blocks
     172/300 blocks
     173/300 blocks
     174/300 blocks
     175/300 blocks
     176/300 blocks
     177/300 blocks
     178/300 blocks
     179/300 blocks
     180/300 blocks
     181/300 blocks
     182/300 blocks
     183/300 blocks
     184/300 blocks
     185/300 blocks
     186/300 blocks
     187/300 blocks
     188/300 blocks
     189/300 blocks
     190/300 blocks
     191/300 blocks
     192/300 blocks
     193/300 blocks
     194/300 blocks
     195/300 blocks
     196/300 blocks
     197/300 blocks
     198/300 blocks
     199/300 blocks
     200/300 blocks
     201/300 blocks
     202/300 blocks
     203/300 blocks
     204/300 blocks
     205/300 blocks
     206/300 blocks
     207/300 blocks
     208/300 blocks
     209/300 blocks
     210/300 blocks
     211/300 blocks
     212/300 blocks
     213/300 blocks
     214/300 blocks
     215/300 blocks
     216/300 blocks
     217/300 blocks
     218/300 blocks
     219/300 blocks
     220/300 blocks
     221/300 blocks
     222/300 blocks
     223/300 blocks
     224/300 blocks
     225/300 blocks
     226/300 blocks
     227/300 blocks
     228/300 blocks
     229/300 blocks
     230/300 blocks
     231/300 blocks
     232/300 blocks
     233/300 blocks
     234/300 blocks
     235/300 blocks
     236/300 blocks
     237/300 blocks
     238/300 blocks
     239/300 blocks
     240/300 blocks
     241/300 blocks
     242/300 blocks
     243/300 blocks
     244/300 blocks
     245/300 blocks
     246/300 blocks
     247/300 blocks
     248/300 blocks
     249/300 blocks
     250/300 blocks
     251/300 blocks
     252/300 blocks
     253/300 blocks
     254/300 blocks
     255/300 blocks
     256/300 blocks
     257/300 blocks
     258/300 blocks
     259/300 blocks
     260/300 blocks
     261/300 blocks
     262/300 blocks
     263/300 blocks
     264/300 blocks
     265/300 blocks
     266/300 blocks
     267/300 blocks
     268/300 blocks
     269/300 blocks
     270/300 blocks
     271/300 blocks
     272/300 blocks
     273/300 blocks
     274/300 blocks
     275/300 blocks
     276/300 blocks
     277/300 blocks
     278/300 blocks
     279/300 blocks
     280/300 blocks
     281/300 blocks
     282/300 blocks
     283/300 blocks
     284/300 blocks
     285/300 blocks
     286/300 blocks
     287/300 blocks
     288/300 blocks
     289/300 blocks
     290/300 blocks
     291/300 blocks
     292/300 blocks
     293/300 blocks
     294/300 blocks
     295/300 blocks
     296/300 blocks
     297/300 blocks
     298/300 blocks
     299/300 blocks
     300/300 blocks
</pre><pre class="codeinput"><span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>
<span class="comment">%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Plotting %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%</span>

figure;
imagesc([1:size(StrainRate,2)]/Frequency,[1:size(StrainRate,1)]*Spacing(1),StrainRate)
colormap(jet)
xlabel(<span class="string">'Time (s)'</span>), ylabel(<span class="string">'Distance (m)'</span>)
title(<span class="string">'Strain rate (0.5-10 Hz)'</span>)
set(gca,<span class="string">'fontsize'</span>,15)
caxis([-20 20])


H5S.close(space_id_1);
H5D.close(dset_id_1);
H5F.close(fid);
clear <span class="string">Block_Space_size</span>, clear <span class="string">Block_Time_size</span>, clear <span class="string">First_Block</span>, clear <span class="string">Last_Block</span>, clear <span class="string">First_record</span>, clear <span class="string">Last_record</span>
clear <span class="string">block</span>, clear <span class="string">nb_Block</span>, clear <span class="string">space_id_1</span>, clear <span class="string">dset_id_1</span>, clear <span class="string">fid</span>, clear <span class="string">mem_space_id</span>, clear <span class="string">dims</span>, clear <span class="string">ndims</span>, clear <span class="string">DAS_name</span>
clear <span class="string">tt</span>, clear <span class="string">offset</span>, clear <span class="string">From_Time</span>, clear <span class="string">To_Time</span>, clear <span class="string">Section_time</span>, clear <span class="string">Total_time_size</span>
clear <span class="string">Total_space_size</span>, clear <span class="string">First_point</span>, clear <span class="string">Last_point</span>, clear <span class="string">From_Position</span>, clear <span class="string">To_Position</span>, clear <span class="string">Section_space</span>, clear <span class="string">Zone_to_Plot</span>
</pre><img vspace="5" hspace="5" src="read_das_data_01.png" alt=""> <p class="footer"><br><a href="https://www.mathworks.com/products/matlab/">Published with MATLAB&reg; R2018b</a><br></p></div><!--
##### SOURCE BEGIN #####
%%% Extraction Strain rate from HDF5
%%% Version Han 202203
%%% 
clc
clear
%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%% Parametring %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% File %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

pathname = './'; %To fill

filename = 'SR_Valencia_2020-09-02_21-32-00_UTC_10mins';

disp(['/// ' filename ' \\\'])
Zone_to_Plot = 1; % Put 1 if only one zone otherwise choose the zone

%%%%%%%%%%%%%%%%%%%%%%%%%%%% Time option %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
Section_time = 'yes'; % Put yes if choosing a section of time
From_Time = 1; %s
To_Time = 300; %s

%%%%%%%%%%%%%%%%%%%%%%%%%%%% Space option %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
Section_space = 'no'; % Put yes if choosing a section of space
From_Position = 1.00; %Km
To_Position = 2.0; %Km

%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%% Initialization %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

disp(' ')
disp('> Initialization')
disp([pathname filename '.h5'])
fid = H5F.open([pathname filename '.h5']);
info_HDF5 = h5info([pathname filename '.h5'],'/');
DAS_name = info_HDF5.Groups.Name; % Name of the DAS unit
disp([info_HDF5])
Frequency = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/'],'PulseRateFreq')); %Hz - Pulse periode
Freq_res = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/'],'FreqRes'))/250; %Hz - Pulse periode
Acquisition_length = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/'],'FiberLength')); %m - Distance of fiber for acquistion
Sampling_Res = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/'],'SamplingRes')); %cm - Sampling resolution
Origin = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/'],'Origin'));
Spacing = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/'],'Spacing'));
Extent = double(h5readatt([pathname filename '.h5'],[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/'],'Extent'));
ZI_start = Origin(1)+(Extent(1))*Spacing(1); %m - Start distance of the zone
ZI_end = Origin(1)+(Extent(2))*Spacing(1); %m - End distance of the zone
Distance_fiber = ZI_start:Spacing(1):ZI_end; %m - Distance vector of the fiber 
clear Origin, clear Extent, clear info_HDF5
dset_id_1 = H5D.open(fid,[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/SR_Valencia']);
space_id_1 = H5D.get_space(dset_id_1);
[ndims,dims] = H5S.get_simple_extent_dims(space_id_1); %Get the data dimension
nb_Block = dims(1); %Number of blocks
Block_Time_size = dims(2); %Time size
Block_Space_size = dims(3); %Distance size
Time_0 = double(h5read([pathname filename '.h5'],[DAS_name '/Source1/time'])); %s - Time stamp of each blocks
disp(['     Device : ' DAS_name])
disp(['     Fiber length : ' num2str(Acquisition_length) ' m'])
disp(['     Zone of interest : ' num2str(Zone_to_Plot) ' from ' num2str(ZI_start) ' m to ' num2str(ZI_end) ' m'])
disp(['     Total time of acquisition : ' num2str((nb_Block)*Freq_res) ' s '])
disp(['     Spatial resolution : ' num2str(Spacing(1)) ' m'])
disp(['     Temporal resolution : ' num2str(Spacing(2)) ' ms'])
disp(['     PRF original : ' num2str(Frequency) ' Hz'])
disp(['     Sampling resolution original : ' num2str(Sampling_Res) ' cm'])
clear ZI_start, clear ZI_end, clear Sampling_Res, clear Frequency, clear Acquisition_length 

%%% Initialize blocks and index limits
if strcmp(Section_time,'no') == 1
    First_Block = 0; 
    Last_Block = nb_Block-1;
    First_record = Block_Time_size/4;
    Last_record = 3*Block_Time_size/4; 
    Total_time_size = nb_Block*Block_Time_size/2;  
    Total_time = (0:(Spacing(2)*1e-3):(Total_time_size-1)*(Spacing(2)*1e-3));
else
    Freq_res
    From_Time
    First_Block = fix(From_Time*Freq_res);
    Last_Block = fix(To_Time*Freq_res); 
    First_record = Block_Time_size/4+(From_Time-First_Block)/(Spacing(2)*1e-3);
    Last_record = Block_Time_size/4+(To_Time-Last_Block)/(Spacing(2)*1e-3); 
    Total_time_size = 3*Block_Time_size/4-First_record + ((Last_Block-1)-(First_Block))*Block_Time_size/2 + Last_record-Block_Time_size/4;
    Total_time = (0:((Spacing(2)*1e-3)):(Total_time_size-1)*((Spacing(2)*1e-3)))+From_Time;
end
if strcmp(Section_space,'no') == 1
    First_point = 0;
    Last_point = Block_Space_size;
    Total_space_size = Block_Space_size;
else
    First_point = find(abs(Distance_fiber-From_Position*1000) == min(abs(Distance_fiber-From_Position*1000)))-1;
    Last_point = find(abs(Distance_fiber-To_Position*1000) == min(abs(Distance_fiber-To_Position*1000)))-1;
    First_point = First_point(1);
    Last_point = Last_point(1);
    Total_space_size = (Last_point-First_point)+1;
    Distance_fiber = Distance_fiber(First_point:Last_point);
end

%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%% Extraction and concatenation %%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

disp(' ')
disp('> Data extraction')
Frequency=250;

for tt = First_Block:Last_Block
    disp(['     ' num2str(tt-First_Block+1) '/' num2str(Last_Block-First_Block+1) ' blocks'])

        if tt == First_Block
StrainRate_0=h5read([pathname filename,'.h5'],[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/SR_Valencia'],[First_point+1, 1, tt+1],...
[Last_point-First_point,Frequency,1],[1,1,1]);
StrainRate = StrainRate_0;
        elseif tt<Last_Block
StrainRate_0=h5read([pathname filename,'.h5'],[DAS_name '/Source1/Zone' num2str(Zone_to_Plot) '/SR_Valencia'],[First_point+1, 1, tt+1],...
[Last_point-First_point,Frequency,1],[1,1,1]);
StrainRate(:,size(StrainRate,2)+1:size(StrainRate,2)+size(StrainRate_0,2)) = StrainRate_0;

end
end

for ii=1:size(StrainRate,1)

StrainRate(ii,:) = filter1('lp',double(StrainRate(ii,:)),'fs',250,'fc',10);

end

for ii=1:size(StrainRate,1)

StrainRate(ii,:) = filter1('hp',double(StrainRate(ii,:)),'fs',250,'fc',0.5);

end
%%           
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% Plotting %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

figure;
imagesc([1:size(StrainRate,2)]/Frequency,[1:size(StrainRate,1)]*Spacing(1),StrainRate)
colormap(jet)
xlabel('Time (s)'), ylabel('Distance (m)')
title('Strain rate (0.5-10 Hz)')
set(gca,'fontsize',15)
caxis([-20 20])


H5S.close(space_id_1);
H5D.close(dset_id_1);
H5F.close(fid);   
clear Block_Space_size, clear Block_Time_size, clear First_Block, clear Last_Block, clear First_record, clear Last_record 
clear block, clear nb_Block, clear space_id_1, clear dset_id_1, clear fid, clear mem_space_id, clear dims, clear ndims, clear DAS_name
clear tt, clear offset, clear From_Time, clear To_Time, clear Section_time, clear Total_time_size
clear Total_space_size, clear First_point, clear Last_point, clear From_Position, clear To_Position, clear Section_space, clear Zone_to_Plot
     


##### SOURCE END #####
--></body></html>