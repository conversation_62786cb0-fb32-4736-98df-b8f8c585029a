#!/usr/bin/env python3
"""
Demo script showing spectral validation of anomalies.
Creates synthetic DAS data with known anomalous events and validates them spectrally.
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import spectrogram, welch

def create_synthetic_das_with_events():
    """Create synthetic DAS data with known seismic-like events."""
    duration = 600  # 10 minutes
    fs = 250  # Hz
    n_samples = int(duration * fs)
    
    # Base signal: low frequency trend + noise
    time_axis = np.linspace(0, duration, n_samples)
    base_signal = 0.1 * np.sin(2 * np.pi * 0.02 * time_axis)  # 0.02 Hz trend
    noise = 0.05 * np.random.randn(n_samples)
    
    # Add realistic seismic events
    events = []
    
    # Event 1: High frequency burst (microseismic)
    event1_start = int(120 * fs)  # 2 minutes
    event1_duration = int(15 * fs)  # 15 seconds
    event1_freq = 25  # Hz
    event1_signal = 0.4 * np.sin(2 * np.pi * event1_freq * time_axis[event1_start:event1_start+event1_duration])
    event1_signal *= np.exp(-np.linspace(0, 3, event1_duration))  # Decay
    base_signal[event1_start:event1_start+event1_duration] += event1_signal
    events.append(('Microseismic', event1_start/fs, event1_duration/fs, event1_freq))
    
    # Event 2: Low frequency event (regional earthquake)
    event2_start = int(300 * fs)  # 5 minutes
    event2_duration = int(45 * fs)  # 45 seconds
    event2_freq = 2  # Hz
    event2_signal = 0.3 * np.sin(2 * np.pi * event2_freq * time_axis[event2_start:event2_start+event2_duration])
    event2_signal *= np.exp(-np.linspace(0, 1, event2_duration))  # Slow decay
    base_signal[event2_start:event2_start+event2_duration] += event2_signal
    events.append(('Regional Event', event2_start/fs, event2_duration/fs, event2_freq))
    
    # Event 3: Broadband event (local earthquake)
    event3_start = int(450 * fs)  # 7.5 minutes
    event3_duration = int(30 * fs)  # 30 seconds
    # Multiple frequency components
    for freq in [5, 12, 20]:
        event3_signal = 0.2 * np.sin(2 * np.pi * freq * time_axis[event3_start:event3_start+event3_duration])
        event3_signal *= np.exp(-np.linspace(0, 2, event3_duration))
        base_signal[event3_start:event3_start+event3_duration] += event3_signal
    events.append(('Local Earthquake', event3_start/fs, event3_duration/fs, 'Broadband'))
    
    synthetic_data = base_signal + noise
    
    return synthetic_data, events, fs

def compute_spectrogram_demo(data, fs=250):
    """Compute spectrogram for demo."""
    f, t, Sxx = spectrogram(data, fs=fs, nperseg=512, noverlap=256)
    return f, t, 10 * np.log10(Sxx + 1e-12)

def main():
    print("=== Spectral Anomaly Validation Demo ===")
    
    # Create synthetic data with known events
    print("Creating synthetic DAS data with known seismic events...")
    synthetic_data, events, fs = create_synthetic_das_with_events()
    
    print(f"Generated {len(synthetic_data)} samples ({len(synthetic_data)/fs:.1f} seconds)")
    print(f"Embedded {len(events)} seismic events:")
    for event_name, start_time, duration, freq in events:
        print(f"  {event_name}: {start_time:.1f}s-{start_time+duration:.1f}s, {freq} Hz")
    
    # Create visualization
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Spectral Validation of Synthetic DAS Anomalies', fontsize=16)
    
    # 1. Full time series with events marked
    ax1 = axes[0, 0]
    time_axis = np.arange(len(synthetic_data)) / fs
    ax1.plot(time_axis, synthetic_data, 'b-', alpha=0.7, linewidth=0.8)
    
    # Mark events
    colors = ['red', 'orange', 'purple']
    for i, (event_name, start_time, duration, freq) in enumerate(events):
        ax1.axvspan(start_time, start_time + duration, alpha=0.3, color=colors[i], label=event_name)
    
    ax1.set_title('Synthetic DAS Data with Seismic Events')
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Strain Rate')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Full spectrogram
    ax2 = axes[0, 1]
    f, t, Sxx = compute_spectrogram_demo(synthetic_data, fs)
    im2 = ax2.pcolormesh(t, f, Sxx, shading='gouraud', cmap='viridis')
    ax2.set_title('Full Spectrogram')
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Frequency (Hz)')
    ax2.set_ylim([0, 50])
    plt.colorbar(im2, ax=ax2, label='Power (dB)')
    
    # Mark events on spectrogram
    for event_name, start_time, duration, freq in events:
        ax2.axvline(start_time, color='white', linestyle='--', alpha=0.8)
        ax2.axvline(start_time + duration, color='white', linestyle='--', alpha=0.8)
    
    # 3. Event segment example
    ax3 = axes[1, 0]
    # Extract first event segment
    event_start = int(events[0][1] * fs)
    event_duration = int(events[0][2] * fs)
    event_segment = synthetic_data[event_start:event_start+event_duration]
    
    f_seg, t_seg, Sxx_seg = compute_spectrogram_demo(event_segment, fs)
    im3 = ax3.pcolormesh(t_seg, f_seg, Sxx_seg, shading='gouraud', cmap='viridis')
    ax3.set_title(f'{events[0][0]} Event Spectrogram')
    ax3.set_xlabel('Time (s)')
    ax3.set_ylabel('Frequency (Hz)')
    ax3.set_ylim([0, 50])
    plt.colorbar(im3, ax=ax3, label='Power (dB)')
    
    # 4. Normal segment example
    ax4 = axes[1, 1]
    # Extract normal segment (before first event)
    normal_start = int(60 * fs)  # 1 minute
    normal_duration = int(30 * fs)  # 30 seconds
    normal_segment = synthetic_data[normal_start:normal_start+normal_duration]
    
    f_norm, t_norm, Sxx_norm = compute_spectrogram_demo(normal_segment, fs)
    im4 = ax4.pcolormesh(t_norm, f_norm, Sxx_norm, shading='gouraud', cmap='viridis')
    ax4.set_title('Normal Segment Spectrogram')
    ax4.set_xlabel('Time (s)')
    ax4.set_ylabel('Frequency (Hz)')
    ax4.set_ylim([0, 50])
    plt.colorbar(im4, ax=ax4, label='Power (dB)')
    
    plt.tight_layout()
    plt.savefig('spectral_validation_demo.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Validation analysis
    print("\n=== Spectral Validation Results ===")
    
    # Compare PSDs
    f_event, psd_event = welch(event_segment, fs=fs, nperseg=512)
    f_normal, psd_normal = welch(normal_segment, fs=fs, nperseg=512)
    
    psd_event_db = 10 * np.log10(psd_event + 1e-12)
    psd_normal_db = 10 * np.log10(psd_normal + 1e-12)
    
    spectral_ratio = psd_event_db - psd_normal_db
    max_diff_idx = np.argmax(np.abs(spectral_ratio))
    max_diff_freq = f_event[max_diff_idx]
    max_diff_value = spectral_ratio[max_diff_idx]
    
    print(f"Strongest spectral difference: {max_diff_value:.1f} dB at {max_diff_freq:.1f} Hz")
    
    if abs(max_diff_value) > 5:
        print("✓ STRONG VALIDATION: Clear spectral signature confirms real seismic events")
    elif abs(max_diff_value) > 2:
        print("✓ MODERATE VALIDATION: Spectral differences suggest real events")
    else:
        print("⚠ WEAK VALIDATION: Limited spectral differences")
    
    print("\n✓ Spectral validation demo complete!")
    print("✓ Visualization saved as 'spectral_validation_demo.png'")
    print("\nThis demonstrates how spectral analysis can validate whether detected")
    print("anomalies represent real seismic events or just prediction artifacts.")

if __name__ == "__main__":
    main()
