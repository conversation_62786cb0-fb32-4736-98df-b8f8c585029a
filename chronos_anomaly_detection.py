#!/usr/bin/env python3
"""
Phase 1 Anomaly Detection using Pre-trained Chronos model.
Tests how well Chronos can detect anomalies in DAS data without training.
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import torch
from typing import List, Tuple, Dict
import warnings

# Add src directory to path
sys.path.append('src')

try:
    from chronos_loader import DASChronosDataset
    print("✓ Successfully imported chronos_loader")
except ImportError as e:
    print(f"✗ Failed to import chronos_loader: {e}")
    sys.exit(1)

def setup_plotting():
    """Set up matplotlib for better plots."""
    plt.style.use('default')
    sns.set_palette("husl")
    plt.rcParams['figure.figsize'] = (15, 10)
    plt.rcParams['font.size'] = 10
    plt.rcParams['axes.grid'] = True
    plt.rcParams['grid.alpha'] = 0.3

class ChronosAnomalyDetector:
    """Anomaly detector using pre-trained Chronos model."""
    
    def __init__(self, model_size: str = "tiny"):
        """
        Initialize with pre-trained Chronos model.
        
        Parameters:
        -----------
        model_size : str
            Size of Chronos model: "tiny", "mini", "small", "base", "large"
        """
        self.model_size = model_size
        self.model = None
        self.tokenizer = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        print(f"Initializing Chronos anomaly detector with {model_size} model...")
        self._load_model()
    
    def _load_model(self):
        """Load pre-trained Chronos model."""
        try:
            from chronos import ChronosPipeline
            
            model_name = f"amazon/chronos-t5-{self.model_size}"
            print(f"Loading {model_name}...")
            
            self.pipeline = ChronosPipeline.from_pretrained(
                model_name,
                device_map=self.device,
                torch_dtype=torch.bfloat16 if self.device.type == "cuda" else torch.float32,
            )
            
            print(f"✓ Chronos model loaded successfully on {self.device}")
            
        except ImportError:
            print("✗ Chronos library not available. Installing...")
            print("Please run: pip install git+https://github.com/amazon-science/chronos-forecasting.git")
            sys.exit(1)
        except Exception as e:
            print(f"✗ Error loading Chronos model: {e}")
            print("Falling back to mock predictions for demonstration...")
            self.pipeline = None
    
    def predict(self, context: np.ndarray, prediction_length: int = 32) -> np.ndarray:
        """
        Predict future values using Chronos.
        
        Parameters:
        -----------
        context : np.ndarray
            Historical time series data [context_length]
        prediction_length : int
            Number of steps to predict
            
        Returns:
        --------
        predictions : np.ndarray
            Predicted values [prediction_length]
        """
        if self.pipeline is None:
            # Mock prediction for demonstration
            return np.random.normal(0, 0.1, prediction_length)
        
        try:
            # Convert to tensor and add batch dimension
            context_tensor = torch.tensor(context, dtype=torch.float32).unsqueeze(0)
            
            # Generate forecast
            forecast = self.pipeline.predict(
                context_tensor,
                prediction_length=prediction_length,
                num_samples=1
            )
            
            # Return first (and only) sample
            return forecast[0].cpu().numpy()
            
        except Exception as e:
            print(f"Warning: Prediction failed: {e}")
            # Fallback to simple prediction
            return np.full(prediction_length, context[-1]) + np.random.normal(0, 0.01, prediction_length)
    
    def compute_anomaly_scores(self, 
                             dataset: DASChronosDataset, 
                             active_sensors: List[int],
                             max_samples: int = 1000) -> Dict:
        """
        Compute anomaly scores for DAS data.
        
        Parameters:
        -----------
        dataset : DASChronosDataset
            DAS dataset
        active_sensors : List[int]
            List of active sensor indices
        max_samples : int
            Maximum number of samples to evaluate
            
        Returns:
        --------
        results : Dict
            Dictionary containing anomaly scores and metadata
        """
        print(f"Computing anomaly scores for {min(max_samples, len(dataset))} samples...")
        
        anomaly_scores = []
        predictions = []
        actuals = []
        sensor_indices = []
        sample_indices = []
        
        # Sample from dataset
        sample_step = max(1, len(dataset) // max_samples)
        
        for i in range(0, min(len(dataset), max_samples * sample_step), sample_step):
            if i % 100 == 0:
                print(f"  Processing sample {i//sample_step + 1}/{min(max_samples, len(dataset)//sample_step)}")
            
            try:
                # Get sample
                sample = dataset[i]
                context = sample['past_values'].numpy()
                target = sample['future_values'].numpy()
                sensor_idx = sample['sensor_idx'].item()
                
                # Skip inactive sensors
                if sensor_idx not in active_sensors:
                    continue
                
                # Predict using Chronos
                prediction = self.predict(context, len(target))
                
                # Compute prediction error (anomaly score)
                mse_error = np.mean((prediction - target) ** 2)
                mae_error = np.mean(np.abs(prediction - target))
                
                # Store results
                anomaly_scores.append({
                    'mse': mse_error,
                    'mae': mae_error,
                    'max_error': np.max(np.abs(prediction - target))
                })
                predictions.append(prediction)
                actuals.append(target)
                sensor_indices.append(sensor_idx)
                sample_indices.append(i)
                
            except Exception as e:
                print(f"Warning: Error processing sample {i}: {e}")
                continue
        
        print(f"✓ Computed anomaly scores for {len(anomaly_scores)} samples")
        
        return {
            'scores': anomaly_scores,
            'predictions': predictions,
            'actuals': actuals,
            'sensor_indices': sensor_indices,
            'sample_indices': sample_indices
        }
    
    def analyze_anomalies(self, results: Dict, percentile_threshold: float = 95) -> Dict:
        """
        Analyze anomaly detection results.
        
        Parameters:
        -----------
        results : Dict
            Results from compute_anomaly_scores
        percentile_threshold : float
            Percentile threshold for anomaly detection
            
        Returns:
        --------
        analysis : Dict
            Analysis results including anomaly flags
        """
        scores = results['scores']
        
        # Extract different error metrics
        mse_scores = [s['mse'] for s in scores]
        mae_scores = [s['mae'] for s in scores]
        max_errors = [s['max_error'] for s in scores]
        
        # Compute thresholds
        mse_threshold = np.percentile(mse_scores, percentile_threshold)
        mae_threshold = np.percentile(mae_scores, percentile_threshold)
        max_threshold = np.percentile(max_errors, percentile_threshold)
        
        # Flag anomalies
        mse_anomalies = np.array(mse_scores) > mse_threshold
        mae_anomalies = np.array(mae_scores) > mae_threshold
        max_anomalies = np.array(max_errors) > max_threshold
        
        # Combined anomaly detection (any metric exceeds threshold)
        combined_anomalies = mse_anomalies | mae_anomalies | max_anomalies
        
        print(f"\n=== Anomaly Detection Results ===")
        print(f"Total samples analyzed: {len(scores)}")
        print(f"Threshold percentile: {percentile_threshold}%")
        print(f"MSE anomalies: {np.sum(mse_anomalies)} ({100*np.mean(mse_anomalies):.1f}%)")
        print(f"MAE anomalies: {np.sum(mae_anomalies)} ({100*np.mean(mae_anomalies):.1f}%)")
        print(f"Max error anomalies: {np.sum(max_anomalies)} ({100*np.mean(max_anomalies):.1f}%)")
        print(f"Combined anomalies: {np.sum(combined_anomalies)} ({100*np.mean(combined_anomalies):.1f}%)")
        
        return {
            'mse_scores': mse_scores,
            'mae_scores': mae_scores,
            'max_errors': max_errors,
            'mse_threshold': mse_threshold,
            'mae_threshold': mae_threshold,
            'max_threshold': max_threshold,
            'mse_anomalies': mse_anomalies,
            'mae_anomalies': mae_anomalies,
            'max_anomalies': max_anomalies,
            'combined_anomalies': combined_anomalies,
            'anomaly_indices': np.where(combined_anomalies)[0]
        }

def visualize_anomaly_results(results: Dict, analysis: Dict, max_examples: int = 6):
    """Visualize anomaly detection results."""
    print(f"\n=== Creating Anomaly Visualization ===")
    
    fig, axes = plt.subplots(3, 2, figsize=(16, 12))
    fig.suptitle('Chronos Anomaly Detection Results - DAS Data', fontsize=16)
    
    # 1. Distribution of anomaly scores
    ax1 = axes[0, 0]
    ax1.hist(analysis['mse_scores'], bins=50, alpha=0.7, label='MSE Scores')
    ax1.axvline(analysis['mse_threshold'], color='red', linestyle='--', 
                label=f'Threshold ({analysis["mse_threshold"]:.4f})')
    ax1.set_title('Distribution of MSE Anomaly Scores')
    ax1.set_xlabel('MSE Score')
    ax1.set_ylabel('Frequency')
    ax1.legend()
    ax1.set_yscale('log')
    
    # 2. Anomaly scores over time/samples
    ax2 = axes[0, 1]
    sample_indices = np.arange(len(analysis['mse_scores']))
    ax2.scatter(sample_indices, analysis['mse_scores'], 
                c=analysis['combined_anomalies'], cmap='coolwarm', alpha=0.6)
    ax2.axhline(analysis['mse_threshold'], color='red', linestyle='--', alpha=0.7)
    ax2.set_title('Anomaly Scores Over Samples')
    ax2.set_xlabel('Sample Index')
    ax2.set_ylabel('MSE Score')
    
    # 3. Example normal predictions
    ax3 = axes[1, 0]
    normal_indices = np.where(~analysis['combined_anomalies'])[0]
    if len(normal_indices) > 0:
        for i, idx in enumerate(normal_indices[:3]):
            prediction = results['predictions'][idx]
            actual = results['actuals'][idx]

            # Ensure both are arrays and same length
            if hasattr(prediction, '__len__') and hasattr(actual, '__len__'):
                min_len = min(len(prediction), len(actual))
                time_axis = np.arange(min_len)

                ax3.plot(time_axis, actual[:min_len], 'b-', alpha=0.7, label='Actual' if i == 0 else '')
                ax3.plot(time_axis, prediction[:min_len], 'g--', alpha=0.7, label='Predicted' if i == 0 else '')

    ax3.set_title('Normal Predictions (Low Anomaly Score)')
    ax3.set_xlabel('Time Steps')
    ax3.set_ylabel('Normalized Strain Rate')
    ax3.legend()

    # 4. Example anomalous predictions
    ax4 = axes[1, 1]
    anomaly_indices = analysis['anomaly_indices']
    if len(anomaly_indices) > 0:
        for i, idx in enumerate(anomaly_indices[:3]):
            prediction = results['predictions'][idx]
            actual = results['actuals'][idx]

            # Ensure both are arrays and same length
            if hasattr(prediction, '__len__') and hasattr(actual, '__len__'):
                min_len = min(len(prediction), len(actual))
                time_axis = np.arange(min_len)

                ax4.plot(time_axis, actual[:min_len], 'b-', alpha=0.7, label='Actual' if i == 0 else '')
                ax4.plot(time_axis, prediction[:min_len], 'r--', alpha=0.7, label='Predicted' if i == 0 else '')

    ax4.set_title('Anomalous Predictions (High Anomaly Score)')
    ax4.set_xlabel('Time Steps')
    ax4.set_ylabel('Normalized Strain Rate')
    ax4.legend()
    
    # 5. Sensor-wise anomaly distribution
    ax5 = axes[2, 0]
    sensor_indices = results['sensor_indices']
    unique_sensors = np.unique(sensor_indices)
    
    if len(unique_sensors) > 1:
        sensor_anomaly_rates = []
        for sensor in unique_sensors[:20]:  # First 20 sensors
            sensor_mask = np.array(sensor_indices) == sensor
            if np.sum(sensor_mask) > 0:
                anomaly_rate = np.mean(analysis['combined_anomalies'][sensor_mask])
                sensor_anomaly_rates.append(anomaly_rate)
            else:
                sensor_anomaly_rates.append(0)
        
        ax5.bar(range(len(sensor_anomaly_rates)), sensor_anomaly_rates, alpha=0.7)
        ax5.set_title('Anomaly Rate by Sensor')
        ax5.set_xlabel('Sensor Index (first 20)')
        ax5.set_ylabel('Anomaly Rate')
        ax5.set_xticks(range(len(sensor_anomaly_rates)))
        ax5.set_xticklabels([f'S{unique_sensors[i]}' for i in range(len(sensor_anomaly_rates))], 
                           rotation=45)
    else:
        ax5.text(0.5, 0.5, 'Need multiple sensors for analysis', 
                ha='center', va='center', transform=ax5.transAxes)
    
    # 6. Error correlation analysis
    ax6 = axes[2, 1]
    ax6.scatter(analysis['mse_scores'], analysis['mae_scores'], 
                c=analysis['combined_anomalies'], cmap='coolwarm', alpha=0.6)
    ax6.set_xlabel('MSE Score')
    ax6.set_ylabel('MAE Score')
    ax6.set_title('MSE vs MAE Correlation')
    
    plt.tight_layout()
    plt.savefig('chronos_anomaly_detection_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✓ Anomaly detection visualization saved as 'chronos_anomaly_detection_results.png'")

def main():
    """Main anomaly detection function."""
    print("=== Chronos Anomaly Detection - Phase 1 ===")
    
    setup_plotting()
    
    # Find H5 file
    data_dir = Path("Data")
    h5_files = list(data_dir.rglob("*.h5"))
    
    if not h5_files:
        print("✗ No H5 files found in Data directory")
        return False
    
    test_file = h5_files[0]
    print(f"Using file: {test_file}")
    
    try:
        # Create dataset
        print("Loading DAS dataset...")
        dataset = DASChronosDataset(
            data_path=test_file,
            context_length=128,
            prediction_length=32,
            stride=128,  # Non-overlapping windows for faster processing
            normalize=True,
            normalization_method='z_score',
            apply_filtering=False
        )
        
        # Find active sensors
        print("Finding active sensors...")
        active_sensors = []
        sample_data = dataset.data[:, :1000]
        for i in range(dataset.data.shape[0]):
            if np.std(sample_data[i, :]) > 1.0:
                active_sensors.append(i)
        
        print(f"Found {len(active_sensors)} active sensors")
        
        if len(active_sensors) == 0:
            print("✗ No active sensors found!")
            return False
        
        # Initialize anomaly detector
        detector = ChronosAnomalyDetector(model_size="tiny")
        
        # Compute anomaly scores
        results = detector.compute_anomaly_scores(
            dataset, 
            active_sensors, 
            max_samples=500  # Start with smaller sample for testing
        )
        
        if len(results['scores']) == 0:
            print("✗ No valid samples processed!")
            return False
        
        # Analyze anomalies
        analysis = detector.analyze_anomalies(results, percentile_threshold=90)
        
        # Visualize results
        visualize_anomaly_results(results, analysis)
        
        print(f"\n=== Phase 1 Anomaly Detection Complete ===")
        print("This baseline shows how well pre-trained Chronos detects DAS anomalies")
        print("Next steps: Fine-tune on normal DAS data for better performance")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during anomaly detection: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
