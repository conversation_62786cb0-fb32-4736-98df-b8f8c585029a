#!/usr/bin/env python3
"""
Check Chronos installation and setup.
"""

import sys
import os
from pathlib import Path

def check_chronos_installation():
    """Check if Chronos is properly installed and accessible."""
    
    print("=== CHRONOS INSTALLATION CHECK ===")
    
    # Check if src directory exists
    src_dir = Path("src")
    print(f"1. Checking src directory: {src_dir.absolute()}")
    if src_dir.exists():
        print(f"   ✓ src directory exists")
        print(f"   Contents: {list(src_dir.iterdir())}")
    else:
        print(f"   ❌ src directory not found")
        return False
    
    # Check if chronos module exists in src
    chronos_path = src_dir / "chronos"
    print(f"\n2. Checking chronos module: {chronos_path}")
    if chronos_path.exists():
        print(f"   ✓ chronos directory exists")
        if chronos_path.is_dir():
            print(f"   Contents: {list(chronos_path.iterdir())}")
        else:
            print(f"   ❌ chronos is not a directory")
            return False
    else:
        print(f"   ❌ chronos module not found in src")
        return False
    
    # Check for __init__.py
    init_file = chronos_path / "__init__.py"
    print(f"\n3. Checking __init__.py: {init_file}")
    if init_file.exists():
        print(f"   ✓ __init__.py exists")
    else:
        print(f"   ❌ __init__.py not found")
        return False
    
    # Try to import chronos
    print(f"\n4. Testing import...")
    sys.path.insert(0, str(src_dir))
    
    try:
        import chronos
        print(f"   ✓ chronos module imported successfully")
        print(f"   Module path: {chronos.__file__}")
        
        # Check for ChronosPipeline
        try:
            from chronos import ChronosPipeline
            print(f"   ✓ ChronosPipeline class available")
            return True
        except ImportError as e:
            print(f"   ❌ ChronosPipeline not available: {e}")
            return False
            
    except ImportError as e:
        print(f"   ❌ Failed to import chronos: {e}")
        return False

def check_pytorch():
    """Check PyTorch installation."""
    
    print(f"\n=== PYTORCH CHECK ===")
    
    try:
        import torch
        print(f"✓ PyTorch version: {torch.__version__}")
        
        # Check CUDA availability
        if torch.cuda.is_available():
            print(f"✓ CUDA available: {torch.cuda.get_device_name(0)}")
        else:
            print(f"⚠️  CUDA not available (will use CPU)")
        
        return True
        
    except ImportError as e:
        print(f"❌ PyTorch not available: {e}")
        return False

def check_huggingface():
    """Check Hugging Face transformers."""
    
    print(f"\n=== HUGGING FACE CHECK ===")
    
    try:
        import transformers
        print(f"✓ Transformers version: {transformers.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Transformers not available: {e}")
        return False

def suggest_installation():
    """Suggest how to install Chronos."""
    
    print(f"\n=== INSTALLATION SUGGESTIONS ===")
    
    print("If Chronos is not available, here are installation options:")
    print()
    print("Option 1: Install from PyPI (if available)")
    print("  pip install chronos-forecasting")
    print()
    print("Option 2: Install from GitHub")
    print("  git clone https://github.com/amazon-science/chronos-forecasting.git")
    print("  cd chronos-forecasting")
    print("  pip install -e .")
    print()
    print("Option 3: Download and setup manually")
    print("  1. Download Chronos source code")
    print("  2. Extract to 'src/chronos' directory")
    print("  3. Install dependencies: pip install torch transformers accelerate")
    print()
    print("Option 4: Use alternative time series models")
    print("  - Prophet: pip install prophet")
    print("  - ARIMA: pip install statsmodels")
    print("  - Neural Prophet: pip install neuralprophet")

def check_current_directory():
    """Check current working directory and files."""
    
    print(f"\n=== CURRENT DIRECTORY CHECK ===")
    
    cwd = Path.cwd()
    print(f"Current directory: {cwd}")
    print(f"Contents:")
    
    for item in sorted(cwd.iterdir()):
        if item.is_dir():
            print(f"  📁 {item.name}/")
        else:
            print(f"  📄 {item.name}")

def main():
    """Main diagnostic function."""
    
    print("Chronos Installation Diagnostic")
    print("=" * 40)
    
    # Check current directory
    check_current_directory()
    
    # Check PyTorch first (required for Chronos)
    pytorch_ok = check_pytorch()
    
    # Check Hugging Face transformers
    hf_ok = check_huggingface()
    
    # Check Chronos installation
    chronos_ok = check_chronos_installation()
    
    print(f"\n=== SUMMARY ===")
    print(f"PyTorch: {'✓' if pytorch_ok else '❌'}")
    print(f"Transformers: {'✓' if hf_ok else '❌'}")
    print(f"Chronos: {'✓' if chronos_ok else '❌'}")
    
    if chronos_ok:
        print(f"\n🎉 Chronos is properly installed and should work!")
    else:
        print(f"\n❌ Chronos is not available.")
        suggest_installation()
        
        print(f"\nFor now, the Valencia DAS test will use mock predictions.")
        print(f"This is fine for testing the visualization and anomaly detection logic.")
    
    return chronos_ok

if __name__ == "__main__":
    main()
