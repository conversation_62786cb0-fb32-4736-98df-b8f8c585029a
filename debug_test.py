#!/usr/bin/env python3
"""
Debug script to identify what's failing in the Valencia DAS test.
"""

import sys
import traceback
from pathlib import Path

def test_imports():
    """Test all imports step by step."""
    
    print("=== TESTING IMPORTS ===")
    
    # Basic packages
    basic_packages = ['numpy', 'matplotlib', 'scipy', 'pathlib']
    for pkg in basic_packages:
        try:
            __import__(pkg)
            print(f"✓ {pkg}")
        except ImportError as e:
            print(f"❌ {pkg}: {e}")
            return False
    
    # Matplotlib backend
    try:
        import matplotlib
        matplotlib.use('Agg')  # Use non-interactive backend
        import matplotlib.pyplot as plt
        print("✓ matplotlib.pyplot")
    except Exception as e:
        print(f"❌ matplotlib.pyplot: {e}")
        return False
    
    # Scipy signal
    try:
        from scipy.signal import spectrogram, resample
        print("✓ scipy.signal")
    except ImportError as e:
        print(f"❌ scipy.signal: {e}")
        return False
    
    # H5py (optional)
    try:
        import h5py
        print("✓ h5py")
    except ImportError as e:
        print(f"⚠️  h5py: {e} (will use synthetic data)")
    
    # Torch (optional)
    try:
        import torch
        print("✓ torch")
    except ImportError as e:
        print(f"⚠️  torch: {e} (will use mock predictions)")
    
    # Our custom modules
    try:
        from enhanced_spectral_visualization import visualize_spectral_anomalies_enhanced
        print("✓ enhanced_spectral_visualization")
    except ImportError as e:
        print(f"❌ enhanced_spectral_visualization: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality without complex dependencies."""
    
    print("\n=== TESTING BASIC FUNCTIONALITY ===")
    
    try:
        import numpy as np
        import matplotlib
        matplotlib.use('Agg')  # Non-interactive backend
        import matplotlib.pyplot as plt
        from scipy.signal import spectrogram
        
        # Create simple test data
        print("Creating test data...")
        sample_rate = 25
        duration = 60  # 1 minute
        t = np.linspace(0, duration, int(sample_rate * duration))
        
        # Simple signal with two frequency components
        signal = (np.sin(2 * np.pi * 2 * t) +  # 2 Hz
                 0.5 * np.sin(2 * np.pi * 8 * t) +  # 8 Hz
                 0.1 * np.random.randn(len(t)))  # noise
        
        print(f"✓ Created {len(signal)} samples at {sample_rate} Hz")
        
        # Test spectrogram
        print("Testing spectrogram...")
        f, t_spec, Sxx = spectrogram(signal, fs=sample_rate, nperseg=128, noverlap=64)
        Sxx_db = 10 * np.log10(Sxx + 1e-12)
        
        print(f"✓ Spectrogram: {len(f)} frequencies × {len(t_spec)} time bins")
        print(f"  Frequency range: {f[0]:.2f} - {f[-1]:.2f} Hz")
        print(f"  Time range: {t_spec[0]:.2f} - {t_spec[-1]:.2f} s")
        
        # Test basic plotting
        print("Testing basic plot...")
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))
        im = ax.pcolormesh(t_spec, f, Sxx_db, shading='gouraud', cmap='viridis')
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Frequency (Hz)')
        ax.set_title('Test Spectrogram')
        plt.colorbar(im, ax=ax, label='Power (dB)')
        
        # Save plot
        plt.savefig('debug_test_plot.png', dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        print("✓ Basic plot saved as 'debug_test_plot.png'")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False

def test_enhanced_visualization():
    """Test the enhanced visualization with minimal data."""
    
    print("\n=== TESTING ENHANCED VISUALIZATION ===")
    
    try:
        from enhanced_spectral_visualization import get_appropriate_freq_bands
        
        # Test frequency band function
        print("Testing frequency band selection...")
        
        sample_rates = [10, 25, 50]
        for sr in sample_rates:
            bands = get_appropriate_freq_bands(sr, 'land')
            nyquist = sr / 2
            
            print(f"Sample rate {sr} Hz (Nyquist: {nyquist} Hz):")
            all_valid = True
            for name, (f_low, f_high) in bands.items():
                valid = f_high <= nyquist
                status = "✓" if valid else "❌"
                print(f"  {name}: {f_low}-{f_high} Hz {status}")
                if not valid:
                    all_valid = False
            
            if not all_valid:
                print(f"❌ Some bands exceed Nyquist frequency for {sr} Hz")
                return False
        
        print("✓ All frequency bands properly constrained")
        
        # Test creating mock results
        print("Creating mock spectral results...")
        
        # Create minimal mock results structure
        sample_rate = 25
        start_time = 0
        duration = 60
        
        # Mock spectrogram data
        n_freq = 50
        n_time = 30
        f = np.linspace(0, sample_rate/2, n_freq)
        t = np.linspace(start_time, start_time + duration, n_time)
        Sxx = np.random.randn(n_freq, n_time) * 10 + 50
        
        # Mock segment data
        segment_length = int(duration * sample_rate)
        segment = np.random.randn(segment_length) * 0.1
        
        # Get appropriate frequency bands
        freq_bands = get_appropriate_freq_bands(sample_rate, 'land')
        
        # Create mock band anomalies
        band_anomalies = {}
        for band_name, (f_low, f_high) in freq_bands.items():
            # Mock data for this band
            band_power = np.random.randn(n_time) * 5 + 30
            n_windows = 10
            window_starts = np.linspace(start_time + 5, start_time + duration - 5, n_windows)
            mse_errors = np.random.exponential(0.01, n_windows)
            threshold = np.percentile(mse_errors, 85)
            anomaly_mask = mse_errors > threshold
            
            band_anomalies[band_name] = {
                'band_power': band_power,
                'window_starts': window_starts,
                'mse_errors': mse_errors,
                'threshold': threshold,
                'anomaly_mask': anomaly_mask,
                'n_anomalies': np.sum(anomaly_mask),
                'anomaly_rate': np.sum(anomaly_mask) / len(anomaly_mask),
                'freq_range': (f_low, f_high)
            }
        
        # Package mock results
        mock_results = {
            'start_time': start_time,
            'duration': duration,
            'target_sample_rate': sample_rate,
            'spectrogram_f': f,
            'spectrogram_t': t,
            'spectrogram_Sxx': Sxx,
            'freq_bands': freq_bands,
            'band_anomalies': band_anomalies,
            'segment_resampled': segment
        }
        
        print("✓ Mock results created")
        
        # Test the enhanced visualization
        print("Testing enhanced visualization...")
        from enhanced_spectral_visualization import visualize_spectral_anomalies_enhanced
        
        import matplotlib
        matplotlib.use('Agg')  # Ensure non-interactive backend
        
        fig, axes = visualize_spectral_anomalies_enhanced(mock_results, "Debug_Test")
        
        # Close the figure to free memory
        import matplotlib.pyplot as plt
        plt.close(fig)
        
        print("✓ Enhanced visualization test passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced visualization test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all debug tests."""
    
    print("Valencia DAS Debug Test")
    print("=" * 30)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed - check missing dependencies")
        return False
    
    # Test basic functionality
    if not test_basic_functionality():
        print("\n❌ Basic functionality test failed")
        return False
    
    # Test enhanced visualization
    if not test_enhanced_visualization():
        print("\n❌ Enhanced visualization test failed")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 ALL DEBUG TESTS PASSED!")
    print("=" * 50)
    print("The system should be working correctly.")
    print("Try running: python run_test.py quick")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
