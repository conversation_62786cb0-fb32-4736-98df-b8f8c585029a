{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Valencia DAS Interactive Anomaly Detection\n", "\n", "This notebook provides an interactive environment for exploring anomaly detection in Valencia DAS data using Chronos T5 time series forecasting.\n", "\n", "## Features:\n", "- **Real Valencia DAS data loading**\n", "- **Chronos T5 time series prediction**\n", "- **Spectral anomaly detection**\n", "- **Interactive anomaly explorer with zoom/pan**\n", "- **Frequency band analysis**\n", "- **Whitened spectrograms for better anomaly visibility**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import h5py\n", "from pathlib import Path\n", "from scipy.signal import spectrogram, resample\n", "import torch\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Enable interactive plots\n", "%matplotlib widget\n", "# Alternative if widget doesn't work:\n", "# %matplotlib notebook\n", "\n", "# Import our custom modules\n", "from enhanced_spectral_visualization import (\n", "    visualize_spectral_anomalies_enhanced, \n", "    get_appropriate_freq_bands, \n", "    suggest_analysis_parameters,\n", "    compare_frequency_scales,\n", "    compare_whitened_spectrograms,\n", "    create_interactive_anomaly_explorer\n", ")\n", "\n", "print(\"✓ All imports successful\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> Chronos Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try to load Chronos model\n", "try:\n", "    from chronos import ChronosPipeline\n", "    CHRONOS_AVAILABLE = True\n", "    print(\"✓ ChronosPipeline imported successfully\")\n", "except ImportError as e:\n", "    print(f\"❌ Chronos not available: {e}\")\n", "    print(\"To install: pip install chronos-forecasting\")\n", "    CHRONOS_AVAILABLE = False\n", "\n", "# Load Chronos model if available\n", "pipeline = None\n", "if CHRONOS_AVAILABLE:\n", "    try:\n", "        print(\"Loading Chronos model...\")\n", "        device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "        pipeline = ChronosPipeline.from_pretrained(\n", "            \"amazon/chronos-t5-tiny\",\n", "            device_map=device,\n", "            torch_dtype=torch.bfloat16 if device.type == \"cuda\" else torch.float32,\n", "        )\n", "\n", "        print(f\"✓ Chronos model loaded on {device}\")\n", "        print(\"✓ USING REAL CHRONOS MODEL\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed to load Chronos: {e}\")\n", "        print(\"⚠️  Will use mock predictions instead\")\n", "        pipeline = None\n", "else:\n", "    print(\"⚠️  Using mock predictions (Chronos not available)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Define Prediction Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict_chronos(context, prediction_length=32):\n", "    \"\"\"Make prediction using Chronos or enhanced mock.\"\"\"\n", "    if pipeline is None:\n", "        # Enhanced mock prediction\n", "        return mock_prediction(context, prediction_length)\n", "    \n", "    try:\n", "        # Use real Chronos\n", "        context_tensor = torch.tensor(context, dtype=torch.float32).unsqueeze(0)\n", "        forecast = pipeline.predict(\n", "            context_tensor,\n", "            prediction_length=prediction_length,\n", "            num_samples=1\n", "        )\n", "        return forecast[0].cpu().numpy().flatten()\n", "    except Exception as e:\n", "        print(f\"❌ Chronos prediction failed: {e}\")\n", "        return mock_prediction(context, prediction_length)\n", "\n", "def mock_prediction(context, prediction_length):\n", "    \"\"\"Conservative mock prediction that will fail on anomalies.\"\"\"\n", "    if len(context) < 4:\n", "        base_value = context[-1] if len(context) > 0 else 0\n", "        prediction = np.full(prediction_length, base_value)\n", "        prediction += np.random.normal(0, 0.001, prediction_length)\n", "        return prediction\n", "    \n", "    # Use only stable part of context (ignore recent changes)\n", "    stable_context = context[:-max(1, len(context)//4)]\n", "    if len(stable_context) < 2:\n", "        stable_context = context[:2]\n", "    \n", "    # Conservative baseline from stable period\n", "    baseline = np.mean(stable_context)\n", "    \n", "    # Very small trend from stable period\n", "    if len(stable_context) >= 2:\n", "        mid_point = len(stable_context) // 2\n", "        early_mean = np.mean(stable_context[:mid_point]) if mid_point > 0 else stable_context[0]\n", "        late_mean = np.mean(stable_context[mid_point:]) if mid_point < len(stable_context) else stable_context[-1]\n", "        trend = (late_mean - early_mean) / len(stable_context) * 0.1  # Heavily dampened\n", "    else:\n", "        trend = 0\n", "    \n", "    # Generate conservative predictions\n", "    prediction = []\n", "    for i in range(prediction_length):\n", "        decay_factor = 0.8 ** i\n", "        pred_value = baseline + trend * (i + 1) * decay_factor\n", "        prediction.append(pred_value)\n", "    \n", "    prediction = np.array(prediction)\n", "    noise_level = np.std(stable_context) * 0.02 if len(stable_context) > 1 else 0.001\n", "    prediction += np.random.normal(0, noise_level, prediction_length)\n", "    \n", "    return prediction\n", "\n", "print(\"✓ Prediction functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Loading Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_valencia_data(data_dir=\"Data\", max_files=6, target_sensor=None):\n", "    \"\"\"Load Valencia DAS data from H5 files.\"\"\"\n", "    data_dir = Path(data_dir)\n", "    h5_files = sorted(list(data_dir.rglob(\"*.h5\")))\n", "    print(f\"Found {len(h5_files)} H5 files\")\n", "    \n", "    if len(h5_files) == 0:\n", "        raise FileNotFoundError(\"No H5 files found\")\n", "    \n", "    # Determine target sensor\n", "    if target_sensor is None:\n", "        with h5py.File(h5_files[0], 'r') as f:\n", "            das_name = list(f.keys())[0]\n", "            sr_dataset = f[f\"{das_name}/Source1/Zone1/SR_Valencia\"]\n", "            n_sensors = sr_dataset.shape[2]\n", "        target_sensor = n_sensors // 2\n", "        print(f\"Using middle sensor: {target_sensor} (out of {n_sensors} sensors)\")\n", "    \n", "    # Load data from files\n", "    all_data = []\n", "    files_to_load = min(max_files, len(h5_files))\n", "    print(f\"Loading sensor {target_sensor} from {files_to_load} files...\")\n", "    \n", "    for i, file_path in enumerate(h5_files[:files_to_load]):\n", "        try:\n", "            with h5py.File(file_path, 'r') as f:\n", "                das_name = list(f.keys())[0]\n", "                sr_dataset = f[f\"{das_name}/Source1/Zone1/SR_Valencia\"]\n", "                dims = sr_dataset.shape\n", "                nb_block = dims[0]\n", "                sampling_frequency = 250\n", "                \n", "                sensor_data_blocks = []\n", "                for tt in range(nb_block):\n", "                    block_data = sr_dataset[tt, :sampling_frequency, target_sensor]\n", "                    sensor_data_blocks.append(block_data)\n", "                \n", "                sensor_data = np.concatenate(sensor_data_blocks)\n", "                all_data.append(sensor_data.astype(np.float32))\n", "                print(f\"  Loaded {file_path.name}: {len(sensor_data)} samples\")\n", "        except Exception as e:\n", "            print(f\"  Warning: Failed to load {file_path.name}: {e}\")\n", "            continue\n", "    \n", "    if not all_data:\n", "        raise ValueError(\"No data could be loaded\")\n", "    \n", "    # Concatenate all data\n", "    full_data = np.concatenate(all_data)\n", "    total_duration = len(full_data) / 250\n", "    \n", "    print(f\"✓ Loaded {len(all_data)} files successfully\")\n", "    print(f\"✓ Total data: {len(full_data)} samples ({total_duration/3600:.1f} hours)\")\n", "    print(f\"✓ Sensor {target_sensor} ready for analysis\")\n", "    \n", "    return full_data, target_sensor\n", "\n", "def create_synthetic_data():\n", "    \"\"\"Create synthetic DAS data with known anomalies for testing.\"\"\"\n", "    print(\"Creating synthetic Valencia DAS data...\")\n", "    \n", "    duration_hours = 2\n", "    sample_rate = 250\n", "    n_samples = int(duration_hours * 3600 * sample_rate)\n", "    \n", "    t = np.arange(n_samples) / sample_rate\n", "    \n", "    # Base signals\n", "    traffic = 0.1 * np.sin(2 * np.pi * 5 * t) + 0.05 * np.sin(2 * np.pi * 12 * t)\n", "    rumble = 0.2 * np.sin(2 * np.pi * 0.5 * t) + 0.1 * np.sin(2 * np.pi * 1.5 * t)\n", "    \n", "    # Add strong anomalies\n", "    anomaly_events = [\n", "        {'time': 1800, 'duration': 45, 'freq': 8, 'amplitude': 2.0, 'type': 'high_freq_burst'},\n", "        {'time': 3600, 'duration': 60, 'freq': 2, 'amplitude': 3.0, 'type': 'low_freq_surge'}, \n", "        {'time': 5400, 'duration': 30, 'freq': 12, 'amplitude': 1.5, 'type': 'machinery_spike'},\n", "    ]\n", "    \n", "    for event in anomaly_events:\n", "        start_idx = int(event['time'] * sample_rate)\n", "        end_idx = start_idx + int(event['duration'] * sample_rate)\n", "        if end_idx < n_samples:\n", "            if event['type'] == 'high_freq_burst':\n", "                anomaly_signal = event['amplitude'] * np.sin(2 * np.pi * event['freq'] * t[start_idx:end_idx])\n", "            elif event['type'] == 'low_freq_surge':\n", "                ramp = np.linspace(0, 1, end_idx - start_idx)\n", "                anomaly_signal = event['amplitude'] * ramp * np.sin(2 * np.pi * event['freq'] * t[start_idx:end_idx])\n", "            else:  # machinery_spike\n", "                pulse_pattern = np.where(np.sin(2 * np.pi * 0.5 * t[start_idx:end_idx]) > 0, 1, 0)\n", "                anomaly_signal = event['amplitude'] * pulse_pattern * np.sin(2 * np.pi * event['freq'] * t[start_idx:end_idx])\n", "            \n", "            traffic[start_idx:end_idx] += anomaly_signal\n", "    \n", "    synthetic_data = traffic + rumble + np.random.normal(0, 0.02, n_samples)\n", "    \n", "    print(f\"✓ Created {duration_hours} hours of synthetic data\")\n", "    print(f\"✓ Anomaly events:\")\n", "    for event in anomaly_events:\n", "        print(f\"    {event['time']}s: {event['type']} ({event['duration']}s, {event['freq']} Hz)\")\n", "    \n", "    return synthetic_data, 1488\n", "\n", "print(\"✓ Data loading functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Spectral Anomaly Analysis Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_spectral_anomalies(data, start_time_seconds, duration_seconds, \n", "                              target_sample_rate, original_sample_rate=250, \n", "                              freq_bands=None, context_length=64, \n", "                              prediction_length=16, stride=8):\n", "    \"\"\"Analyze spectral anomalies in DAS data.\"\"\"\n", "    \n", "    print(f\"\\n=== Spectral Anomaly Analysis ===\")\n", "    print(f\"Time window: {start_time_seconds}s + {duration_seconds}s\")\n", "    print(f\"Sample rate: {original_sample_rate} Hz → {target_sample_rate} Hz\")\n", "    \n", "    # Get appropriate frequency bands if not provided\n", "    if freq_bands is None:\n", "        freq_bands = get_appropriate_freq_bands(target_sample_rate, 'land')\n", "        print(f\"Using automatic frequency bands: {list(freq_bands.keys())}\")\n", "    \n", "    # Extract and preprocess segment\n", "    start_idx = int(start_time_seconds * original_sample_rate)\n", "    end_idx = int((start_time_seconds + duration_seconds) * original_sample_rate)\n", "    \n", "    if end_idx > len(data):\n", "        end_idx = len(data)\n", "        duration_seconds = (end_idx - start_idx) / original_sample_rate\n", "        print(f\"Truncated to available data: {duration_seconds:.1f}s\")\n", "    \n", "    segment_original = data[start_idx:end_idx]\n", "    \n", "    # Downsample if needed\n", "    if target_sample_rate != original_sample_rate:\n", "        downsample_factor = original_sample_rate / target_sample_rate\n", "        new_length = int(len(segment_original) / downsample_factor)\n", "        segment_resampled = resample(segment_original, new_length)\n", "        print(f\"Downsampled: {len(segment_original)} → {len(segment_resampled)} samples\")\n", "    else:\n", "        segment_resampled = segment_original\n", "        downsample_factor = 1\n", "    \n", "    # Compute spectrogram\n", "    nperseg = min(256, len(segment_resampled) // 8)\n", "    noverlap = nperseg // 2\n", "    f, t, Sxx = spectrogram(segment_resampled, fs=target_sample_rate,\n", "                           nperseg=nperseg, noverlap=noverlap)\n", "    \n", "    Sxx_db = 10 * np.log10(Sxx + 1e-12)\n", "    \n", "    # Whiten the spectrogram\n", "    print(f\"Whitening spectrogram (subtracting frequency-wise mean)...\")\n", "    freq_means = np.mean(Sxx_db, axis=1, keepdims=True)\n", "    Sxx_whitened = Sxx_db - freq_means\n", "    \n", "    print(f\"  Original power range: [{np.min(Sxx_db):.1f}, {np.max(Sxx_db):.1f}] dB\")\n", "    print(f\"  Whitened power range: [{np.min(Sxx_whitened):.1f}, {np.max(Sxx_whitened):.1f}] dB\")\n", "    \n", "    t_spectrogram = t + start_time_seconds\n", "    print(f\"Spectrogram: {len(f)} frequencies × {len(t)} time bins\")\n", "    \n", "    # Analyze each frequency band\n", "    band_anomalies = {}\n", "    \n", "    for band_name, (f_low, f_high) in freq_bands.items():\n", "        print(f\"\\nAnalyzing {band_name} band ({f_low}-{f_high} Hz)...\")\n", "        \n", "        # Find frequency indices\n", "        freq_mask = (f >= f_low) & (f <= f_high)\n", "        if not np.any(freq_mask):\n", "            print(f\"  Warning: No frequencies found in band\")\n", "            continue\n", "        \n", "        # Extract power in this frequency band\n", "        band_power = np.mean(Sxx_whitened[freq_mask, :], axis=0)\n", "        \n", "        # Normalize for prediction\n", "        band_mean = np.mean(band_power)\n", "        band_std = np.std(band_power)\n", "        band_normalized = (band_power - band_mean) / band_std\n", "        \n", "        # Apply prediction-based anomaly detection\n", "        total_length = context_length + prediction_length\n", "        n_windows = (len(band_normalized) - total_length) // stride + 1\n", "        \n", "        window_starts = []\n", "        mse_errors = []\n", "        \n", "        for i in range(n_windows):\n", "            window_start = i * stride\n", "            context_end = window_start + context_length\n", "            target_end = window_start + total_length\n", "            \n", "            if target_end > len(band_normalized):\n", "                break\n", "            \n", "            context = band_normalized[window_start:context_end]\n", "            target = band_normalized[context_end:target_end]\n", "            \n", "            prediction = predict_chronos(context, prediction_length)\n", "            mse_error = np.mean((prediction - target) ** 2)\n", "            \n", "            window_time = t_spectrogram[window_start] if window_start < len(t_spectrogram) else t_spectrogram[-1]\n", "            window_starts.append(window_time)\n", "            mse_errors.append(mse_error)\n", "        \n", "        window_starts = np.array(window_starts)\n", "        mse_errors = np.array(mse_errors)\n", "        \n", "        # Check if we have analysis windows\n", "        if len(mse_errors) == 0:\n", "            print(f\"  Warning: No analysis windows created\")\n", "            band_anomalies[band_name] = {\n", "                'band_power': band_power,\n", "                'window_starts': np.array([]),\n", "                'mse_errors': np.array([]),\n", "                'threshold': 0.0,\n", "                'anomaly_mask': np.array([], dtype=bool),\n", "                'n_anomalies': 0,\n", "                'anomaly_rate': 0.0,\n", "                'freq_range': (f_low, f_high)\n", "            }\n", "            continue\n", "        \n", "        # Detect anomalies\n", "        threshold = np.percentile(mse_errors, 85)\n", "        anomaly_mask = mse_errors > threshold\n", "        n_anomalies = np.sum(anomaly_mask)\n", "        \n", "        # Debug information\n", "        print(f\"  MSE errors: min={np.min(mse_errors):.6f}, max={np.max(mse_errors):.6f}, mean={np.mean(mse_errors):.6f}\")\n", "        print(f\"  Threshold (85th percentile): {threshold:.6f}\")\n", "        print(f\"  Anomalies: {n_anomalies}/{len(mse_errors)} ({100*n_anomalies/len(mse_errors):.1f}%)\")\n", "        \n", "        # Try lower threshold if no anomalies\n", "        if n_anomalies == 0:\n", "            threshold = np.percentile(mse_errors, 75)\n", "            anomaly_mask = mse_errors > threshold\n", "            n_anomalies = np.sum(anomaly_mask)\n", "            print(f\"  Trying 75th percentile: {threshold:.6f}, anomalies: {n_anomalies}\")\n", "        \n", "        band_anomalies[band_name] = {\n", "            'band_power': band_power,\n", "            'band_normalized': band_normalized,\n", "            'normalization': {'mean': band_mean, 'std': band_std},\n", "            'window_starts': window_starts,\n", "            'mse_errors': mse_errors,\n", "            'threshold': threshold,\n", "            'anomaly_mask': anomaly_mask,\n", "            'n_anomalies': n_anomalies,\n", "            'anomaly_rate': n_anomalies / len(mse_errors),\n", "            'freq_range': (f_low, f_high)\n", "        }\n", "        \n", "        print(f\"  {n_anomalies} anomalies ({100*band_anomalies[band_name]['anomaly_rate']:.1f}%)\")\n", "        \n", "        # Show worst prediction errors\n", "        if len(mse_errors) > 0:\n", "            worst_indices = np.argsort(mse_errors)[-3:]\n", "            print(f\"  Worst prediction errors:\")\n", "            for idx in reversed(worst_indices):\n", "                if idx < len(window_starts):\n", "                    error_time = window_starts[idx]\n", "                    error_value = mse_errors[idx]\n", "                    is_anomaly = \"🚨 ANOMALY\" if anomaly_mask[idx] else \"normal\"\n", "                    print(f\"    t={error_time:.1f}s: MSE={error_value:.6f} ({is_anomaly})\")\n", "    \n", "    # Package results\n", "    results = {\n", "        'start_time': start_time_seconds,\n", "        'duration': duration_seconds,\n", "        'target_sample_rate': target_sample_rate,\n", "        'downsample_factor': downsample_factor,\n", "        'spectrogram_f': f,\n", "        'spectrogram_t': t_spectrogram,\n", "        'spectrogram_Sxx': Sxx_whitened,\n", "        'spectrogram_Sxx_original': Sxx_db,\n", "        'freq_bands': freq_bands,\n", "        'band_anomalies': band_anomalies,\n", "        'segment_resampled': segment_resampled\n", "    }\n", "    \n", "    print(f\"\\n✓ Spectral analysis complete\")\n", "    return results\n", "\n", "print(\"✓ Analysis function ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try to load real data first, fall back to synthetic\n", "try:\n", "    print(\"Attempting to load REAL Valencia DAS data...\")\n", "    data, sensor_id = load_valencia_data(max_files=6, target_sensor=None)\n", "    data_type = \"REAL\"\n", "    print(f\"✓ Using REAL data from sensor {sensor_id}\")\n", "except Exception as e:\n", "    print(f\"Failed to load real data: {e}\")\n", "    print(\"Falling back to synthetic data...\")\n", "    data, sensor_id = create_synthetic_data()\n", "    data_type = \"SYNTHETIC\"\n", "    print(f\"✓ Using SYNTHETIC data\")\n", "\n", "print(f\"\\nData loaded: {len(data)} samples ({len(data)/250/3600:.2f} hours)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Configure Analysis Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Choose analysis parameters\n", "sample_rate = 30  # Hz - good compromise for DAS analysis\n", "analysis_duration = 600  # 10 minutes\n", "\n", "# Choose time segment\n", "total_duration = len(data) / 250\n", "print(f\"Total data duration: {total_duration/3600:.1f} hours\")\n", "\n", "if data_type == \"REAL\":\n", "    # For real data, try different interesting time periods\n", "    analysis_options = [\n", "        {\"start\": 3600, \"description\": \"1 hour mark (potential traffic changes)\"},\n", "        {\"start\": 7200, \"description\": \"2 hour mark (potential activity changes)\"},  \n", "        {\"start\": 14400, \"description\": \"4 hour mark (potential environmental changes)\"},\n", "        {\"start\": int(total_duration * 0.3), \"description\": \"30% through dataset\"},\n", "        {\"start\": int(total_duration * 0.7), \"description\": \"70% through dataset\"}\n", "    ]\n", "    \n", "    # Choose first valid option\n", "    analysis_start = None\n", "    for option in analysis_options:\n", "        if option[\"start\"] + analysis_duration < total_duration:\n", "            analysis_start = option[\"start\"]\n", "            description = option[\"description\"]\n", "            break\n", "    \n", "    if analysis_start is None:\n", "        analysis_start = int(total_duration * 0.5)\n", "        description = \"Middle of dataset (fallback)\"\n", "else:\n", "    # For synthetic data, choose segment with known anomaly\n", "    analysis_start = 3500  # Just before 3600s anomaly\n", "    description = \"Around 3600s synthetic anomaly\"\n", "\n", "print(f\"\\nAnalysis configuration:\")\n", "print(f\"  Data type: {data_type}\")\n", "print(f\"  Time segment: {analysis_start}s to {analysis_start + analysis_duration}s\")\n", "print(f\"  Description: {description}\")\n", "print(f\"  Sample rate: {sample_rate} Hz\")\n", "print(f\"  Duration: {analysis_duration} seconds\")\n", "\n", "# Get suggested parameters\n", "suggested_params = suggest_analysis_parameters(sample_rate, analysis_duration, 'land')\n", "print(f\"\\nSuggested analysis parameters:\")\n", "print(f\"  Context length: {suggested_params['context_length']}\")\n", "print(f\"  Prediction length: {suggested_params['prediction_length']}\")\n", "print(f\"  Stride: {suggested_params['stride']}\")\n", "print(f\"  Frequency bands: {list(suggested_params['freq_bands'].keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Run Spectral Anomaly Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the analysis\n", "print(f\"\\n{'='*60}\")\n", "print(f\"RUNNING SPECTRAL ANOMALY ANALYSIS\")\n", "print(f\"{'='*60}\")\n", "\n", "results = analyze_spectral_anomalies(\n", "    data,\n", "    start_time_seconds=analysis_start,\n", "    duration_seconds=analysis_duration,\n", "    target_sample_rate=sample_rate,\n", "    freq_bands=suggested_params['freq_bands'],\n", "    context_length=suggested_params['context_length'],\n", "    prediction_length=suggested_params['prediction_length'],\n", "    stride=suggested_params['stride']\n", ")\n", "\n", "# Summary\n", "total_anomalies = sum([band['n_anomalies'] for band in results['band_anomalies'].values()])\n", "print(f\"\\n🎯 ANALYSIS COMPLETE\")\n", "print(f\"Total anomalies detected: {total_anomalies}\")\n", "print(f\"Frequency bands analyzed: {len(results['band_anomalies'])}\")\n", "\n", "if total_anomalies > 0:\n", "    print(f\"\\nAnomalies by frequency band:\")\n", "    for band_name, band_result in results['band_anomalies'].items():\n", "        n_anom = band_result['n_anomalies']\n", "        if n_anom > 0:\n", "            freq_range = band_result['freq_range']\n", "            print(f\"  {band_name}: {n_anom} anomalies ({freq_range[0]:.1f}-{freq_range[1]:.1f} Hz)\")\n", "else:\n", "    print(f\"\\n⚠️  No anomalies detected. This could mean:\")\n", "    print(f\"   - The data is very regular (normal for DAS)\")\n", "    print(f\"   - The detection threshold is too high\")\n", "    print(f\"   - The prediction model is too adaptive\")\n", "    print(f\"   - Try a different time segment or parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Enhanced Static Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create enhanced static visualization with log frequency scale\n", "print(\"Creating enhanced visualization with log frequency scale...\")\n", "fig, axes = visualize_spectral_anomalies_enhanced(\n", "    results, \n", "    f\"{data_type}_Data_Analysis\",\n", "    log_freq=True\n", ")\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Frequency Scale Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare linear vs log frequency scales\n", "print(\"Creating frequency scale comparison...\")\n", "fig_comp, axes_comp = compare_frequency_scales(results, f\"{data_type}_Scale_Comparison\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. <PERSON><PERSON> Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare original vs whitened spectrograms\n", "print(\"Creating whitening comparison...\")\n", "fig_white, axes_white = compare_whitened_spectrograms(results, f\"{data_type}_Whitening_Comparison\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. 🎯 Interactive Anomaly Explorer\n", "\n", "**This is the main interactive tool for exploring anomalies in detail!**\n", "\n", "### Controls:\n", "- **Click and drag** on the main time series plot to zoom to a time range\n", "- **Press 'r'** to reset zoom to full range\n", "- **Anomalies** are marked with dashed vertical lines and colored dots\n", "- **Each color** represents a different frequency band\n", "- **Console output** shows anomalies in selected time ranges"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Launch the interactive anomaly explorer\n", "print(\"\\n🚀 LAUNCHING INTERACTIVE ANOMALY EXPLORER\")\n", "print(\"=\" * 50)\n", "\n", "fig_interactive, axes_interactive, anomaly_info = create_interactive_anomaly_explorer(\n", "    results, f\"{data_type}_Interactive_Explorer\"\n", ")\n", "\n", "# The interactive plot will appear above this cell\n", "# Use the controls described in the markdown cell above"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 13. Anomaly Details and Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Detailed analysis of detected anomalies\n", "if anomaly_info:\n", "    print(f\"\\n📊 DETAILED ANOMALY ANALYSIS\")\n", "    print(f\"=\" * 40)\n", "    \n", "    for i, anom in enumerate(anomaly_info):\n", "        print(f\"\\nAnomaly #{i+1}:\")\n", "        print(f\"  Time: {anom['time']:.1f} seconds\")\n", "        print(f\"  Frequency band: {anom['band']}\")\n", "        print(f\"  Frequency range: {anom['freq_range'][0]:.1f}-{anom['freq_range'][1]:.1f} Hz\")\n", "        \n", "        # Get the band result for more details\n", "        band_result = results['band_anomalies'][anom['band']]\n", "        \n", "        # Find the specific anomaly in the band results\n", "        anomaly_times = band_result['window_starts'][band_result['anomaly_mask']]\n", "        if len(anomaly_times) > 0:\n", "            # Find closest anomaly time\n", "            closest_idx = np.argmin(np.abs(anomaly_times - anom['time']))\n", "            if closest_idx < len(band_result['mse_errors'][band_result['anomaly_mask']]):\n", "                mse_errors_anom = band_result['mse_errors'][band_result['anomaly_mask']]\n", "                mse_value = mse_errors_anom[closest_idx]\n", "                threshold = band_result['threshold']\n", "                print(f\"  MSE error: {mse_value:.6f} (threshold: {threshold:.6f})\")\n", "                print(f\"  Severity: {mse_value/threshold:.2f}x threshold\")\n", "    \n", "    # Summary statistics\n", "    print(f\"\\n📈 SUMMARY STATISTICS\")\n", "    print(f\"=\" * 30)\n", "    \n", "    # Group anomalies by frequency band\n", "    band_counts = {}\n", "    for anom in anomaly_info:\n", "        band = anom['band']\n", "        if band not in band_counts:\n", "            band_counts[band] = 0\n", "        band_counts[band] += 1\n", "    \n", "    print(f\"Anomalies by frequency band:\")\n", "    for band, count in band_counts.items():\n", "        freq_range = results['band_anomalies'][band]['freq_range']\n", "        print(f\"  {band}: {count} anomalies ({freq_range[0]:.1f}-{freq_range[1]:.1f} Hz)\")\n", "    \n", "    # Time distribution\n", "    times = [anom['time'] for anom in anomaly_info]\n", "    if len(times) > 1:\n", "        time_span = max(times) - min(times)\n", "        print(f\"\\nTemporal distribution:\")\n", "        print(f\"  First anomaly: {min(times):.1f}s\")\n", "        print(f\"  Last anomaly: {max(times):.1f}s\")\n", "        print(f\"  Time span: {time_span:.1f}s\")\n", "        print(f\"  Average interval: {time_span/(len(times)-1):.1f}s\")\n", "\n", "else:\n", "    print(f\"\\n⚠️  No anomalies detected for detailed analysis\")\n", "    print(f\"\\nTroubleshooting suggestions:\")\n", "    print(f\"1. Try a different time segment\")\n", "    print(f\"2. Adjust detection thresholds (lower percentile)\")\n", "    print(f\"3. Use different analysis parameters\")\n", "    print(f\"4. Check if Chronos model is working properly\")\n", "    \n", "    if pipeline is None:\n", "        print(f\"\\n🔧 Chronos model not loaded - using mock predictions\")\n", "        print(f\"   Install Chronos for better anomaly detection:\")\n", "        print(f\"   pip install chronos-forecasting\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 14. Next Steps and Experimentation\n", "\n", "### Try Different Parameters:\n", "- **Different time segments**: Change `analysis_start` in cell 7\n", "- **Different sample rates**: Try 10, 25, or 50 Hz\n", "- **Different durations**: Try 300s (5 min) or 1200s (20 min)\n", "\n", "### Experiment with Detection:\n", "- **Lower thresholds**: Modify percentile from 85 to 75 or 70\n", "- **Different context lengths**: Try 32, 128, or 256\n", "- **Different prediction lengths**: Try 8, 32, or 64\n", "\n", "### Advanced Analysis:\n", "- **Multiple sensors**: Load different sensor indices\n", "- **Cross-sensor correlation**: Compare anomalies across sensors\n", "- **Temporal patterns**: Look for recurring anomalies\n", "- **Environmental correlation**: Match anomalies to external factors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Experimental cell - modify parameters and re-run analysis\n", "print(\"\\n🧪 EXPERIMENTAL ANALYSIS\")\n", "print(\"Modify the parameters below and re-run this cell to experiment:\")\n", "\n", "# Experimental parameters - modify these!\n", "exp_start_time = 7200  # Try different start time\n", "exp_duration = 300     # Try different duration\n", "exp_sample_rate = 25   # Try different sample rate\n", "exp_threshold_percentile = 75  # Try lower threshold\n", "\n", "print(f\"\\nExperimental parameters:\")\n", "print(f\"  Start time: {exp_start_time}s\")\n", "print(f\"  Duration: {exp_duration}s\")\n", "print(f\"  Sample rate: {exp_sample_rate} Hz\")\n", "print(f\"  Threshold percentile: {exp_threshold_percentile}\")\n", "\n", "# Check if parameters are valid\n", "if exp_start_time + exp_duration < len(data) / 250:\n", "    print(f\"\\n✓ Parameters valid, running experimental analysis...\")\n", "    \n", "    # Get parameters for experimental analysis\n", "    exp_params = suggest_analysis_parameters(exp_sample_rate, exp_duration, 'land')\n", "    \n", "    # Run experimental analysis\n", "    exp_results = analyze_spectral_anomalies(\n", "        data,\n", "        start_time_seconds=exp_start_time,\n", "        duration_seconds=exp_duration,\n", "        target_sample_rate=exp_sample_rate,\n", "        freq_bands=exp_params['freq_bands'],\n", "        context_length=exp_params['context_length'],\n", "        prediction_length=exp_params['prediction_length'],\n", "        stride=exp_params['stride']\n", "    )\n", "    \n", "    # Quick visualization\n", "    exp_fig, exp_axes = visualize_spectral_anomalies_enhanced(\n", "        exp_results, f\"Experimental_{data_type}_Analysis\", log_freq=True\n", "    )\n", "    plt.show()\n", "    \n", "    # Launch experimental interactive explorer\n", "    exp_fig_int, exp_axes_int, exp_anomaly_info = create_interactive_anomaly_explorer(\n", "        exp_results, f\"Experimental_{data_type}_Explorer\"\n", "    )\n", "    \n", "else:\n", "    print(f\"\\n❌ Invalid parameters - time segment exceeds available data\")\n", "    print(f\"   Available data: {len(data)/250:.1f} seconds\")\n", "    print(f\"   Requested: {exp_start_time + exp_duration} seconds\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}