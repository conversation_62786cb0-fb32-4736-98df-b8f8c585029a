{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Valencia-Mallorca DAS Anomaly Detection with Chronos\n", "\n", "**Dataset**: Valencia-Islalink fiber-optic cable (50km, 2977 channels)\n", "- **Land segment**: 0-9.2km (channels 0-547) - traffic noise\n", "- **Sea segment**: 9.2-50km (channels 548-2976) - marine gravity waves, microseisms\n", "- **Sampling**: 250 Hz, 16.8m spatial resolution, 10-minute files\n", "\n", "**Enhanced analysis approach**:\n", "1. Geographic-aware channel selection (land/sea transition focus)\n", "2. Multi-channel comparative analysis\n", "3. Time-of-day pattern analysis\n", "4. Multi-scale temporal anomaly detection with Chronos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import h5py\n", "from pathlib import Path\n", "from scipy.signal import spectrogram, resample\n", "import torch\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add src directory\n", "sys.path.append('src')\n", "from chronos import ChronosPipeline\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (18, 12)\n", "plt.rcParams['font.size'] = 11\n", "\n", "print(\"✓ Libraries imported\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Valencia Cable Geographic Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Valencia Cable Geographic Configuration\n", "VALENCIA_CONFIG = {\n", "    'total_length_km': 50.0,\n", "    'total_channels': 2977,\n", "    'spatial_resolution_m': 16.8,\n", "    'land_length_km': 9.189,\n", "    'sea_length_km': 40.811,\n", "    'land_channels': int(9189 / 16.8),  # ~547 channels\n", "    'sampling_rate': 250,\n", "    'file_duration_min': 10\n", "}\n", "\n", "# Define geographic segments\n", "SEGMENTS = {\n", "    'land': {'start': 0, 'end': VALENCIA_CONFIG['land_channels'], 'description': 'Land (traffic noise)'},\n", "    'transition': {'start': VALENCIA_CONFIG['land_channels']-50, 'end': VALENCIA_CONFIG['land_channels']+200, 'description': 'Land-sea transition'},\n", "    'shallow_sea': {'start': VALENCIA_CONFIG['land_channels'], 'end': 1200, 'description': 'Shallow sea'},\n", "    'deep_sea': {'start': 1200, 'end': VALENCIA_CONFIG['total_channels'], 'description': 'Deep sea (microseisms)'}\n", "}\n", "\n", "# Time periods for analysis\n", "TIME_PERIODS = {\n", "    'night': {'start_hour': 2, 'end_hour': 6, 'description': 'Night (minimal traffic)'},\n", "    'morning': {'start_hour': 8, 'end_hour': 10, 'description': 'Morning (rush hour)'},\n", "    'afternoon': {'start_hour': 14, 'end_hour': 16, 'description': 'Afternoon (active)'},\n", "    'evening': {'start_hour': 20, 'end_hour': 22, 'description': 'Evening (moderate traffic)'}\n", "}\n", "\n", "print(f\"Valencia Cable Configuration:\")\n", "print(f\"  Total: {VALENCIA_CONFIG['total_length_km']}km, {VALENCIA_CONFIG['total_channels']} channels\")\n", "print(f\"  Land: 0-{VALENCIA_CONFIG['land_length_km']:.1f}km (channels 0-{VALENCIA_CONFIG['land_channels']})\")\n", "print(f\"  Sea: {VALENCIA_CONFIG['land_length_km']:.1f}-{VALENCIA_CONFIG['total_length_km']}km (channels {VALENCIA_CONFIG['land_channels']}-{VALENCIA_CONFIG['total_channels']})\")\n", "print(f\"  Spatial resolution: {VALENCIA_CONFIG['spatial_resolution_m']}m\")\n", "\n", "def get_channel_info(channel_idx):\n", "    \"\"\"Get geographic information for a channel.\"\"\"\n", "    distance_km = channel_idx * VALENCIA_CONFIG['spatial_resolution_m'] / 1000\n", "    \n", "    if channel_idx < VALENCIA_CONFIG['land_channels']:\n", "        segment = 'land'\n", "        environment = 'Land (traffic/urban noise)'\n", "    elif channel_idx < 1200:\n", "        segment = 'shallow_sea'\n", "        environment = 'Shallow sea (transition zone)'\n", "    else:\n", "        segment = 'deep_sea'\n", "        environment = 'Deep sea (marine waves/microseisms)'\n", "    \n", "    return {\n", "        'channel': channel_idx,\n", "        'distance_km': distance_km,\n", "        'segment': segment,\n", "        'environment': environment\n", "    }\n", "\n", "def get_representative_channels():\n", "    \"\"\"Get representative channels across different segments.\"\"\"\n", "    channels = {\n", "        'land_start': 100,  # Early land\n", "        'land_end': VALENCIA_CONFIG['land_channels'] - 50,  # Late land\n", "        'transition': VALENCIA_CONFIG['land_channels'] + 50,  # Just offshore\n", "        'shallow_sea': 800,  # Shallow water\n", "        'mid_sea': 1500,  # Mid-depth\n", "        'deep_sea': 2200,  # Deep water\n", "        'far_sea': 2700   # Far offshore\n", "    }\n", "    \n", "    print(\"\\nRepresentative channels:\")\n", "    for name, ch in channels.items():\n", "        info = get_channel_info(ch)\n", "        print(f\"  {name}: ch{ch} ({info['distance_km']:.1f}km) - {info['environment']}\")\n", "    \n", "    return channels\n", "\n", "representative_channels = get_representative_channels()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Enhanced Data Loading Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_multiple_sensors_from_file(file_path, sensor_indices):\n", "    \"\"\"Load multiple sensors from one H5 file.\"\"\"\n", "    with h5py.File(file_path, 'r') as f:\n", "        das_name = list(f.keys())[0]\n", "        zone_path = f\"{das_name}/Source1/Zone1/\"\n", "        sr_dataset_path = f\"{zone_path}SR_Valencia\"\n", "        \n", "        if sr_dataset_path not in f:\n", "            raise ValueError(f\"Could not find strain rate data at {sr_dataset_path}\")\n", "        \n", "        sr_dataset = f[sr_dataset_path]\n", "        dims = sr_dataset.shape\n", "        nb_block = dims[0]\n", "        sampling_frequency = 250\n", "        \n", "        # Extract data for all requested sensors\n", "        sensors_data = {}\n", "        for sensor_idx in sensor_indices:\n", "            sensor_data_blocks = []\n", "            for tt in range(nb_block):\n", "                block_data = sr_dataset[tt, :sampling_frequency, sensor_idx]\n", "                sensor_data_blocks.append(block_data)\n", "            \n", "            sensors_data[sensor_idx] = np.concatenate(sensor_data_blocks).astype(np.float32)\n", "        \n", "    return sensors_data\n", "\n", "def load_multi_channel_data(data_dir, channels_dict, max_files=144):\n", "    \"\"\"\n", "    Load data from multiple channels across time.\n", "    \n", "    Parameters:\n", "    -----------\n", "    data_dir : Path\n", "        Directory containing H5 files\n", "    channels_dict : dict\n", "        Dictionary of channel names and indices\n", "    max_files : int\n", "        Maximum files to load\n", "    \"\"\"\n", "    h5_files = sorted(list(data_dir.rglob(\"*.h5\")))\n", "    print(f\"Found {len(h5_files)} H5 files\")\n", "    \n", "    if len(h5_files) == 0:\n", "        raise ValueError(\"No H5 files found\")\n", "    \n", "    channel_indices = list(channels_dict.values())\n", "    channel_names = list(channels_dict.keys())\n", "    \n", "    print(f\"Loading {len(channel_indices)} channels: {channel_names}\")\n", "    \n", "    # Initialize data storage\n", "    all_data = {name: [] for name in channel_names}\n", "    file_info = []\n", "    \n", "    files_to_load = min(max_files, len(h5_files))\n", "    print(f\"Loading from {files_to_load} files...\")\n", "    \n", "    for i, file_path in enumerate(h5_files[:files_to_load]):\n", "        try:\n", "            sensors_data = load_multiple_sensors_from_file(file_path, channel_indices)\n", "            \n", "            # Store data by channel name\n", "            for name, ch_idx in channels_dict.items():\n", "                all_data[name].append(sensors_data[ch_idx])\n", "            \n", "            duration = len(sensors_data[channel_indices[0]]) / 250\n", "            file_info.append({\n", "                'filename': file_path.name,\n", "                'duration_seconds': duration,\n", "                'samples': len(sensors_data[channel_indices[0]])\n", "            })\n", "            \n", "            if (i + 1) % 12 == 0:  # Progress every 2 hours\n", "                print(f\"  Loaded {i+1}/{files_to_load} files ({(i+1)*10/60:.1f} hours)\")\n", "                \n", "        except Exception as e:\n", "            print(f\"  Warning: Failed to load {file_path.name}: {e}\")\n", "            continue\n", "    \n", "    # Concatenate all data\n", "    full_data = {}\n", "    for name in channel_names:\n", "        if all_data[name]:\n", "            full_data[name] = np.concatenate(all_data[name])\n", "        else:\n", "            print(f\"Warning: No data loaded for channel {name}\")\n", "    \n", "    total_duration = len(full_data[channel_names[0]]) / 250 if full_data else 0\n", "    \n", "    print(f\"\\n✓ Loaded {len(file_info)} files successfully\")\n", "    print(f\"✓ Total data: {len(full_data[channel_names[0]])} samples ({total_duration/3600:.1f} hours)\")\n", "    print(f\"✓ {len(channel_names)} channels ready for analysis\")\n", "    \n", "    return full_data, file_info\n", "\n", "print(\"✓ Enhanced data loading functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Load Representative Channels Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data from representative channels\n", "data_dir = Path(\"Data\")\n", "multi_channel_data, file_info = load_multi_channel_data(data_dir, representative_channels, max_files=144)\n", "\n", "print(f\"\\nData summary for each channel:\")\n", "for name, data in multi_channel_data.items():\n", "    ch_info = get_channel_info(representative_channels[name])\n", "    print(f\"  {name}: ch{representative_channels[name]} ({ch_info['distance_km']:.1f}km)\")\n", "    print(f\"    Duration: {len(data)/250/3600:.1f} hours\")\n", "    print(f\"    Range: [{np.min(data):.2f}, {np.max(data):.2f}]\")\n", "    print(f\"    Environment: {ch_info['environment']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Initialize Chronos Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Chronos model\n", "print(\"Loading Chronos model...\")\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "pipeline = ChronosPipeline.from_pretrained(\n", "    \"amazon/chronos-t5-tiny\",\n", "    device_map=device,\n", "    torch_dtype=torch.bfloat16 if device.type == \"cuda\" else torch.float32,\n", ")\n", "\n", "print(f\"✓ Chronos model loaded on {device}\")\n", "\n", "def predict_chronos(context, prediction_length=32):\n", "    \"\"\"Make prediction using Chronos.\"\"\"\n", "    try:\n", "        context_tensor = torch.tensor(context, dtype=torch.float32).unsqueeze(0)\n", "        forecast = pipeline.predict(\n", "            context_tensor,\n", "            prediction_length=prediction_length,\n", "            num_samples=1\n", "        )\n", "        return forecast[0].cpu().numpy().flatten()\n", "    except Exception as e:\n", "        print(f\"Prediction failed: {e}\")\n", "        # Fallback: simple continuation\n", "        return np.full(prediction_length, context[-1]) + np.random.normal(0, 0.01, prediction_length)\n", "\n", "print(\"✓ Prediction function ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Enhanced Multi-Channel Analysis Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_multi_channel_segment(data_dict, start_time_seconds, duration_seconds, target_sample_rate,\n", "                                 original_sample_rate=250, context_length=128, prediction_length=32, stride=16):\n", "    \"\"\"\n", "    Analyze multiple channels simultaneously for comparative anomaly detection.\n", "    \n", "    Parameters:\n", "    -----------\n", "    data_dict : dict\n", "        Dictionary of channel_name: data_array\n", "    start_time_seconds : float\n", "        Start time in seconds from beginning of data\n", "    duration_seconds : float\n", "        Duration of segment to analyze\n", "    target_sample_rate : float\n", "        Target sample rate for analysis (Hz)\n", "    \n", "    Returns:\n", "    --------\n", "    dict : Analysis results for all channels\n", "    \"\"\"\n", "    print(f\"\\n=== Multi-Channel Analysis: {duration_seconds}s starting at {start_time_seconds}s ===\")\n", "    print(f\"Target sample rate: {target_sample_rate} Hz\")\n", "    print(f\"Analyzing {len(data_dict)} channels: {list(data_dict.keys())}\")\n", "    \n", "    results = {}\n", "    \n", "    for channel_name, data in data_dict.items():\n", "        print(f\"\\n--- Processing {channel_name} ---\")\n", "        \n", "        # Extract segment\n", "        start_idx = int(start_time_seconds * original_sample_rate)\n", "        end_idx = int((start_time_seconds + duration_seconds) * original_sample_rate)\n", "        \n", "        if end_idx > len(data):\n", "            end_idx = len(data)\n", "            duration_seconds = (end_idx - start_idx) / original_sample_rate\n", "        \n", "        segment_original = data[start_idx:end_idx]\n", "        \n", "        # Downsample if needed\n", "        if target_sample_rate != original_sample_rate:\n", "            downsample_factor = original_sample_rate / target_sample_rate\n", "            new_length = int(len(segment_original) / downsample_factor)\n", "            segment_resampled = resample(segment_original, new_length)\n", "        else:\n", "            segment_resampled = segment_original\n", "            downsample_factor = 1\n", "        \n", "        # Normalize\n", "        segment_mean = np.mean(segment_resampled)\n", "        segment_std = np.std(segment_resampled)\n", "        segment_normalized = (segment_resampled - segment_mean) / segment_std\n", "        \n", "        # Time axis\n", "        time_axis = np.arange(len(segment_normalized)) / target_sample_rate + start_time_seconds\n", "        \n", "        # Anomaly detection\n", "        total_length = context_length + prediction_length\n", "        n_windows = (len(segment_normalized) - total_length) // stride + 1\n", "        \n", "        window_starts = []\n", "        mse_errors = []\n", "        \n", "        for i in range(n_windows):\n", "            window_start = i * stride\n", "            context_end = window_start + context_length\n", "            target_end = window_start + total_length\n", "            \n", "            if target_end > len(segment_normalized):\n", "                break\n", "            \n", "            context = segment_normalized[window_start:context_end]\n", "            target = segment_normalized[context_end:target_end]\n", "            \n", "            prediction = predict_chronos(context, prediction_length)\n", "            mse_error = np.mean((prediction - target) ** 2)\n", "            \n", "            original_window_start = start_idx + int(window_start * downsample_factor)\n", "            window_starts.append(original_window_start)\n", "            mse_errors.append(mse_error)\n", "        \n", "        window_starts = np.array(window_starts)\n", "        mse_errors = np.array(mse_errors)\n", "        \n", "        # Detect anomalies\n", "        threshold = np.percentile(mse_errors, 90)\n", "        anomaly_mask = mse_errors > threshold\n", "        n_anomalies = np.sum(anomaly_mask)\n", "        \n", "        # Compute spectrogram\n", "        nperseg = min(512, len(segment_normalized) // 8)\n", "        f, t, Sxx = spectrogram(segment_normalized, fs=target_sample_rate, \n", "                               nperseg=nperseg, noverlap=nperseg//2)\n", "        Sxx_db = 10 * np.log10(Sxx + 1e-12)\n", "        t_spectrogram = t + start_time_seconds\n", "        \n", "        # Store results\n", "        results[channel_name] = {\n", "            'channel_info': get_channel_info(representative_channels[channel_name]),\n", "            'start_time': start_time_seconds,\n", "            'duration': duration_seconds,\n", "            'target_sample_rate': target_sample_rate,\n", "            'downsample_factor': downsample_factor,\n", "            'time_axis': time_axis,\n", "            'segment_normalized': segment_normalized,\n", "            'normalization': {'mean': segment_mean, 'std': segment_std},\n", "            'spectrogram_f': f,\n", "            'spectrogram_t': t_spectrogram,\n", "            'spectrogram_Sxx': Sxx_db,\n", "            'window_starts': window_starts,\n", "            'mse_errors': mse_errors,\n", "            'threshold': threshold,\n", "            'anomaly_mask': anomaly_mask,\n", "            'n_anomalies': n_anomalies,\n", "            'anomaly_rate': n_anomalies / len(mse_errors) if len(mse_errors) > 0 else 0\n", "        }\n", "        \n", "        print(f\"  {channel_name}: {n_anomalies} anomalies ({100*results[channel_name]['anomaly_rate']:.1f}%)\")\n", "    \n", "    print(f\"\\n✓ Multi-channel analysis complete\")\n", "    return results\n", "\n", "print(\"✓ Multi-channel analysis function ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Enhanced Multi-Channel Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_multi_channel_analysis(results, focus_channels=None):\n", "    \"\"\"\n", "    Create comprehensive visualization comparing multiple channels.\n", "    \n", "    Parameters:\n", "    -----------\n", "    results : dict\n", "        Results from analyze_multi_channel_segment\n", "    focus_channels : list, optional\n", "        List of channel names to focus on (default: all)\n", "    \"\"\"\n", "    if focus_channels is None:\n", "        focus_channels = list(results.keys())\n", "    \n", "    n_channels = len(focus_channels)\n", "    fig, axes = plt.subplots(3, n_channels, figsize=(6*n_channels, 15))\n", "    \n", "    if n_channels == 1:\n", "        axes = axes.reshape(-1, 1)\n", "    \n", "    # Get common parameters\n", "    first_result = results[focus_channels[0]]\n", "    start_time = first_result['start_time']\n", "    duration = first_result['duration']\n", "    sample_rate = first_result['target_sample_rate']\n", "    \n", "    fig.suptitle(f'Valencia DAS Multi-Channel Anomaly Analysis\\n'\n", "                f'{start_time:.0f}-{start_time+duration:.0f}s ({duration:.0f}s) at {sample_rate} Hz', \n", "                fontsize=16)\n", "    \n", "    for col, channel_name in enumerate(focus_channels):\n", "        result = results[channel_name]\n", "        ch_info = result['channel_info']\n", "        \n", "        # Column title\n", "        col_title = f\"{channel_name}\\nCh{ch_info['channel']} ({ch_info['distance_km']:.1f}km)\\n{ch_info['environment']}\"\n", "        \n", "        # 1. Time series with anomalies\n", "        ax1 = axes[0, col]\n", "        ax1.plot(result['time_axis'], result['segment_normalized'], 'b-', alpha=0.7, linewidth=0.8)\n", "        \n", "        # Highlight anomalous regions\n", "        for i, is_anomaly in enumerate(result['anomaly_mask']):\n", "            if is_anomaly:\n", "                window_start_time = result['window_starts'][i] / 250\n", "                window_duration = (128 + 32) * result['downsample_factor'] / 250\n", "                window_end_time = window_start_time + window_duration\n", "                ax1.axvspan(window_start_time, window_end_time, alpha=0.3, color='red')\n", "        \n", "        ax1.set_title(f'{col_title}\\nAnomalies: {result[\"n_anomalies\"]} ({100*result[\"anomaly_rate\"]:.1f}%)')\n", "        ax1.set_xlabel('Time (s)')\n", "        ax1.set_ylabel('Normalized Strain Rate')\n", "        ax1.grid(True, alpha=0.3)\n", "        \n", "        # 2. Spectrogram\n", "        ax2 = axes[1, col]\n", "        im = ax2.pcolormesh(result['spectrogram_t'], result['spectrogram_f'], \n", "                           result['spectrogram_Sxx'], shading='gouraud', cmap='viridis')\n", "        ax2.set_title('Spectrogram')\n", "        ax2.set_xlabel('Time (s)')\n", "        ax2.set_ylabel('Frequency (Hz)')\n", "        ax2.set_ylim([0, min(50, sample_rate/2)])\n", "        \n", "        # Mark anomalies on spectrogram\n", "        for i, is_anomaly in enumerate(result['anomaly_mask']):\n", "            if is_anomaly:\n", "                window_start_time = result['window_starts'][i] / 250\n", "                ax2.axvline(window_start_time, color='red', alpha=0.7, linewidth=1)\n", "        \n", "        # Add colorbar only for last column\n", "        if col == n_channels - 1:\n", "            plt.colorbar(im, ax=ax2, label='Power (dB)')\n", "        \n", "        # 3. MSE errors\n", "        ax3 = axes[2, col]\n", "        error_times = result['window_starts'] / 250\n", "        ax3.plot(error_times, result['mse_errors'], 'g-', linewidth=1.5, alpha=0.8)\n", "        ax3.axhline(result['threshold'], color='red', linestyle='--', alpha=0.7, \n", "                   label=f'Threshold: {result[\"threshold\"]:.4f}')\n", "        ax3.scatter(error_times[result['anomaly_mask']], \n", "                   result['mse_errors'][result['anomaly_mask']], \n", "                   color='red', s=30, zorder=5, alpha=0.8, label='Anomalies')\n", "        \n", "        ax3.set_title('Prediction MSE Errors')\n", "        ax3.set_xlabel('Time (s)')\n", "        ax3.set_ylabel('MSE Error')\n", "        ax3.set_yscale('log')\n", "        ax3.legend(fontsize=9)\n", "        ax3.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # Save plot\n", "    filename = f'valencia_multi_channel_{start_time:.0f}s_{duration:.0f}s_{sample_rate:.0f}Hz.png'\n", "    plt.savefig(filename, dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(f\"\\n✓ Multi-channel visualization saved as: {filename}\")\n", "    \n", "    # Print comparative summary\n", "    print(f\"\\n=== Multi-Channel Analysis Summary ===\")\n", "    print(f\"Time window: {start_time:.0f}-{start_time+duration:.0f}s ({duration:.0f}s)\")\n", "    print(f\"Sample rate: {sample_rate} Hz\")\n", "    print(f\"\\nChannel comparison:\")\n", "    \n", "    for channel_name in focus_channels:\n", "        result = results[channel_name]\n", "        ch_info = result['channel_info']\n", "        print(f\"  {channel_name}: ch{ch_info['channel']} ({ch_info['distance_km']:.1f}km)\")\n", "        print(f\"    Environment: {ch_info['environment']}\")\n", "        print(f\"    Anomalies: {result['n_anomalies']} ({100*result['anomaly_rate']:.1f}%)\")\n", "        print(f\"    MSE range: [{np.min(result['mse_errors']):.6f}, {np.max(result['mse_errors']):.6f}]\")\n", "\n", "print(\"✓ Multi-channel visualization function ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Time-of-Day Analysis Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_time_periods(data_dict, period_name, duration_minutes=30, target_sample_rate=10, \n", "                        focus_channels=['land_end', 'transition', 'shallow_sea']):\n", "    \"\"\"\n", "    Analyze specific time periods (night, morning, afternoon, evening).\n", "    \n", "    Parameters:\n", "    -----------\n", "    data_dict : dict\n", "        Multi-channel data dictionary\n", "    period_name : str\n", "        Time period name ('night', 'morning', 'afternoon', 'evening')\n", "    duration_minutes : float\n", "        Duration to analyze in minutes\n", "    target_sample_rate : float\n", "        Target sample rate for analysis\n", "    focus_channels : list\n", "        Channels to focus on for this analysis\n", "    \"\"\"\n", "    if period_name not in TIME_PERIODS:\n", "        raise ValueError(f\"Unknown period: {period_name}. Choose from {list(TIME_PERIODS.keys())}\")\n", "    \n", "    period_info = TIME_PERIODS[period_name]\n", "    start_time_seconds = period_info['start_hour'] * 3600\n", "    duration_seconds = duration_minutes * 60\n", "    \n", "    print(f\"\\n{'='*80}\")\n", "    print(f\"TIME PERIOD ANALYSIS: {period_name.upper()}\")\n", "    print(f\"Period: {period_info['description']}\")\n", "    print(f\"Time: {period_info['start_hour']:02d}:00-{period_info['end_hour']:02d}:00\")\n", "    print(f\"Analysis window: {duration_minutes} minutes at {target_sample_rate} Hz\")\n", "    print(f\"Focus channels: {focus_channels}\")\n", "    print(f\"{'='*80}\")\n", "    \n", "    # Filter data to focus channels\n", "    focus_data = {ch: data_dict[ch] for ch in focus_channels if ch in data_dict}\n", "    \n", "    # Run analysis\n", "    results = analyze_multi_channel_segment(\n", "        focus_data, \n", "        start_time_seconds, \n", "        duration_seconds, \n", "        target_sample_rate\n", "    )\n", "    \n", "    # Visualize\n", "    visualize_multi_channel_analysis(results, focus_channels)\n", "    \n", "    return results\n", "\n", "print(\"✓ Time-of-day analysis function ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Example <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 1: Land-Sea Transition Analysis (High Resolution)\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"EXAMPLE 1: Land-Sea Transition Zone Analysis\")\n", "print(\"Focus: Comparing land vs sea characteristics\")\n", "print(\"=\"*80)\n", "\n", "transition_channels = ['land_end', 'transition', 'shallow_sea']\n", "transition_data = {ch: multi_channel_data[ch] for ch in transition_channels}\n", "\n", "results_transition = analyze_multi_channel_segment(\n", "    transition_data,\n", "    start_time_seconds=3600,  # 1 hour in\n", "    duration_seconds=300,     # 5 minutes\n", "    target_sample_rate=25,    # High resolution\n", "    context_length=128,       # ~5 seconds context\n", "    prediction_length=32,     # ~1.3 seconds prediction\n", "    stride=16                 # ~0.6 seconds stride\n", ")\n", "\n", "visualize_multi_channel_analysis(results_transition, transition_channels)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 2: Deep Sea Multi-Scale Analysis\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"EXAMPLE 2: Deep Sea Multi-Scale Analysis\")\n", "print(\"Focus: Marine microseisms and gravity waves\")\n", "print(\"=\"*80)\n", "\n", "deep_sea_channels = ['shallow_sea', 'mid_sea', 'deep_sea', 'far_sea']\n", "deep_sea_data = {ch: multi_channel_data[ch] for ch in deep_sea_channels}\n", "\n", "results_deep_sea = analyze_multi_channel_segment(\n", "    deep_sea_data,\n", "    start_time_seconds=7200,  # 2 hours in\n", "    duration_seconds=1800,    # 30 minutes\n", "    target_sample_rate=5,     # Lower resolution for long-term patterns\n", "    context_length=128,       # ~25 seconds context\n", "    prediction_length=32,     # ~6 seconds prediction\n", "    stride=16                 # ~3 seconds stride\n", ")\n", "\n", "visualize_multi_channel_analysis(results_deep_sea, deep_sea_channels)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 3: Time-of-Day Comparative Analysis\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"EXAMPLE 3: Time-of-Day Comparative Analysis\")\n", "print(\"Focus: Traffic patterns and environmental changes\")\n", "print(\"=\"*80)\n", "\n", "# Analyze different time periods\n", "time_results = {}\n", "\n", "# Night analysis (minimal traffic)\n", "time_results['night'] = analyze_time_periods(\n", "    multi_channel_data, \n", "    'night', \n", "    duration_minutes=20, \n", "    target_sample_rate=10,\n", "    focus_channels=['land_start', 'land_end', 'transition']\n", ")\n", "\n", "# Morning analysis (rush hour)\n", "time_results['morning'] = analyze_time_periods(\n", "    multi_channel_data, \n", "    'morning', \n", "    duration_minutes=20, \n", "    target_sample_rate=10,\n", "    focus_channels=['land_start', 'land_end', 'transition']\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 4: Quick Interactive Analysis Function\n", "def quick_valencia_analysis(segment='transition', time_period='afternoon', \n", "                           duration_minutes=10, sample_rate_hz=15):\n", "    \"\"\"\n", "    Quick analysis function for Valencia dataset.\n", "    \n", "    Parameters:\n", "    -----------\n", "    segment : str\n", "        'land', 'transition', 'shallow_sea', 'deep_sea', or 'all'\n", "    time_period : str\n", "        'night', 'morning', 'afternoon', 'evening'\n", "    duration_minutes : float\n", "        Duration in minutes\n", "    sample_rate_hz : float\n", "        Target sample rate in Hz\n", "    \"\"\"\n", "    # Define channel groups\n", "    channel_groups = {\n", "        'land': ['land_start', 'land_end'],\n", "        'transition': ['land_end', 'transition', 'shallow_sea'],\n", "        'shallow_sea': ['transition', 'shallow_sea', 'mid_sea'],\n", "        'deep_sea': ['mid_sea', 'deep_sea', 'far_sea'],\n", "        'all': ['land_start', 'transition', 'mid_sea', 'far_sea']\n", "    }\n", "    \n", "    if segment not in channel_groups:\n", "        print(f\"Unknown segment: {segment}. Choose from {list(channel_groups.keys())}\")\n", "        return\n", "    \n", "    focus_channels = channel_groups[segment]\n", "    \n", "    print(f\"Quick Valencia Analysis:\")\n", "    print(f\"  Segment: {segment} ({focus_channels})\")\n", "    print(f\"  Time period: {time_period}\")\n", "    print(f\"  Duration: {duration_minutes} minutes at {sample_rate_hz} Hz\")\n", "    \n", "    results = analyze_time_periods(\n", "        multi_channel_data,\n", "        time_period,\n", "        duration_minutes=duration_minutes,\n", "        target_sample_rate=sample_rate_hz,\n", "        focus_channels=focus_channels\n", "    )\n", "    \n", "    return results\n", "\n", "print(\"\\n✓ All Valencia DAS analysis functions ready!\")\n", "print(\"\\n=== Quick Usage Examples ===\")\n", "print(\"# Land-sea transition during afternoon:\")\n", "print(\"quick_valencia_analysis('transition', 'afternoon', 15, 20)\")\n", "print(\"\\n# Deep sea analysis during night:\")\n", "print(\"quick_valencia_analysis('deep_sea', 'night', 30, 5)\")\n", "print(\"\\n# Full array comparison during morning rush:\")\n", "print(\"quick_valencia_analysis('all', 'morning', 10, 10)\")\n", "print(\"\\n# Custom analysis:\")\n", "print(\"analyze_multi_channel_segment(multi_channel_data, start_time_seconds, duration_seconds, target_sample_rate)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 4}