{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Valencia DAS Anomaly Detection - Simple Version\n", "\n", "This notebook provides anomaly detection for Valencia DAS data with static plots that work in any Jupyter environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic setup - guaranteed to work\n", "import sys\n", "import numpy as np\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "import h5py\n", "from pathlib import Path\n", "from scipy.signal import spectrogram, resample\n", "import torch\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Force inline backend for compatibility\n", "%matplotlib inline\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10\n", "\n", "print(f\"✓ Python: {sys.version.split()[0]}\")\n", "print(f\"✓ Matplotlib: {matplotlib.__version__} (backend: {plt.get_backend()})\")\n", "print(f\"✓ NumPy: {np.__version__}\")\n", "print(f\"✓ PyTorch: {torch.__version__}\")\n", "\n", "# Test plot\n", "fig, ax = plt.subplots(figsize=(8, 3))\n", "ax.plot([1, 2, 3, 4], [1, 4, 2, 3], 'o-')\n", "ax.set_title('✓ Matplotlib Test - If you see this, plotting works!')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✓ All imports and plotting successful!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import our custom functions\n", "from enhanced_spectral_visualization import (\n", "    visualize_spectral_anomalies_enhanced, \n", "    get_appropriate_freq_bands, \n", "    suggest_analysis_parameters,\n", "    compare_frequency_scales,\n", "    compare_whitened_spectrograms\n", ")\n", "\n", "print(\"✓ Custom visualization functions imported\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Chronos model\n", "try:\n", "    from chronos import ChronosPipeline\n", "    CHRONOS_AVAILABLE = True\n", "    print(\"✓ ChronosPipeline imported successfully\")\n", "    \n", "    # Load model\n", "    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "    pipeline = ChronosPipeline.from_pretrained(\n", "        \"amazon/chronos-t5-tiny\",\n", "        device_map=device,\n", "        torch_dtype=torch.bfloat16 if device.type == \"cuda\" else torch.float32,\n", "    )\n", "    print(f\"✓ Chronos model loaded on {device}\")\n", "    \n", "except ImportError as e:\n", "    print(f\"❌ Chronos not available: {e}\")\n", "    CHRONOS_AVAILABLE = False\n", "    pipeline = None\n", "except Exception as e:\n", "    print(f\"❌ Failed to load Chronos: {e}\")\n", "    CHRONOS_AVAILABLE = False\n", "    pipeline = None\n", "\n", "if CHRONOS_AVAILABLE:\n", "    print(\"🎯 USING REAL CHRONOS MODEL\")\n", "else:\n", "    print(\"⚠️  Using mock predictions instead\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define prediction functions\n", "def predict_chronos(context, prediction_length=32):\n", "    \"\"\"Make prediction using Chronos or enhanced mock.\"\"\"\n", "    if pipeline is None:\n", "        return mock_prediction(context, prediction_length)\n", "    \n", "    try:\n", "        context_tensor = torch.tensor(context, dtype=torch.float32).unsqueeze(0)\n", "        forecast = pipeline.predict(\n", "            context_tensor,\n", "            prediction_length=prediction_length,\n", "            num_samples=1\n", "        )\n", "        return forecast[0].cpu().numpy().flatten()\n", "    except Exception as e:\n", "        print(f\"❌ Chronos prediction failed: {e}\")\n", "        return mock_prediction(context, prediction_length)\n", "\n", "def mock_prediction(context, prediction_length):\n", "    \"\"\"Conservative mock prediction that will fail on anomalies.\"\"\"\n", "    if len(context) < 4:\n", "        base_value = context[-1] if len(context) > 0 else 0\n", "        prediction = np.full(prediction_length, base_value)\n", "        prediction += np.random.normal(0, 0.001, prediction_length)\n", "        return prediction\n", "    \n", "    # Use only stable part of context (ignore recent changes)\n", "    stable_context = context[:-max(1, len(context)//4)]\n", "    if len(stable_context) < 2:\n", "        stable_context = context[:2]\n", "    \n", "    # Conservative baseline from stable period\n", "    baseline = np.mean(stable_context)\n", "    \n", "    # Very small trend from stable period\n", "    if len(stable_context) >= 2:\n", "        mid_point = len(stable_context) // 2\n", "        early_mean = np.mean(stable_context[:mid_point]) if mid_point > 0 else stable_context[0]\n", "        late_mean = np.mean(stable_context[mid_point:]) if mid_point < len(stable_context) else stable_context[-1]\n", "        trend = (late_mean - early_mean) / len(stable_context) * 0.1  # Heavily dampened\n", "    else:\n", "        trend = 0\n", "    \n", "    # Generate conservative predictions\n", "    prediction = []\n", "    for i in range(prediction_length):\n", "        decay_factor = 0.8 ** i\n", "        pred_value = baseline + trend * (i + 1) * decay_factor\n", "        prediction.append(pred_value)\n", "    \n", "    prediction = np.array(prediction)\n", "    noise_level = np.std(stable_context) * 0.02 if len(stable_context) > 1 else 0.001\n", "    prediction += np.random.normal(0, noise_level, prediction_length)\n", "    \n", "    return prediction\n", "\n", "print(\"✓ Prediction functions ready\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data (copy the load functions from valencia_das_test.py)\n", "def load_valencia_data(data_dir=\"Data\", max_files=6, target_sensor=None):\n", "    \"\"\"Load Valencia DAS data from H5 files.\"\"\"\n", "    data_dir = Path(data_dir)\n", "    h5_files = sorted(list(data_dir.rglob(\"*.h5\")))\n", "    print(f\"Found {len(h5_files)} H5 files\")\n", "    \n", "    if len(h5_files) == 0:\n", "        raise FileNotFoundError(\"No H5 files found\")\n", "    \n", "    # Determine target sensor\n", "    if target_sensor is None:\n", "        with h5py.File(h5_files[0], 'r') as f:\n", "            das_name = list(f.keys())[0]\n", "            sr_dataset = f[f\"{das_name}/Source1/Zone1/SR_Valencia\"]\n", "            n_sensors = sr_dataset.shape[2]\n", "        target_sensor = n_sensors // 2\n", "        print(f\"Using middle sensor: {target_sensor} (out of {n_sensors} sensors)\")\n", "    \n", "    # Load data from files\n", "    all_data = []\n", "    files_to_load = min(max_files, len(h5_files))\n", "    print(f\"Loading sensor {target_sensor} from {files_to_load} files...\")\n", "    \n", "    for i, file_path in enumerate(h5_files[:files_to_load]):\n", "        try:\n", "            with h5py.File(file_path, 'r') as f:\n", "                das_name = list(f.keys())[0]\n", "                sr_dataset = f[f\"{das_name}/Source1/Zone1/SR_Valencia\"]\n", "                dims = sr_dataset.shape\n", "                nb_block = dims[0]\n", "                sampling_frequency = 250\n", "                \n", "                sensor_data_blocks = []\n", "                for tt in range(nb_block):\n", "                    block_data = sr_dataset[tt, :sampling_frequency, target_sensor]\n", "                    sensor_data_blocks.append(block_data)\n", "                \n", "                sensor_data = np.concatenate(sensor_data_blocks)\n", "                all_data.append(sensor_data.astype(np.float32))\n", "                print(f\"  Loaded {file_path.name}: {len(sensor_data)} samples\")\n", "        except Exception as e:\n", "            print(f\"  Warning: Failed to load {file_path.name}: {e}\")\n", "            continue\n", "    \n", "    if not all_data:\n", "        raise ValueError(\"No data could be loaded\")\n", "    \n", "    # Concatenate all data\n", "    full_data = np.concatenate(all_data)\n", "    total_duration = len(full_data) / 250\n", "    \n", "    print(f\"✓ Loaded {len(all_data)} files successfully\")\n", "    print(f\"✓ Total data: {len(full_data)} samples ({total_duration/3600:.1f} hours)\")\n", "    print(f\"✓ Sensor {target_sensor} ready for analysis\")\n", "    \n", "    return full_data, target_sensor\n", "\n", "# Try to load real data\n", "try:\n", "    print(\"Attempting to load REAL Valencia DAS data...\")\n", "    data, sensor_id = load_valencia_data(max_files=6, target_sensor=None)\n", "    data_type = \"REAL\"\n", "    print(f\"✓ Using REAL data from sensor {sensor_id}\")\n", "except Exception as e:\n", "    print(f\"Failed to load real data: {e}\")\n", "    print(\"Creating synthetic data for testing...\")\n", "    \n", "    # Create synthetic data\n", "    duration_hours = 2\n", "    sample_rate = 250\n", "    n_samples = int(duration_hours * 3600 * sample_rate)\n", "    t = np.arange(n_samples) / sample_rate\n", "    \n", "    # Base signals\n", "    traffic = 0.1 * np.sin(2 * np.pi * 5 * t) + 0.05 * np.sin(2 * np.pi * 12 * t)\n", "    rumble = 0.2 * np.sin(2 * np.pi * 0.5 * t) + 0.1 * np.sin(2 * np.pi * 1.5 * t)\n", "    \n", "    # Add strong anomalies\n", "    anomaly_events = [\n", "        {'time': 1800, 'duration': 45, 'freq': 8, 'amplitude': 2.0},\n", "        {'time': 3600, 'duration': 60, 'freq': 2, 'amplitude': 3.0}, \n", "        {'time': 5400, 'duration': 30, 'freq': 12, 'amplitude': 1.5},\n", "    ]\n", "    \n", "    for event in anomaly_events:\n", "        start_idx = int(event['time'] * sample_rate)\n", "        end_idx = start_idx + int(event['duration'] * sample_rate)\n", "        if end_idx < n_samples:\n", "            anomaly_signal = event['amplitude'] * np.sin(2 * np.pi * event['freq'] * t[start_idx:end_idx])\n", "            traffic[start_idx:end_idx] += anomaly_signal\n", "    \n", "    data = traffic + rumble + np.random.normal(0, 0.02, n_samples)\n", "    sensor_id = 1488\n", "    data_type = \"SYNTHETIC\"\n", "    print(f\"✓ Using SYNTHETIC data\")\n", "\n", "print(f\"\\nData loaded: {len(data)} samples ({len(data)/250/3600:.2f} hours)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure analysis parameters\n", "sample_rate = 30  # Hz - good compromise for DAS analysis\n", "analysis_duration = 600  # 10 minutes\n", "\n", "# Choose time segment\n", "total_duration = len(data) / 250\n", "print(f\"Total data duration: {total_duration/3600:.1f} hours\")\n", "\n", "if data_type == \"REAL\":\n", "    # For real data, try different interesting time periods\n", "    analysis_options = [\n", "        {\"start\": 3600, \"description\": \"1 hour mark\"},\n", "        {\"start\": 7200, \"description\": \"2 hour mark\"},  \n", "        {\"start\": int(total_duration * 0.3), \"description\": \"30% through dataset\"},\n", "        {\"start\": int(total_duration * 0.7), \"description\": \"70% through dataset\"}\n", "    ]\n", "    \n", "    # Choose first valid option\n", "    analysis_start = None\n", "    for option in analysis_options:\n", "        if option[\"start\"] + analysis_duration < total_duration:\n", "            analysis_start = option[\"start\"]\n", "            description = option[\"description\"]\n", "            break\n", "    \n", "    if analysis_start is None:\n", "        analysis_start = int(total_duration * 0.5)\n", "        description = \"Middle of dataset\"\n", "else:\n", "    # For synthetic data, choose segment with known anomaly\n", "    analysis_start = 3500  # Just before 3600s anomaly\n", "    description = \"Around 3600s synthetic anomaly\"\n", "\n", "print(f\"\\nAnalysis configuration:\")\n", "print(f\"  Data type: {data_type}\")\n", "print(f\"  Time segment: {analysis_start}s to {analysis_start + analysis_duration}s\")\n", "print(f\"  Description: {description}\")\n", "print(f\"  Sample rate: {sample_rate} Hz\")\n", "print(f\"  Duration: {analysis_duration} seconds\")\n", "\n", "# Get suggested parameters\n", "suggested_params = suggest_analysis_parameters(sample_rate, analysis_duration, 'land')\n", "print(f\"\\nSuggested analysis parameters:\")\n", "print(f\"  Context length: {suggested_params['context_length']}\")\n", "print(f\"  Prediction length: {suggested_params['prediction_length']}\")\n", "print(f\"  Stride: {suggested_params['stride']}\")\n", "print(f\"  Frequency bands: {list(suggested_params['freq_bands'].keys())}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simplified spectral anomaly analysis function\n", "def analyze_spectral_anomalies_simple(data, start_time_seconds, duration_seconds, \n", "                                    target_sample_rate, original_sample_rate=250, \n", "                                    freq_bands=None):\n", "    \"\"\"Simplified spectral anomaly analysis for notebook use.\"\"\"\n", "    \n", "    print(f\"\\n=== Spectral Anomaly Analysis ===\")\n", "    print(f\"Time window: {start_time_seconds}s + {duration_seconds}s\")\n", "    print(f\"Sample rate: {original_sample_rate} Hz → {target_sample_rate} Hz\")\n", "    \n", "    # Get appropriate frequency bands if not provided\n", "    if freq_bands is None:\n", "        freq_bands = get_appropriate_freq_bands(target_sample_rate, 'land')\n", "        print(f\"Using automatic frequency bands: {list(freq_bands.keys())}\")\n", "    \n", "    # Extract and preprocess segment\n", "    start_idx = int(start_time_seconds * original_sample_rate)\n", "    end_idx = int((start_time_seconds + duration_seconds) * original_sample_rate)\n", "    \n", "    if end_idx > len(data):\n", "        end_idx = len(data)\n", "        duration_seconds = (end_idx - start_idx) / original_sample_rate\n", "        print(f\"Truncated to available data: {duration_seconds:.1f}s\")\n", "    \n", "    segment_original = data[start_idx:end_idx]\n", "    \n", "    # Downsample if needed\n", "    if target_sample_rate != original_sample_rate:\n", "        downsample_factor = original_sample_rate / target_sample_rate\n", "        new_length = int(len(segment_original) / downsample_factor)\n", "        segment_resampled = resample(segment_original, new_length)\n", "        print(f\"Downsampled: {len(segment_original)} → {len(segment_resampled)} samples\")\n", "    else:\n", "        segment_resampled = segment_original\n", "    \n", "    # Compute spectrogram\n", "    nperseg = min(256, len(segment_resampled) // 8)\n", "    noverlap = nperseg // 2\n", "    f, t, Sxx = spectrogram(segment_resampled, fs=target_sample_rate,\n", "                           nperseg=nperseg, noverlap=noverlap)\n", "    \n", "    Sxx_db = 10 * np.log10(Sxx + 1e-12)\n", "    \n", "    # Whiten the spectrogram\n", "    print(f\"Whitening spectrogram...\")\n", "    freq_means = np.mean(Sxx_db, axis=1, keepdims=True)\n", "    Sxx_whitened = Sxx_db - freq_means\n", "    \n", "    t_spectrogram = t + start_time_seconds\n", "    print(f\"Spectrogram: {len(f)} frequencies × {len(t)} time bins\")\n", "    \n", "    # Analyze each frequency band (simplified)\n", "    band_anomalies = {}\n", "    \n", "    for band_name, (f_low, f_high) in freq_bands.items():\n", "        print(f\"\\nAnalyzing {band_name} band ({f_low}-{f_high} Hz)...\")\n", "        \n", "        # Find frequency indices\n", "        freq_mask = (f >= f_low) & (f <= f_high)\n", "        if not np.any(freq_mask):\n", "            print(f\"  Warning: No frequencies found in band\")\n", "            continue\n", "        \n", "        # Extract power in this frequency band\n", "        band_power = np.mean(Sxx_whitened[freq_mask, :], axis=0)\n", "        \n", "        # Simple anomaly detection using statistical thresholds\n", "        power_mean = np.mean(band_power)\n", "        power_std = np.std(band_power)\n", "        threshold = power_mean + 2 * power_std  # 2-sigma threshold\n", "        \n", "        anomaly_mask = band_power > threshold\n", "        n_anomalies = np.sum(anomaly_mask)\n", "        \n", "        print(f\"  Power: mean={power_mean:.3f}, std={power_std:.3f}\")\n", "        print(f\"  Threshold: {threshold:.3f}\")\n", "        print(f\"  Anomalies: {n_anomalies}/{len(band_power)} ({100*n_anomalies/len(band_power):.1f}%)\")\n", "        \n", "        band_anomalies[band_name] = {\n", "            'band_power': band_power,\n", "            'threshold': threshold,\n", "            'anomaly_mask': anomaly_mask,\n", "            'n_anomalies': n_anomalies,\n", "            'anomaly_rate': n_anomalies / len(band_power),\n", "            'freq_range': (f_low, f_high),\n", "            'window_starts': t_spectrogram  # For compatibility with visualization\n", "        }\n", "    \n", "    # Package results\n", "    results = {\n", "        'start_time': start_time_seconds,\n", "        'duration': duration_seconds,\n", "        'target_sample_rate': target_sample_rate,\n", "        'spectrogram_f': f,\n", "        'spectrogram_t': t_spectrogram,\n", "        'spectrogram_Sxx': Sxx_whitened,\n", "        'freq_bands': freq_bands,\n", "        'band_anomalies': band_anomalies,\n", "        'segment_resampled': segment_resampled\n", "    }\n", "    \n", "    print(f\"\\n✓ Spectral analysis complete\")\n", "    return results\n", "\n", "print(\"✓ Analysis function ready\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the analysis\n", "print(f\"\\n{'='*60}\")\n", "print(f\"RUNNING SPECTRAL ANOMALY ANALYSIS\")\n", "print(f\"{'='*60}\")\n", "\n", "results = analyze_spectral_anomalies_simple(\n", "    data,\n", "    start_time_seconds=analysis_start,\n", "    duration_seconds=analysis_duration,\n", "    target_sample_rate=sample_rate,\n", "    freq_bands=suggested_params['freq_bands']\n", ")\n", "\n", "# Summary\n", "total_anomalies = sum([band['n_anomalies'] for band in results['band_anomalies'].values()])\n", "print(f\"\\n🎯 ANALYSIS COMPLETE\")\n", "print(f\"Total anomalies detected: {total_anomalies}\")\n", "print(f\"Frequency bands analyzed: {len(results['band_anomalies'])}\")\n", "\n", "if total_anomalies > 0:\n", "    print(f\"\\nAnomalies by frequency band:\")\n", "    for band_name, band_result in results['band_anomalies'].items():\n", "        n_anom = band_result['n_anomalies']\n", "        if n_anom > 0:\n", "            freq_range = band_result['freq_range']\n", "            print(f\"  {band_name}: {n_anom} anomalies ({freq_range[0]:.1f}-{freq_range[1]:.1f} Hz)\")\n", "else:\n", "    print(f\"\\n⚠️  No anomalies detected with current thresholds\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create visualizations (static plots that work everywhere)\n", "print(\"Creating enhanced visualization...\")\n", "\n", "# Enhanced visualization (no file saving)\n", "fig, axes = visualize_spectral_anomalies_enhanced(\n", "    results, \n", "    f\"{data_type}_Data_Analysis\",\n", "    log_freq=True,\n", "    save_fig=False  # Don't save files in notebook\n", ")\n", "\n", "print(\"✓ Enhanced visualization displayed above\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook provides a simplified but functional version of the Valencia DAS anomaly detection system that works in any Jupyter environment without requiring interactive widget extensions.\n", "\n", "### What you should see:\n", "1. **Test plot** confirming matplotlib works\n", "2. **Data loading** from real Valencia DAS files or synthetic data\n", "3. **Spectral analysis** with frequency band anomaly detection\n", "4. **Enhanced visualization** showing spectrograms and anomaly locations\n", "\n", "### Key Features:\n", "- **Static plots** that display inline in any Jupyter environment\n", "- **No file saving** - all plots appear directly in notebook\n", "- **Simplified anomaly detection** using statistical thresholds\n", "- **Real or synthetic data** support\n", "\n", "### Next steps:\n", "- Try different time segments by changing `analysis_start`\n", "- Experiment with different sample rates and durations\n", "- Adjust anomaly detection thresholds\n", "- Compare results between real and synthetic data\n", "\n", "### Troubleshooting:\n", "If you don't see plots:\n", "1. Make sure you're running in a Jupyter environment\n", "2. Try restarting the kernel and running all cells\n", "3. Check that `%matplotlib inline` is working in the first cell"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}