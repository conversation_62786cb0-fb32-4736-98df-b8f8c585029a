#!/usr/bin/env python3
"""
Test script to diagnose and fix matplotlib issues in Jupyter notebooks.
"""

import sys
import os
import matplotlib
import matplotlib.pyplot as plt
import numpy as np

def test_matplotlib_config():
    """Test matplotlib configuration and suggest fixes."""
    
    print("🔍 MATPLOTLIB DIAGNOSTIC TEST")
    print("=" * 50)
    
    # Basic info
    print(f"Python version: {sys.version}")
    print(f"Matplotlib version: {matplotlib.__version__}")
    print(f"Current backend: {plt.get_backend()}")
    print(f"Interactive mode: {matplotlib.is_interactive()}")
    print(f"Platform: {sys.platform}")
    
    # Environment info
    print(f"\nEnvironment:")
    print(f"  DISPLAY: {os.environ.get('DISPLAY', 'Not set')}")
    print(f"  JUPYTER_SERVER_ROOT: {os.environ.get('JUPYTER_SERVER_ROOT', 'Not set')}")
    
    # Test basic plotting
    print(f"\n📊 Testing basic plotting...")
    try:
        # Use Agg backend for testing (non-interactive)
        matplotlib.use('Agg')
        
        fig, ax = plt.subplots(figsize=(6, 4))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y)
        ax.set_title('Test Plot')
        
        # Save to test if matplotlib works
        fig.savefig('test_plot.png', dpi=100, bbox_inches='tight')
        plt.close(fig)
        
        print("✓ Basic matplotlib functionality works")
        
        # Clean up
        if os.path.exists('test_plot.png'):
            os.remove('test_plot.png')
            
    except Exception as e:
        print(f"❌ Basic matplotlib test failed: {e}")
        return False
    
    # Test widget availability
    print(f"\n🔧 Testing widget availability...")
    try:
        import ipywidgets
        print(f"✓ ipywidgets available: {ipywidgets.__version__}")
    except ImportError:
        print("❌ ipywidgets not available")
        print("   Install with: pip install ipywidgets")
    
    # Test jupyter widgets
    try:
        import jupyterlab_widgets
        print(f"✓ jupyterlab-widgets available")
    except ImportError:
        print("❌ jupyterlab-widgets not available")
        print("   Install with: pip install jupyterlab-widgets")
    
    return True

def suggest_fixes():
    """Suggest fixes for common matplotlib issues."""
    
    print(f"\n🛠️  SUGGESTED FIXES")
    print("=" * 50)
    
    print("1. Install required packages:")
    print("   pip install ipywidgets jupyterlab-widgets")
    
    print("\n2. Enable Jupyter extensions:")
    print("   jupyter nbextension enable --py widgetsnbextension")
    print("   jupyter labextension install @jupyter-widgets/jupyterlab-manager")
    
    print("\n3. For notebook cells, use this pattern:")
    print("""
   # At the start of your notebook:
   import matplotlib
   matplotlib.use('Agg')  # Set non-interactive backend first
   
   import matplotlib.pyplot as plt
   
   # Then enable interactive mode:
   %matplotlib inline  # For static plots
   # OR
   %matplotlib widget  # For interactive plots (if available)
   """)
    
    print("\n4. Alternative approach for notebooks:")
    print("""
   # Force inline backend
   %matplotlib inline
   import matplotlib.pyplot as plt
   
   # Your plotting code here
   fig, ax = plt.subplots()
   ax.plot([1,2,3], [1,4,2])
   plt.show()  # This should display the plot
   """)
    
    print("\n5. If still having issues:")
    print("   - Restart Jupyter kernel")
    print("   - Clear all outputs and run all cells")
    print("   - Check browser console for JavaScript errors")
    print("   - Try different browsers")

def create_simple_notebook_test():
    """Create a simple test for notebook use."""
    
    notebook_code = '''
# Simple matplotlib test for notebooks
import matplotlib
matplotlib.use('Agg')  # Non-interactive backend

import matplotlib.pyplot as plt
import numpy as np

# Enable inline plotting
%matplotlib inline

# Test plot
fig, ax = plt.subplots(figsize=(8, 4))
x = np.linspace(0, 10, 100)
y = np.sin(x)
ax.plot(x, y, 'b-', linewidth=2)
ax.set_title('Test Plot - If you see this, matplotlib works!')
ax.set_xlabel('X')
ax.set_ylabel('sin(X)')
ax.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

print("✓ If you see the plot above, matplotlib is working correctly!")
'''
    
    print(f"\n📝 SIMPLE NOTEBOOK TEST CODE")
    print("=" * 50)
    print("Copy this code into a notebook cell:")
    print(notebook_code)

if __name__ == "__main__":
    success = test_matplotlib_config()
    suggest_fixes()
    create_simple_notebook_test()
    
    if success:
        print(f"\n✅ Matplotlib basic functionality confirmed")
    else:
        print(f"\n❌ Matplotlib has issues - follow suggested fixes above")
