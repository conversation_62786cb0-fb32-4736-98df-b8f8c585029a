#!/usr/bin/env python3
"""
Quick test of the anomaly exploration functionality.
This runs a subset of the notebook analysis to verify everything works.
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import torch

# Add src directory to path
sys.path.append('src')

from chronos_loader import DASChronosDataset
from chronos import ChronosPipeline

def quick_anomaly_test():
    """Run a quick test of anomaly detection functionality."""
    print("=== Quick Anomaly Detection Test ===")
    
    # Load dataset
    data_dir = Path("Data")
    h5_files = list(data_dir.rglob("*.h5"))
    test_file = h5_files[0]
    
    print(f"Loading: {test_file.name}")
    
    dataset = DASChronosDataset(
        data_path=test_file,
        context_length=128,
        prediction_length=32,
        stride=32,
        normalize=True,
        normalization_method='z_score',
        apply_filtering=False
    )
    
    print(f"Dataset: {dataset.data.shape[0]} sensors, {dataset.data.shape[1]} timesteps")
    
    # Find active sensor
    print("Finding active sensor...")
    for i in range(min(100, dataset.data.shape[0])):
        sensor_data = dataset.data[i, :2000]
        if np.std(sensor_data) > 0.5:
            selected_sensor = i
            break
    
    print(f"Selected sensor {selected_sensor}")
    
    # Load Chronos
    print("Loading Chronos model...")
    device = torch.device("cpu")  # Use CPU for quick test
    
    pipeline = ChronosPipeline.from_pretrained(
        "amazon/chronos-t5-tiny",
        device_map=device,
        torch_dtype=torch.float32,
    )
    
    # Analyze 30-second window
    print("Analyzing 30-second window...")
    sensor_data = dataset.data[selected_sensor, :7500]  # 30 seconds at 250 Hz
    
    # Create sliding windows
    context_length = 128
    prediction_length = 32
    stride = 32
    
    errors = []
    predictions = []
    actuals = []
    
    n_windows = (len(sensor_data) - context_length - prediction_length) // stride + 1
    
    for i in range(min(20, n_windows)):  # Test first 20 windows
        start_idx = i * stride
        context = sensor_data[start_idx:start_idx + context_length]
        target = sensor_data[start_idx + context_length:start_idx + context_length + prediction_length]
        
        # Predict
        context_tensor = torch.tensor(context, dtype=torch.float32).unsqueeze(0)
        forecast = pipeline.predict(context_tensor, prediction_length=prediction_length, num_samples=1)
        prediction = forecast[0].cpu().numpy()
        
        # Compute error
        mse_error = np.mean((prediction - target) ** 2)
        errors.append(mse_error)
        predictions.append(prediction)
        actuals.append(target)
        
        if (i + 1) % 5 == 0:
            print(f"  Processed {i + 1}/{min(20, n_windows)} windows")
    
    errors = np.array(errors)
    
    # Detect anomalies
    threshold = np.percentile(errors, 90)
    anomalies = errors > threshold
    
    print(f"\nResults:")
    print(f"  Windows analyzed: {len(errors)}")
    print(f"  Mean error: {np.mean(errors):.6f}")
    print(f"  90th percentile threshold: {threshold:.6f}")
    print(f"  Anomalies detected: {np.sum(anomalies)} ({100*np.mean(anomalies):.1f}%)")
    print(f"  Max error: {np.max(errors):.6f}")
    
    # Quick visualization
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle(f'Quick Anomaly Test - Sensor {selected_sensor}', fontsize=14)
    
    # 1. Raw data
    time_axis = np.arange(len(sensor_data)) / 250
    axes[0, 0].plot(time_axis, sensor_data, 'b-', alpha=0.7, linewidth=0.8)
    axes[0, 0].set_title('Raw Sensor Data (30 seconds)')
    axes[0, 0].set_xlabel('Time (s)')
    axes[0, 0].set_ylabel('Normalized Strain Rate')
    
    # 2. Error time series
    error_time = np.arange(len(errors)) * stride / 250
    axes[0, 1].plot(error_time, errors, 'g-', linewidth=1.5)
    axes[0, 1].axhline(threshold, color='red', linestyle='--', label='90th percentile')
    axes[0, 1].scatter(error_time[anomalies], errors[anomalies], color='red', s=50, zorder=5)
    axes[0, 1].set_title('Prediction Errors')
    axes[0, 1].set_xlabel('Time (s)')
    axes[0, 1].set_ylabel('MSE Error')
    axes[0, 1].legend()
    axes[0, 1].set_yscale('log')
    
    # 3. Example normal prediction
    normal_idx = np.where(~anomalies)[0]
    if len(normal_idx) > 0:
        idx = normal_idx[len(normal_idx)//2]
        pred_time = np.arange(prediction_length)
        axes[1, 0].plot(pred_time, actuals[idx], 'b-', linewidth=2, label='Actual')
        pred_flat = predictions[idx].flatten() if predictions[idx].ndim > 1 else predictions[idx]
        axes[1, 0].plot(pred_time, pred_flat, 'g--', linewidth=2, label='Predicted')
        axes[1, 0].set_title(f'Normal Prediction (MSE: {errors[idx]:.6f})')
        axes[1, 0].set_xlabel('Steps')
        axes[1, 0].set_ylabel('Value')
        axes[1, 0].legend()
    
    # 4. Example anomalous prediction
    anomaly_idx = np.where(anomalies)[0]
    if len(anomaly_idx) > 0:
        idx = anomaly_idx[0]
        pred_time = np.arange(prediction_length)
        axes[1, 1].plot(pred_time, actuals[idx], 'b-', linewidth=2, label='Actual')
        pred_flat = predictions[idx].flatten() if predictions[idx].ndim > 1 else predictions[idx]
        axes[1, 1].plot(pred_time, pred_flat, 'r--', linewidth=2, label='Predicted')
        axes[1, 1].set_title(f'Anomalous Prediction (MSE: {errors[idx]:.6f})')
        axes[1, 1].set_xlabel('Steps')
        axes[1, 1].set_ylabel('Value')
        axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig('quick_anomaly_test_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n✓ Quick test completed successfully!")
    print(f"✓ Visualization saved as 'quick_anomaly_test_results.png'")
    print(f"\nThe full notebook provides much more detailed analysis including:")
    print(f"  - 90-second and 1-hour window analysis")
    print(f"  - Amplitude analysis of anomalies")
    print(f"  - Temporal distribution patterns")
    print(f"  - Statistical comparisons")
    print(f"  - Interactive exploration capabilities")
    
    return True

if __name__ == "__main__":
    try:
        success = quick_anomaly_test()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
