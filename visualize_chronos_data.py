#!/usr/bin/env python3
"""
Visualization script for the chronos_loader.py module.
Loads DAS data and creates visualizations to understand the data structure and patterns.
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from pathlib import Path
from datetime import datetime, timedelta
import seaborn as sns

# Add src directory to path to import chronos_loader
sys.path.append('src')

try:
    from chronos_loader import DASChronosDataset, DASChronosDataLoader
    print("✓ Successfully imported chronos_loader modules")
except ImportError as e:
    print(f"✗ Failed to import chronos_loader: {e}")
    sys.exit(1)

def setup_plotting():
    """Set up matplotlib for better plots."""
    plt.style.use('default')
    sns.set_palette("husl")
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 10
    plt.rcParams['axes.grid'] = True
    plt.rcParams['grid.alpha'] = 0.3

def visualize_raw_data(dataset, max_sensors=10, max_time_samples=1000):
    """Visualize the raw DAS data."""
    print(f"\n=== Visualizing Raw DAS Data ===")
    
    # Get a subset of the data for visualization
    n_sensors, n_timesteps = dataset.data.shape
    sensor_indices = np.linspace(0, n_sensors-1, min(max_sensors, n_sensors), dtype=int)
    time_indices = np.linspace(0, n_timesteps-1, min(max_time_samples, n_timesteps), dtype=int)
    
    data_subset = dataset.data[np.ix_(sensor_indices, time_indices)]
    
    # Create time axis (assuming 1 sample per second for visualization)
    time_axis = np.arange(len(time_indices))
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('DAS Data Overview', fontsize=16)
    
    # 1. Waterfall plot (time vs sensors)
    ax1 = axes[0, 0]
    im1 = ax1.imshow(data_subset, aspect='auto', cmap='seismic', 
                     extent=[0, len(time_indices), sensor_indices[-1], sensor_indices[0]])
    ax1.set_title('Waterfall Plot (Sensors vs Time)')
    ax1.set_xlabel('Time Samples')
    ax1.set_ylabel('Sensor Index')
    plt.colorbar(im1, ax=ax1, label='Strain Rate')
    
    # 2. Time series for selected sensors
    ax2 = axes[0, 1]
    for i, sensor_idx in enumerate(sensor_indices[:5]):  # Show first 5 sensors
        ax2.plot(time_axis, data_subset[i, :], label=f'Sensor {sensor_idx}', alpha=0.7)
    ax2.set_title('Time Series for Selected Sensors')
    ax2.set_xlabel('Time Samples')
    ax2.set_ylabel('Strain Rate')
    ax2.legend()
    
    # 3. Histogram of values
    ax3 = axes[1, 0]
    ax3.hist(data_subset.flatten(), bins=50, alpha=0.7, edgecolor='black')
    ax3.set_title('Distribution of Strain Rate Values')
    ax3.set_xlabel('Strain Rate')
    ax3.set_ylabel('Frequency')
    ax3.axvline(np.mean(data_subset), color='red', linestyle='--', label='Mean')
    ax3.axvline(np.median(data_subset), color='orange', linestyle='--', label='Median')
    ax3.legend()
    
    # 4. Statistics per sensor
    ax4 = axes[1, 1]
    sensor_means = np.mean(data_subset, axis=1)
    sensor_stds = np.std(data_subset, axis=1)
    ax4.errorbar(sensor_indices, sensor_means, yerr=sensor_stds, 
                fmt='o-', capsize=5, alpha=0.7)
    ax4.set_title('Mean ± Std per Sensor')
    ax4.set_xlabel('Sensor Index')
    ax4.set_ylabel('Strain Rate')
    
    plt.tight_layout()
    plt.savefig('das_raw_data_overview.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✓ Raw data visualization saved as 'das_raw_data_overview.png'")

def visualize_batch_samples(dataset, batch_size=4):
    """Visualize samples from a batch."""
    print(f"\n=== Visualizing Batch Samples ===")
    
    # Create a DataLoader
    dataloader = DASChronosDataLoader.create_train_loader(
        dataset, batch_size=batch_size, shuffle=True, num_workers=0
    )
    
    # Get one batch
    batch = next(iter(dataloader))
    past_values = batch['past_values'].numpy()
    future_values = batch['future_values'].numpy()
    sensor_indices = batch['sensor_idx'].numpy()
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Chronos Dataset Batch Samples', fontsize=16)
    
    colors = plt.cm.tab10(np.linspace(0, 1, batch_size))
    
    # 1. Context (past) values
    ax1 = axes[0, 0]
    for i in range(batch_size):
        ax1.plot(past_values[i], color=colors[i], alpha=0.7, 
                label=f'Sample {i} (Sensor {sensor_indices[i]})')
    ax1.set_title(f'Context Values (Length: {past_values.shape[1]})')
    ax1.set_xlabel('Time Steps')
    ax1.set_ylabel('Normalized Strain Rate')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Target (future) values
    ax2 = axes[0, 1]
    for i in range(batch_size):
        ax2.plot(future_values[i], color=colors[i], alpha=0.7,
                label=f'Sample {i} (Sensor {sensor_indices[i]})')
    ax2.set_title(f'Target Values (Length: {future_values.shape[1]})')
    ax2.set_xlabel('Time Steps')
    ax2.set_ylabel('Normalized Strain Rate')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. Combined context + target for one sample
    ax3 = axes[1, 0]
    sample_idx = 0
    context_len = past_values.shape[1]
    target_len = future_values.shape[1]
    
    full_sequence = np.concatenate([past_values[sample_idx], future_values[sample_idx]])
    time_axis = np.arange(len(full_sequence))
    
    ax3.plot(time_axis[:context_len], past_values[sample_idx], 
            color='blue', linewidth=2, label='Context (Input)')
    ax3.plot(time_axis[context_len:], future_values[sample_idx], 
            color='red', linewidth=2, label='Target (Output)')
    ax3.axvline(context_len, color='black', linestyle='--', alpha=0.7, label='Prediction Point')
    ax3.set_title(f'Context → Target (Sensor {sensor_indices[sample_idx]})')
    ax3.set_xlabel('Time Steps')
    ax3.set_ylabel('Normalized Strain Rate')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. Statistics of the batch
    ax4 = axes[1, 1]
    
    # Calculate statistics
    context_stats = {
        'mean': np.mean(past_values, axis=1),
        'std': np.std(past_values, axis=1),
        'min': np.min(past_values, axis=1),
        'max': np.max(past_values, axis=1)
    }
    
    target_stats = {
        'mean': np.mean(future_values, axis=1),
        'std': np.std(future_values, axis=1),
        'min': np.min(future_values, axis=1),
        'max': np.max(future_values, axis=1)
    }
    
    x_pos = np.arange(batch_size)
    width = 0.35
    
    ax4.bar(x_pos - width/2, context_stats['mean'], width, 
           label='Context Mean', alpha=0.7, color='blue')
    ax4.bar(x_pos + width/2, target_stats['mean'], width, 
           label='Target Mean', alpha=0.7, color='red')
    
    ax4.set_title('Batch Statistics')
    ax4.set_xlabel('Sample Index')
    ax4.set_ylabel('Mean Normalized Strain Rate')
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels([f'S{i}\n(Sen{sensor_indices[i]})' for i in range(batch_size)])
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('chronos_batch_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✓ Batch visualization saved as 'chronos_batch_visualization.png'")
    
    # Print batch statistics
    print(f"\nBatch Statistics:")
    print(f"  Batch size: {batch_size}")
    print(f"  Context length: {past_values.shape[1]}")
    print(f"  Target length: {future_values.shape[1]}")
    print(f"  Sensor indices: {sensor_indices}")
    print(f"  Context value range: [{np.min(past_values):.4f}, {np.max(past_values):.4f}]")
    print(f"  Target value range: [{np.min(future_values):.4f}, {np.max(future_values):.4f}]")

def analyze_normalization(dataset):
    """Analyze the normalization applied to the data."""
    print(f"\n=== Analyzing Normalization ===")
    
    # Get normalization stats
    norm_stats = dataset.get_normalization_stats()
    
    if not norm_stats:
        print("No normalization applied")
        return
    
    # Extract statistics
    sensor_indices = list(norm_stats.keys())
    means = [norm_stats[i]['mean'] for i in sensor_indices]
    stds = [norm_stats[i]['std'] for i in sensor_indices]
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle('Normalization Statistics', fontsize=14)
    
    # Plot means
    axes[0].plot(sensor_indices, means, 'o-', alpha=0.7)
    axes[0].set_title('Original Means per Sensor')
    axes[0].set_xlabel('Sensor Index')
    axes[0].set_ylabel('Original Mean')
    axes[0].grid(True, alpha=0.3)
    
    # Plot standard deviations
    axes[1].plot(sensor_indices, stds, 'o-', alpha=0.7, color='orange')
    axes[1].set_title('Original Std Deviations per Sensor')
    axes[1].set_xlabel('Sensor Index')
    axes[1].set_ylabel('Original Std Dev')
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('normalization_stats.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✓ Normalization analysis saved as 'normalization_stats.png'")
    print(f"  Mean of means: {np.mean(means):.6f}")
    print(f"  Mean of stds: {np.mean(stds):.6f}")
    print(f"  Range of means: [{np.min(means):.6f}, {np.max(means):.6f}]")
    print(f"  Range of stds: [{np.min(stds):.6f}, {np.max(stds):.6f}]")

def main():
    """Main visualization function."""
    print("=== DAS Chronos Data Visualization ===")
    
    # Set up plotting
    setup_plotting()
    
    # Find the first available H5 file
    data_dir = Path("Data")
    h5_files = list(data_dir.rglob("*.h5"))
    
    if not h5_files:
        print("✗ No H5 files found in Data directory")
        return False
    
    # Use the first H5 file
    test_file = h5_files[0]
    print(f"Using file: {test_file}")
    
    try:
        # Create dataset
        dataset = DASChronosDataset(
            data_path=test_file,
            context_length=128,
            prediction_length=32,
            stride=32,  # Less overlap for faster processing
            normalize=True,
            normalization_method='z_score',
            sensor_indices=None  # Use all sensors
        )
        
        print(f"✓ Dataset created with {len(dataset)} samples")
        
        # Run visualizations
        visualize_raw_data(dataset, max_sensors=20, max_time_samples=600)
        visualize_batch_samples(dataset, batch_size=4)
        analyze_normalization(dataset)
        
        print(f"\n=== Visualization Complete ===")
        print("Generated files:")
        print("  - das_raw_data_overview.png")
        print("  - chronos_batch_visualization.png") 
        print("  - normalization_stats.png")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during visualization: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Check for required packages
    try:
        import torch
        print("✓ PyTorch available")
    except ImportError:
        print("✗ PyTorch not available")
    
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        print("✓ Matplotlib and Seaborn available")
    except ImportError:
        print("✗ Matplotlib/Seaborn not available - installing...")
        os.system("pip install matplotlib seaborn")
    
    success = main()
    sys.exit(0 if success else 1)
