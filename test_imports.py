#!/usr/bin/env python3
"""
Test script to verify all imports work correctly for the notebook.
"""

print("Testing imports for Valencia DAS Interactive Anomaly Detection...")

try:
    import sys
    import numpy as np
    import matplotlib.pyplot as plt
    import h5py
    from pathlib import Path
    from scipy.signal import spectrogram, resample
    import torch
    print("✓ Basic imports successful")
except ImportError as e:
    print(f"❌ Basic import failed: {e}")
    sys.exit(1)

try:
    from enhanced_spectral_visualization import (
        visualize_spectral_anomalies_enhanced, 
        get_appropriate_freq_bands, 
        suggest_analysis_parameters,
        compare_frequency_scales,
        compare_whitened_spectrograms,
        create_interactive_anomaly_explorer
    )
    print("✓ Enhanced spectral visualization imports successful")
except ImportError as e:
    print(f"❌ Enhanced spectral visualization import failed: {e}")
    sys.exit(1)

# Test the suggest_analysis_parameters function
try:
    params = suggest_analysis_parameters(30, 600, 'land')
    print("✓ suggest_analysis_parameters function works")
    print(f"  Sample parameters: {list(params.keys())}")
except Exception as e:
    print(f"❌ suggest_analysis_parameters failed: {e}")
    sys.exit(1)

# Test Chronos import
try:
    from chronos import ChronosPipeline
    print("✓ Chronos import successful")
    CHRONOS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Chronos not available: {e}")
    print("   This is OK - will use mock predictions")
    CHRONOS_AVAILABLE = False

print(f"\n🎯 All imports successful!")
print(f"Chronos available: {CHRONOS_AVAILABLE}")
print(f"Ready to run the notebook!")
