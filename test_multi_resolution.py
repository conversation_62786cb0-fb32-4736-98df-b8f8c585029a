#!/usr/bin/env python3
"""
Test script to demonstrate multi-resolution anomaly detection concept.
"""

import numpy as np
import matplotlib.pyplot as plt

def determine_optimal_resolution(duration_seconds):
    """Determine optimal sampling resolution based on analysis duration."""
    if duration_seconds <= 60:  # ≤ 1 minute: full resolution
        return 250.0, 1
    elif duration_seconds <= 600:  # ≤ 10 minutes: moderate downsampling
        return 25.0, 10
    elif duration_seconds <= 3600:  # ≤ 1 hour: significant downsampling
        return 5.0, 50
    else:  # > 1 hour: heavy downsampling
        return 1.0, 250

def downsample_data(data, downsample_factor, method='mean'):
    """Downsample data by the specified factor."""
    if downsample_factor == 1:
        return data
    
    # Trim data to be divisible by downsample_factor
    n_samples = len(data)
    n_complete_blocks = n_samples // downsample_factor
    trimmed_data = data[:n_complete_blocks * downsample_factor]
    
    # Reshape and apply downsampling method
    reshaped = trimmed_data.reshape(-1, downsample_factor)
    
    if method == 'mean':
        return np.mean(reshaped, axis=1)
    else:
        raise ValueError(f"Unknown downsampling method: {method}")

def adaptive_context_prediction_lengths(target_hz, duration_seconds):
    """Determine appropriate context and prediction lengths based on resolution."""
    if target_hz >= 250:  # High resolution
        return 128, 32, 16
    elif target_hz >= 25:  # Medium resolution
        return 64, 16, 8
    elif target_hz >= 5:  # Low resolution
        return 32, 8, 4
    else:  # Very low resolution
        return 16, 4, 2

def main():
    print("=== Multi-Resolution Anomaly Detection Demo ===")
    
    # Create synthetic DAS-like data
    print("Creating synthetic DAS data...")
    duration_seconds = 3600  # 1 hour
    sampling_rate = 250  # Hz
    n_samples = int(duration_seconds * sampling_rate)
    
    # Generate synthetic strain rate data
    time_axis = np.linspace(0, duration_seconds, n_samples)
    
    # Base signal: low frequency trend + noise
    base_signal = 0.1 * np.sin(2 * np.pi * 0.01 * time_axis)  # 0.01 Hz trend
    noise = 0.05 * np.random.randn(n_samples)
    
    # Add some "seismic events" - higher frequency bursts
    for event_time in [300, 1200, 2400]:  # Events at 5, 20, 40 minutes
        event_start = int(event_time * sampling_rate)
        event_duration = int(30 * sampling_rate)  # 30 second events
        event_end = min(event_start + event_duration, n_samples)
        
        # High frequency burst
        event_signal = 0.3 * np.sin(2 * np.pi * 10 * time_axis[event_start:event_end])
        base_signal[event_start:event_end] += event_signal
    
    synthetic_data = base_signal + noise
    
    print(f"Generated {len(synthetic_data)} samples ({duration_seconds} seconds)")
    
    # Test different analysis windows
    analysis_windows = [
        {'name': '30-second', 'duration': 30},
        {'name': '5-minute', 'duration': 300},
        {'name': '30-minute', 'duration': 1800},
        {'name': 'Full Hour', 'duration': 3600}
    ]
    
    print(f"\n=== Testing Multi-Resolution Analysis ===")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Multi-Resolution Analysis Demo', fontsize=16)
    
    for i, window_config in enumerate(analysis_windows):
        name = window_config['name']
        duration = window_config['duration']
        
        # Determine resolution
        target_hz, downsample_factor = determine_optimal_resolution(duration)
        context_length, prediction_length, stride = adaptive_context_prediction_lengths(target_hz, duration)
        
        print(f"\n{name} ({duration}s):")
        print(f"  Target resolution: {target_hz} Hz (downsample factor: {downsample_factor})")
        print(f"  Context/Prediction/Stride: {context_length}/{prediction_length}/{stride}")
        
        # Extract window data
        start_idx = 0
        end_idx = int(duration * sampling_rate)
        window_data_full = synthetic_data[start_idx:end_idx]
        
        # Downsample if needed
        if downsample_factor > 1:
            window_data = downsample_data(window_data_full, downsample_factor, method='mean')
            print(f"  Downsampled from {len(window_data_full)} to {len(window_data)} samples")
        else:
            window_data = window_data_full
        
        # Calculate number of prediction windows
        total_length = context_length + prediction_length
        n_windows = (len(window_data) - total_length) // stride + 1
        print(f"  Prediction windows: {n_windows}")
        
        # Plot
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        # Create time axis for plotting
        if downsample_factor > 1:
            plot_time = np.arange(len(window_data)) * downsample_factor / sampling_rate
        else:
            plot_time = np.arange(len(window_data)) / sampling_rate
        
        ax.plot(plot_time, window_data, 'b-', alpha=0.7, linewidth=0.8)
        ax.set_title(f'{name} - {target_hz} Hz Resolution')
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Synthetic Strain Rate')
        ax.grid(True, alpha=0.3)
        
        # Highlight events if visible at this resolution
        for event_time in [300, 1200, 2400]:
            if event_time < duration:
                ax.axvline(event_time, color='red', linestyle='--', alpha=0.7, label='Seismic Event' if event_time == 300 else '')
        
        if i == 0:  # Add legend to first plot
            ax.legend()
    
    plt.tight_layout()
    plt.savefig('multi_resolution_demo.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n=== Summary ===")
    print("Multi-resolution approach benefits:")
    print("✓ Short windows (≤60s): Full 250 Hz resolution for detailed anomaly detection")
    print("✓ Medium windows (5-10 min): 25 Hz resolution for computational efficiency")
    print("✓ Long windows (≥1 hour): 1-5 Hz resolution for broad pattern analysis")
    print("✓ Memory efficient: reduces data size by up to 250x for long windows")
    print("✓ Computationally scalable: analysis time scales with resolution, not raw data size")
    
    print(f"\n✓ Demo complete! Visualization saved as 'multi_resolution_demo.png'")

if __name__ == "__main__":
    main()
