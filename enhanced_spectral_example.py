"""
Enhanced Spectral Anomaly Visualization Example
This shows how to use the improved visualization with your Valencia DAS data.
"""

# Import the enhanced visualization function
from enhanced_spectral_visualization import visualize_spectral_anomalies_enhanced

# Example usage with your Valencia data:
def run_enhanced_spectral_analysis():
    """
    Run the enhanced spectral analysis with improved visualizations.
    """
    
    print("\n" + "="*80)
    print("ENHANCED SPECTRAL EXAMPLE: Frequency Band Anomaly Detection")
    print("Focus: Precise time-frequency anomaly locations")
    print("="*80)
    
    # This assumes you have already loaded multi_channel_data from your notebook
    # If running standalone, you would need to load the data first
    
    try:
        # Import your analysis function (from the notebook)
        from valencia_das_anomaly_detection import analyze_spectral_anomalies, multi_channel_data
        
        # Import the frequency band suggestion function
        from enhanced_spectral_visualization import get_appropriate_freq_bands, suggest_analysis_parameters

        # Get appropriate frequency bands for the sample rate and environment
        sample_rate = 50
        suggested_params = suggest_analysis_parameters(sample_rate, 600, 'land')

        print(f"Suggested frequency bands for {sample_rate} Hz land analysis:")
        for name, (f_low, f_high) in suggested_params['freq_bands'].items():
            print(f"  {name}: {f_low}-{f_high} Hz")
        print(f"Max displayable frequency: {suggested_params['max_frequency']:.1f} Hz")

        # Analyze land channel for traffic-related spectral anomalies
        land_spectral_results = analyze_spectral_anomalies(
            multi_channel_data['land_end'],
            start_time_seconds=14400,  # 4 hours (afternoon traffic)
            duration_seconds=600,      # 10 minutes
            target_sample_rate=sample_rate,
            freq_bands=suggested_params['freq_bands'],  # Use appropriate bands
            context_length=suggested_params['context_length'],
            prediction_length=suggested_params['prediction_length'],
            stride=suggested_params['stride']
        )
        
        # Use the enhanced visualization instead of the original
        print("\nUsing ENHANCED visualization with precise anomaly locations...")
        fig, axes = visualize_spectral_anomalies_enhanced(land_spectral_results, "Land_End")
        
        return land_spectral_results, fig, axes
        
    except ImportError:
        print("Note: This example requires the Valencia DAS notebook to be loaded first.")
        print("Please run the notebook cells to load multi_channel_data and analysis functions.")
        return None, None, None

def compare_visualizations():
    """
    Compare the original vs enhanced visualization approaches.
    """
    
    print("\n" + "="*80)
    print("VISUALIZATION COMPARISON")
    print("="*80)
    
    print("\nORIGINAL VISUALIZATION:")
    print("- Vertical lines across entire frequency range")
    print("- Overlapping anomaly markers")
    print("- Inconsistent time axes")
    print("- Generic frequency band overlays")
    
    print("\nENHANCED VISUALIZATION:")
    print("✓ Precise rectangles at specific time-frequency locations")
    print("✓ Numbered markers for anomaly identification")
    print("✓ Consistent xlim bounds across all subplots")
    print("✓ Clear frequency band boundaries with labels")
    print("✓ Enhanced color scheme and legend")
    print("✓ Detailed anomaly breakdown in output")
    
    print("\nKEY IMPROVEMENTS:")
    print("1. SPATIAL PRECISION: Anomalies shown exactly where they occur in time-frequency space")
    print("2. VISUAL CLARITY: Numbered markers and rectangles instead of overlapping lines")
    print("3. CONSISTENCY: All subplots use same time axis for easy comparison")
    print("4. INFORMATION DENSITY: More detailed anomaly information in console output")

def usage_examples():
    """
    Show different ways to use the enhanced visualization.
    """
    
    print("\n" + "="*80)
    print("USAGE EXAMPLES")
    print("="*80)
    
    print("\n# Example 1: Land traffic analysis with appropriate frequency bands")
    print("from enhanced_spectral_visualization import get_appropriate_freq_bands")
    print("")
    print("# Get frequency bands that fit within sample rate limits")
    print("sample_rate = 50")
    print("freq_bands = get_appropriate_freq_bands(sample_rate, 'land')")
    print("print(f'Appropriate bands for {sample_rate} Hz: {freq_bands}')")
    print("")
    print("land_results = analyze_spectral_anomalies(")
    print("    multi_channel_data['land_end'],")
    print("    start_time_seconds=14400,  # Afternoon traffic")
    print("    duration_seconds=600,      # 10 minutes")
    print("    target_sample_rate=sample_rate,")
    print("    freq_bands=freq_bands      # Use appropriate bands!")
    print(")")
    print("fig, axes = visualize_spectral_anomalies_enhanced(land_results, 'Land_Traffic')")
    
    print("\n# Example 2: Marine microseism analysis")
    print("sea_results = analyze_spectral_anomalies(")
    print("    multi_channel_data['deep_sea'],")
    print("    start_time_seconds=7200,   # Night (less interference)")
    print("    duration_seconds=1800,     # 30 minutes")
    print("    target_sample_rate=25,")
    print("    freq_bands={")
    print("        'primary_microseism': (0.05, 0.15),")
    print("        'secondary_microseism': (0.1, 0.5),")
    print("        'swell': (0.5, 2.0)")
    print("    }")
    print(")")
    print("fig, axes = visualize_spectral_anomalies_enhanced(sea_results, 'Deep_Sea_Microseisms')")
    
    print("\n# Example 3: Transition zone analysis")
    print("transition_results = analyze_spectral_anomalies(")
    print("    multi_channel_data['transition'],")
    print("    start_time_seconds=10800,  # 3 hours")
    print("    duration_seconds=900,      # 15 minutes")
    print("    target_sample_rate=30,")
    print("    freq_bands={")
    print("        'low_freq': (0.1, 1.0),")
    print("        'mixed_signals': (1.0, 10.0),")
    print("        'high_freq': (10.0, 50.0)")
    print("    }")
    print(")")
    print("fig, axes = visualize_spectral_anomalies_enhanced(transition_results, 'Land_Sea_Transition')")

def main():
    """
    Main function to demonstrate enhanced spectral visualization.
    """
    
    print("Enhanced Spectral Anomaly Visualization for Valencia DAS")
    print("=" * 60)
    
    # Show comparison
    compare_visualizations()
    
    # Show usage examples
    usage_examples()
    
    # Try to run actual analysis if data is available
    results, fig, axes = run_enhanced_spectral_analysis()
    
    if results is not None:
        print(f"\n✓ Enhanced analysis complete!")
        print(f"✓ Results saved with improved visualization")
    else:
        print(f"\n→ To use with your data, run the Valencia DAS notebook first")
        print(f"→ Then call: visualize_spectral_anomalies_enhanced(your_results, 'Channel_Name')")
    
    return results, fig, axes

if __name__ == "__main__":
    main()
