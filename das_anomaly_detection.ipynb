{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DAS Anomaly Detection with Chronos\n", "\n", "A focused approach to anomaly detection in DAS data:\n", "1. Load 1 day of data from one station (middle of array)\n", "2. Function to analyze X seconds with target sample rate\n", "3. Outputs: timeseries, spectrogram, detected anomalies\n", "4. Anomaly detection based on MSE of Chronos prediction error"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import h5py\n", "from pathlib import Path\n", "from scipy.signal import spectrogram, resample\n", "import torch\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add src directory\n", "sys.path.append('src')\n", "from chronos import ChronosPipeline\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "plt.rcParams['figure.figsize'] = (16, 10)\n", "plt.rcParams['font.size'] = 11\n", "\n", "print(\"✓ Libraries imported\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load One Day of Data from Middle Station"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_single_sensor_from_file(file_path, sensor_idx):\n", "    \"\"\"Load one sensor from one H5 file.\"\"\"\n", "    with h5py.File(file_path, 'r') as f:\n", "        das_name = list(f.keys())[0]\n", "        zone_path = f\"{das_name}/Source1/Zone1/\"\n", "        sr_dataset_path = f\"{zone_path}SR_Valencia\"\n", "        \n", "        if sr_dataset_path not in f:\n", "            raise ValueError(f\"Could not find strain rate data at {sr_dataset_path}\")\n", "        \n", "        sr_dataset = f[sr_dataset_path]\n", "        dims = sr_dataset.shape\n", "        nb_block = dims[0]\n", "        sampling_frequency = 250\n", "        \n", "        # Extract sensor data from all blocks\n", "        sensor_data_blocks = []\n", "        for tt in range(nb_block):\n", "            block_data = sr_dataset[tt, :sampling_frequency, sensor_idx]\n", "            sensor_data_blocks.append(block_data)\n", "        \n", "        sensor_data = np.concatenate(sensor_data_blocks)\n", "        \n", "    return sensor_data.astype(np.float32)\n", "\n", "def load_one_day_data(data_dir, target_sensor=None, max_files=144):\n", "    \"\"\"\n", "    Load one day of data from a single sensor.\n", "    \n", "    Parameters:\n", "    -----------\n", "    data_dir : Path\n", "        Directory containing H5 files\n", "    target_sensor : int, optional\n", "        Sensor index. If None, uses middle of array\n", "    max_files : int\n", "        Maximum files to load (144 files = 24 hours at 10min/file)\n", "    \"\"\"\n", "    # Find all H5 files\n", "    h5_files = sorted(list(data_dir.rglob(\"*.h5\")))\n", "    print(f\"Found {len(h5_files)} H5 files\")\n", "    \n", "    if len(h5_files) == 0:\n", "        raise ValueError(\"No H5 files found\")\n", "    \n", "    # Determine target sensor (middle of array if not specified)\n", "    if target_sensor is None:\n", "        # Get sensor count from first file\n", "        with h5py.File(h5_files[0], 'r') as f:\n", "            das_name = list(f.keys())[0]\n", "            sr_dataset = f[f\"{das_name}/Source1/Zone1/SR_Valencia\"]\n", "            n_sensors = sr_dataset.shape[2]\n", "        \n", "        target_sensor = n_sensors // 2  # Middle sensor\n", "        print(f\"Using middle sensor: {target_sensor} (out of {n_sensors} sensors)\")\n", "    \n", "    # Load data from consecutive files\n", "    all_data = []\n", "    file_info = []\n", "    \n", "    files_to_load = min(max_files, len(h5_files))\n", "    print(f\"Loading sensor {target_sensor} from {files_to_load} files...\")\n", "    \n", "    for i, file_path in enumerate(h5_files[:files_to_load]):\n", "        try:\n", "            sensor_data = load_single_sensor_from_file(file_path, target_sensor)\n", "            all_data.append(sensor_data)\n", "            \n", "            duration = len(sensor_data) / 250\n", "            file_info.append({\n", "                'filename': file_path.name,\n", "                'duration_seconds': duration,\n", "                'samples': len(sensor_data)\n", "            })\n", "            \n", "            if (i + 1) % 12 == 0:  # Progress every 2 hours\n", "                print(f\"  Loaded {i+1}/{files_to_load} files ({(i+1)*10/60:.1f} hours)\")\n", "                \n", "        except Exception as e:\n", "            print(f\"  Warning: Failed to load {file_path.name}: {e}\")\n", "            continue\n", "    \n", "    if not all_data:\n", "        raise ValueError(\"No data could be loaded\")\n", "    \n", "    # Concatenate all data\n", "    full_day_data = np.concatenate(all_data)\n", "    total_duration = len(full_day_data) / 250\n", "    \n", "    print(f\"\\n✓ Loaded {len(all_data)} files successfully\")\n", "    print(f\"✓ Total data: {len(full_day_data)} samples ({total_duration/3600:.1f} hours)\")\n", "    print(f\"✓ Sensor {target_sensor} ready for analysis\")\n", "    \n", "    return full_day_data, target_sensor, file_info\n", "\n", "# Load the data\n", "data_dir = Path(\"Data\")\n", "full_day_data, selected_sensor, file_info = load_one_day_data(data_dir, max_files=144)\n", "\n", "print(f\"\\nData summary:\")\n", "print(f\"  Sensor: {selected_sensor}\")\n", "print(f\"  Duration: {len(full_day_data)/250/3600:.1f} hours\")\n", "print(f\"  Sample rate: 250 Hz\")\n", "print(f\"  Data range: [{np.min(full_day_data):.2f}, {np.max(full_day_data):.2f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Initialize Chronos Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Chronos model\n", "print(\"Loading Chronos model...\")\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "pipeline = ChronosPipeline.from_pretrained(\n", "    \"amazon/chronos-t5-tiny\",\n", "    device_map=device,\n", "    torch_dtype=torch.bfloat16 if device.type == \"cuda\" else torch.float32,\n", ")\n", "\n", "print(f\"✓ Chronos model loaded on {device}\")\n", "\n", "def predict_chronos(context, prediction_length=32):\n", "    \"\"\"Make prediction using Chronos.\"\"\"\n", "    try:\n", "        context_tensor = torch.tensor(context, dtype=torch.float32).unsqueeze(0)\n", "        forecast = pipeline.predict(\n", "            context_tensor,\n", "            prediction_length=prediction_length,\n", "            num_samples=1\n", "        )\n", "        return forecast[0].cpu().numpy().flatten()\n", "    except Exception as e:\n", "        print(f\"Prediction failed: {e}\")\n", "        # Fallback: simple continuation\n", "        return np.full(prediction_length, context[-1]) + np.random.normal(0, 0.01, prediction_length)\n", "\n", "print(\"✓ Prediction function ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Core Analysis Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_das_segment(data, start_time_seconds, duration_seconds, target_sample_rate, \n", "                       original_sample_rate=250, context_length=128, prediction_length=32, stride=16):\n", "    \"\"\"\n", "    Analyze a segment of DAS data with specified duration and sample rate.\n", "    \n", "    Parameters:\n", "    -----------\n", "    data : np.n<PERSON><PERSON>\n", "        Full day DAS data at original sample rate\n", "    start_time_seconds : float\n", "        Start time in seconds from beginning of data\n", "    duration_seconds : float\n", "        Duration of segment to analyze\n", "    target_sample_rate : float\n", "        Target sample rate for analysis (Hz)\n", "    original_sample_rate : float\n", "        Original sample rate of data (Hz)\n", "    context_length : int\n", "        Context window length for Chronos\n", "    prediction_length : int\n", "        Prediction window length for Chronos\n", "    stride : int\n", "        Stride between prediction windows\n", "        \n", "    Returns:\n", "    --------\n", "    dict : Analysis results with timeseries, spectrogram, and anomalies\n", "    \"\"\"\n", "    print(f\"\\n=== Analyzing {duration_seconds}s segment starting at {start_time_seconds}s ===\")\n", "    print(f\"Target sample rate: {target_sample_rate} Hz\")\n", "    \n", "    # Extract segment from full data\n", "    start_idx = int(start_time_seconds * original_sample_rate)\n", "    end_idx = int((start_time_seconds + duration_seconds) * original_sample_rate)\n", "    \n", "    if end_idx > len(data):\n", "        print(f\"Warning: Requested end time exceeds data length, truncating\")\n", "        end_idx = len(data)\n", "        duration_seconds = (end_idx - start_idx) / original_sample_rate\n", "    \n", "    segment_original = data[start_idx:end_idx]\n", "    print(f\"Extracted segment: {len(segment_original)} samples ({len(segment_original)/original_sample_rate:.1f}s)\")\n", "    \n", "    # Downsample if needed\n", "    if target_sample_rate != original_sample_rate:\n", "        downsample_factor = original_sample_rate / target_sample_rate\n", "        new_length = int(len(segment_original) / downsample_factor)\n", "        segment_resampled = resample(segment_original, new_length)\n", "        print(f\"Downsampled to {target_sample_rate} Hz: {len(segment_resampled)} samples\")\n", "    else:\n", "        segment_resampled = segment_original\n", "        downsample_factor = 1\n", "    \n", "    # Normalize data\n", "    segment_mean = np.mean(segment_resampled)\n", "    segment_std = np.std(segment_resampled)\n", "    segment_normalized = (segment_resampled - segment_mean) / segment_std\n", "    print(f\"Normalized: mean={segment_mean:.3f}, std={segment_std:.3f}\")\n", "    \n", "    # Create time axis\n", "    time_axis = np.arange(len(segment_normalized)) / target_sample_rate + start_time_seconds\n", "    \n", "    # Anomaly detection using sliding windows\n", "    print(f\"\\nRunning anomaly detection...\")\n", "    total_length = context_length + prediction_length\n", "    n_windows = (len(segment_normalized) - total_length) // stride + 1\n", "    print(f\"Creating {n_windows} prediction windows (context: {context_length}, pred: {prediction_length}, stride: {stride})\")\n", "    \n", "    window_starts = []\n", "    mse_errors = []\n", "    predictions = []\n", "    actuals = []\n", "    \n", "    for i in range(n_windows):\n", "        window_start = i * stride\n", "        context_end = window_start + context_length\n", "        target_end = window_start + total_length\n", "        \n", "        if target_end > len(segment_normalized):\n", "            break\n", "        \n", "        context = segment_normalized[window_start:context_end]\n", "        target = segment_normalized[context_end:target_end]\n", "        \n", "        # Predict\n", "        prediction = predict_chronos(context, prediction_length)\n", "        \n", "        # Compute MSE\n", "        mse_error = np.mean((prediction - target) ** 2)\n", "        \n", "        # Store results (convert back to original time scale)\n", "        original_window_start = start_idx + int(window_start * downsample_factor)\n", "        window_starts.append(original_window_start)\n", "        mse_errors.append(mse_error)\n", "        predictions.append(prediction)\n", "        actuals.append(target)\n", "    \n", "    window_starts = np.array(window_starts)\n", "    mse_errors = np.array(mse_errors)\n", "    \n", "    print(f\"Completed {len(mse_errors)} predictions\")\n", "    print(f\"MSE range: [{np.min(mse_errors):.6f}, {np.max(mse_errors):.6f}]\")\n", "    \n", "    # Detect anomalies (90th percentile threshold)\n", "    threshold = np.percentile(mse_errors, 90)\n", "    anomaly_mask = mse_errors > threshold\n", "    n_anomalies = np.sum(anomaly_mask)\n", "    \n", "    print(f\"Anomaly threshold (90th percentile): {threshold:.6f}\")\n", "    print(f\"Detected {n_anomalies} anomalies ({100*n_anomalies/len(mse_errors):.1f}%)\")\n", "    \n", "    # Compute spectrogram\n", "    print(f\"\\nComputing spectrogram...\")\n", "    nperseg = min(512, len(segment_normalized) // 8)\n", "    f, t, Sxx = spectrogram(segment_normalized, fs=target_sample_rate, \n", "                           nperseg=nperseg, noverlap=nperseg//2)\n", "    Sxx_db = 10 * np.log10(Sxx + 1e-12)\n", "    \n", "    # Adjust spectrogram time axis\n", "    t_spectrogram = t + start_time_seconds\n", "    \n", "    print(f\"Spectrogram: {len(f)} frequencies, {len(t)} time bins\")\n", "    \n", "    # Package results\n", "    results = {\n", "        'start_time': start_time_seconds,\n", "        'duration': duration_seconds,\n", "        'target_sample_rate': target_sample_rate,\n", "        'downsample_factor': downsample_factor,\n", "        \n", "        # Time series data\n", "        'time_axis': time_axis,\n", "        'segment_original': segment_original,\n", "        'segment_normalized': segment_normalized,\n", "        'normalization': {'mean': segment_mean, 'std': segment_std},\n", "        \n", "        # Spectrogram\n", "        'spectrogram_f': f,\n", "        'spectrogram_t': t_spectrogram,\n", "        'spectrogram_Sxx': Sxx_db,\n", "        \n", "        # Anomaly detection\n", "        'window_starts': window_starts,\n", "        'mse_errors': mse_errors,\n", "        'threshold': threshold,\n", "        'anomaly_mask': anomaly_mask,\n", "        'n_anomalies': n_anomalies,\n", "        'anomaly_rate': n_anomalies / len(mse_errors),\n", "        'predictions': predictions,\n", "        'actuals': actuals\n", "    }\n", "    \n", "    print(f\"\\n✓ Analysis complete\")\n", "    return results\n", "\n", "print(\"✓ Analysis function ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Visualization Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_analysis(results, sensor_id):\n", "    \"\"\"\n", "    Create comprehensive visualization of analysis results.\n", "    \n", "    Parameters:\n", "    -----------\n", "    results : dict\n", "        Results from analyze_das_segment\n", "    sensor_id : int\n", "        Sensor identifier for plot titles\n", "    \"\"\"\n", "    fig, axes = plt.subplots(3, 1, figsize=(16, 12))\n", "    \n", "    start_time = results['start_time']\n", "    duration = results['duration']\n", "    sample_rate = results['target_sample_rate']\n", "    \n", "    fig.suptitle(f'DAS Anomaly Analysis - Sensor {sensor_id}\\n'\n", "                f'{start_time:.0f}-{start_time+duration:.0f}s ({duration:.0f}s) at {sample_rate} Hz', \n", "                fontsize=14)\n", "    \n", "    # 1. Time series with anomalies\n", "    ax1 = axes[0]\n", "    ax1.plot(results['time_axis'], results['segment_normalized'], 'b-', alpha=0.7, linewidth=0.8)\n", "    \n", "    # Highlight anomalous regions\n", "    for i, is_anomaly in enumerate(results['anomaly_mask']):\n", "        if is_anomaly:\n", "            window_start_time = results['window_starts'][i] / 250  # Convert to seconds\n", "            window_duration = (128 + 32) * results['downsample_factor'] / 250  # Window duration\n", "            window_end_time = window_start_time + window_duration\n", "            ax1.axvspan(window_start_time, window_end_time, alpha=0.3, color='red')\n", "    \n", "    ax1.set_title(f'Time Series with Detected Anomalies ({results[\"n_anomalies\"]} anomalies, {100*results[\"anomaly_rate\"]:.1f}%)')\n", "    ax1.set_xlabel('Time (s)')\n", "    ax1.set_ylabel('Normalized Strain Rate')\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 2. Spectrogram\n", "    ax2 = axes[1]\n", "    im = ax2.pcolormesh(results['spectrogram_t'], results['spectrogram_f'], \n", "                       results['spectrogram_Sxx'], shading='gouraud', cmap='viridis')\n", "    ax2.set_title('Spectrogram')\n", "    ax2.set_xlabel('Time (s)')\n", "    ax2.set_ylabel('Frequency (Hz)')\n", "    ax2.set_ylim([0, min(50, sample_rate/2)])  # Focus on lower frequencies\n", "    plt.colorbar(im, ax=ax2, label='Power (dB)')\n", "    \n", "    # Mark anomalies on spectrogram\n", "    for i, is_anomaly in enumerate(results['anomaly_mask']):\n", "        if is_anomaly:\n", "            window_start_time = results['window_starts'][i] / 250\n", "            ax2.axvline(window_start_time, color='red', alpha=0.7, linewidth=1)\n", "    \n", "    # 3. MSE errors\n", "    ax3 = axes[2]\n", "    error_times = results['window_starts'] / 250  # Convert to seconds\n", "    ax3.plot(error_times, results['mse_errors'], 'g-', linewidth=1.5, alpha=0.8)\n", "    ax3.axhline(results['threshold'], color='red', linestyle='--', alpha=0.7, \n", "               label=f'Threshold (90th %ile): {results[\"threshold\"]:.4f}')\n", "    ax3.scatter(error_times[results['anomaly_mask']], \n", "               results['mse_errors'][results['anomaly_mask']], \n", "               color='red', s=30, zorder=5, alpha=0.8, label='Anomalies')\n", "    \n", "    ax3.set_title('Prediction MSE Errors')\n", "    ax3.set_xlabel('Time (s)')\n", "    ax3.set_ylabel('MSE Error')\n", "    ax3.set_yscale('log')\n", "    ax3.legend()\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # Save plot\n", "    filename = f'das_analysis_sensor{sensor_id}_{start_time:.0f}s_{duration:.0f}s_{sample_rate:.0f}Hz.png'\n", "    plt.savefig(filename, dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(f\"\\n✓ Visualization saved as: {filename}\")\n", "    \n", "    # Print summary\n", "    print(f\"\\n=== Analysis Summary ===\")\n", "    print(f\"Sensor: {sensor_id}\")\n", "    print(f\"Time window: {start_time:.0f}-{start_time+duration:.0f}s ({duration:.0f}s)\")\n", "    print(f\"Sample rate: {sample_rate} Hz (downsample factor: {results['downsample_factor']:.1f})\")\n", "    print(f\"Anomalies detected: {results['n_anomalies']} out of {len(results['mse_errors'])} windows ({100*results['anomaly_rate']:.1f}%)\")\n", "    print(f\"MSE threshold: {results['threshold']:.6f}\")\n", "    print(f\"MSE range: [{np.min(results['mse_errors']):.6f}, {np.max(results['mse_errors']):.6f}]\")\n", "\n", "print(\"✓ Visualization function ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Example Usage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 1: High-resolution short window (60 seconds at 50 Hz)\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"EXAMPLE 1: High-resolution short window\")\n", "print(\"=\"*60)\n", "\n", "results_short = analyze_das_segment(\n", "    data=full_day_data,\n", "    start_time_seconds=3600,  # Start at 1 hour\n", "    duration_seconds=60,      # 60 seconds\n", "    target_sample_rate=50,    # 50 Hz\n", "    context_length=128,       # ~2.5 seconds context\n", "    prediction_length=32,     # ~0.6 seconds prediction\n", "    stride=16                 # ~0.3 seconds stride\n", ")\n", "\n", "visualize_analysis(results_short, selected_sensor)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 2: Lower-resolution longer window (1 hour at 1 Hz)\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"EXAMPLE 2: Lower-resolution longer window\")\n", "print(\"=\"*60)\n", "\n", "results_long = analyze_das_segment(\n", "    data=full_day_data,\n", "    start_time_seconds=7200,  # Start at 2 hours\n", "    duration_seconds=3600,    # 1 hour\n", "    target_sample_rate=1,     # 1 Hz\n", "    context_length=128,       # ~2 minutes context\n", "    prediction_length=32,     # ~30 seconds prediction\n", "    stride=16                 # ~15 seconds stride\n", ")\n", "\n", "visualize_analysis(results_long, selected_sensor)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 3: Medium resolution window (10 minutes at 10 Hz)\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"EXAMPLE 3: Medium resolution window\")\n", "print(\"=\"*60)\n", "\n", "results_medium = analyze_das_segment(\n", "    data=full_day_data,\n", "    start_time_seconds=14400,  # Start at 4 hours\n", "    duration_seconds=600,      # 10 minutes\n", "    target_sample_rate=10,     # 10 Hz\n", "    context_length=128,        # ~12.8 seconds context\n", "    prediction_length=32,      # ~3.2 seconds prediction\n", "    stride=16                  # ~1.6 seconds stride\n", ")\n", "\n", "visualize_analysis(results_medium, selected_sensor)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Interactive Analysis Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def quick_analysis(start_hours=0, duration_minutes=10, sample_rate_hz=10):\n", "    \"\"\"\n", "    Quick analysis function with convenient time units.\n", "    \n", "    Parameters:\n", "    -----------\n", "    start_hours : float\n", "        Start time in hours from beginning of data\n", "    duration_minutes : float\n", "        Duration in minutes\n", "    sample_rate_hz : float\n", "        Target sample rate in Hz\n", "    \"\"\"\n", "    start_seconds = start_hours * 3600\n", "    duration_seconds = duration_minutes * 60\n", "    \n", "    print(f\"Quick analysis: {start_hours}h + {duration_minutes}min at {sample_rate_hz} Hz\")\n", "    \n", "    results = analyze_das_segment(\n", "        data=full_day_data,\n", "        start_time_seconds=start_seconds,\n", "        duration_seconds=duration_seconds,\n", "        target_sample_rate=sample_rate_hz\n", "    )\n", "    \n", "    visualize_analysis(results, selected_sensor)\n", "    return results\n", "\n", "print(\"\\n✓ All functions ready!\")\n", "print(\"\\nTo run a quick analysis, use:\")\n", "print(\"  quick_analysis(start_hours=2, duration_minutes=5, sample_rate_hz=25)\")\n", "print(\"\\nOr use the full function:\")\n", "print(\"  analyze_das_segment(full_day_data, start_time_seconds, duration_seconds, target_sample_rate)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quick_analysis(start_hours=2, duration_minutes=5, sample_rate_hz=25)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 4}