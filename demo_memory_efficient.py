#!/usr/bin/env python3
"""
Simple demonstration of memory-efficient multi-file loading.
"""

import sys
import numpy as np
import h5py
from pathlib import Path

# Add src directory to path
sys.path.append('src')

def load_single_sensor_from_file(file_path, sensor_idx):
    """Load only one sensor from a file."""
    with h5py.File(file_path, 'r') as f:
        das_name = list(f.keys())[0]
        zone_path = f"{das_name}/Source1/Zone1/"
        sr_dataset_path = f"{zone_path}SR_Valencia"
        
        sr_dataset = f[sr_dataset_path]
        dims = sr_dataset.shape
        nb_block = dims[0]
        sampling_frequency = 250
        
        # Extract only the target sensor from all blocks
        sensor_data_blocks = []
        for tt in range(nb_block):
            block_data = sr_dataset[tt, :sampling_frequency, sensor_idx]
            sensor_data_blocks.append(block_data)
        
        sensor_data = np.concatenate(sensor_data_blocks)
    return sensor_data.astype(np.float32)

def main():
    print("=== Memory-Efficient Multi-File Loading Demo ===")
    
    # Find files
    data_dir = Path("Data")
    h5_files = sorted(list(data_dir.rglob("*.h5")))
    
    print(f"Found {len(h5_files)} H5 files")
    if len(h5_files) < 3:
        print("Need at least 3 files")
        return
    
    # Use sensor 50 as example (likely to be active)
    target_sensor = 50
    print(f"Loading sensor {target_sensor} from first 3 files...")
    
    all_data = []
    total_duration = 0
    
    for i, file_path in enumerate(h5_files[:3]):
        print(f"  File {i+1}: {file_path.name}")
        try:
            sensor_data = load_single_sensor_from_file(file_path, target_sensor)
            duration = len(sensor_data) / 250
            all_data.append(sensor_data)
            total_duration += duration
            print(f"    Loaded {len(sensor_data)} samples ({duration:.1f} seconds)")
        except Exception as e:
            print(f"    Failed: {e}")
    
    if all_data:
        concatenated = np.concatenate(all_data)
        print(f"\n✓ Success!")
        print(f"  Total samples: {len(concatenated)}")
        print(f"  Total duration: {total_duration:.1f} seconds ({total_duration/60:.1f} minutes)")
        print(f"  Data size: {concatenated.nbytes / 1024 / 1024:.1f} MB")
        print(f"  Memory savings vs full dataset: ~99.97% (loading 1 sensor vs 2977 sensors)")
        
        # Show data characteristics
        print(f"\nData characteristics:")
        print(f"  Range: [{np.min(concatenated):.2f}, {np.max(concatenated):.2f}]")
        print(f"  Mean: {np.mean(concatenated):.2f}")
        print(f"  Std: {np.std(concatenated):.2f}")
        
        print(f"\n✓ Ready for Chronos anomaly analysis!")
    else:
        print("✗ No data loaded")

if __name__ == "__main__":
    main()
