#!/usr/bin/env python3
"""
Diagnostic script to examine raw DAS data before normalization.
This will help us understand why the data appears to be all zeros.
"""

import sys
import numpy as np
import h5py
import matplotlib.pyplot as plt
from pathlib import Path

# Add src directory to path
sys.path.append('src')

def examine_raw_h5_data(file_path):
    """Examine raw H5 data without any processing."""
    print(f"=== Examining Raw H5 Data: {file_path} ===")
    
    with h5py.File(file_path, 'r') as f:
        # Get DAS device name
        das_name = list(f.keys())[0]
        print(f"DAS device: {das_name}")
        
        # Get dataset
        zone_path = f"{das_name}/Source1/Zone1/"
        sr_dataset_path = f"{zone_path}SR_Valencia"
        sr_dataset = f[sr_dataset_path]
        
        print(f"Dataset shape: {sr_dataset.shape}")
        print(f"Dataset dtype: {sr_dataset.dtype}")
        
        # Sample a few blocks to check raw values
        print("\n=== Sampling Raw Data ===")
        
        # Check first block
        first_block = sr_dataset[0, :10, :10]  # First 10x10 samples
        print(f"First block (10x10 sample):")
        print(f"  Min: {np.min(first_block)}")
        print(f"  Max: {np.max(first_block)}")
        print(f"  Mean: {np.mean(first_block)}")
        print(f"  Std: {np.std(first_block)}")
        print(f"  Non-finite count: {np.sum(~np.isfinite(first_block))}")
        print(f"  Zero count: {np.sum(first_block == 0)}")
        
        # Check middle block
        mid_idx = sr_dataset.shape[0] // 2
        mid_block = sr_dataset[mid_idx, :10, :10]
        print(f"\nMiddle block (10x10 sample):")
        print(f"  Min: {np.min(mid_block)}")
        print(f"  Max: {np.max(mid_block)}")
        print(f"  Mean: {np.mean(mid_block)}")
        print(f"  Std: {np.std(mid_block)}")
        print(f"  Non-finite count: {np.sum(~np.isfinite(mid_block))}")
        print(f"  Zero count: {np.sum(mid_block == 0)}")
        
        # Check a larger sample from first block
        print(f"\n=== Larger Sample Analysis ===")
        large_sample = sr_dataset[0, :250, :100]  # First 250 time samples, first 100 sensors
        print(f"Large sample shape: {large_sample.shape}")
        print(f"  Min: {np.min(large_sample)}")
        print(f"  Max: {np.max(large_sample)}")
        print(f"  Mean: {np.mean(large_sample)}")
        print(f"  Std: {np.std(large_sample)}")
        print(f"  Non-finite count: {np.sum(~np.isfinite(large_sample))}")
        print(f"  Zero count: {np.sum(large_sample == 0)} / {large_sample.size}")
        print(f"  Non-zero count: {np.sum(large_sample != 0)}")
        
        # Look at value distribution
        finite_values = large_sample[np.isfinite(large_sample)]
        if len(finite_values) > 0:
            print(f"\nFinite values statistics:")
            print(f"  Count: {len(finite_values)}")
            print(f"  Min: {np.min(finite_values)}")
            print(f"  Max: {np.max(finite_values)}")
            print(f"  Mean: {np.mean(finite_values)}")
            print(f"  Std: {np.std(finite_values)}")
            
            # Show some actual values
            non_zero_finite = finite_values[finite_values != 0]
            if len(non_zero_finite) > 0:
                print(f"  Non-zero finite values count: {len(non_zero_finite)}")
                print(f"  Sample non-zero values: {non_zero_finite[:10]}")
            else:
                print(f"  All finite values are zero!")
        
        return large_sample

def test_chronos_loader_raw_data():
    """Test our chronos loader with raw data examination."""
    print(f"\n=== Testing Chronos Loader Raw Data ===")
    
    from chronos_loader import DASChronosDataset
    
    # Find H5 file
    data_dir = Path("Data")
    h5_files = list(data_dir.rglob("*.h5"))
    test_file = h5_files[0]
    
    # Create dataset without normalization to see raw values
    dataset = DASChronosDataset(
        data_path=test_file,
        context_length=128,
        prediction_length=32,
        stride=64,
        normalize=False,  # Don't normalize to see raw values
        apply_filtering=False  # Don't filter to see raw values
    )
    
    print(f"Dataset shape: {dataset.data.shape}")
    print(f"Raw data statistics:")
    print(f"  Min: {np.min(dataset.data)}")
    print(f"  Max: {np.max(dataset.data)}")
    print(f"  Mean: {np.mean(dataset.data)}")
    print(f"  Std: {np.std(dataset.data)}")
    print(f"  Non-finite count: {np.sum(~np.isfinite(dataset.data))}")
    print(f"  Zero count: {np.sum(dataset.data == 0)} / {dataset.data.size}")
    
    # Sample a few time series
    print(f"\nSample time series (first 5 sensors, first 1000 timesteps):")
    sample_data = dataset.data[:5, :1000]
    for i in range(5):
        series = sample_data[i, :]
        finite_series = series[np.isfinite(series)]
        non_zero_series = finite_series[finite_series != 0]
        print(f"  Sensor {i}: min={np.min(finite_series):.6e}, max={np.max(finite_series):.6e}, "
              f"non-zero={len(non_zero_series)}/{len(series)}")
    
    return dataset

def visualize_raw_data_sample(data_sample):
    """Create a simple visualization of raw data."""
    print(f"\n=== Creating Raw Data Visualization ===")
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # 1. Histogram of all values
    finite_data = data_sample[np.isfinite(data_sample)]
    axes[0, 0].hist(finite_data.flatten(), bins=50, alpha=0.7)
    axes[0, 0].set_title('Distribution of All Finite Values')
    axes[0, 0].set_xlabel('Value')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_yscale('log')
    
    # 2. Histogram of non-zero values only
    non_zero_data = finite_data[finite_data != 0]
    if len(non_zero_data) > 0:
        axes[0, 1].hist(non_zero_data, bins=50, alpha=0.7, color='orange')
        axes[0, 1].set_title(f'Distribution of Non-Zero Values (n={len(non_zero_data)})')
        axes[0, 1].set_xlabel('Value')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].set_yscale('log')
    else:
        axes[0, 1].text(0.5, 0.5, 'No non-zero values found', 
                       ha='center', va='center', transform=axes[0, 1].transAxes)
        axes[0, 1].set_title('No Non-Zero Values')
    
    # 3. Sample time series
    axes[1, 0].plot(data_sample[0, :100], label='Sensor 0')
    axes[1, 0].plot(data_sample[1, :100], label='Sensor 1')
    axes[1, 0].set_title('Sample Time Series (First 100 Points)')
    axes[1, 0].set_xlabel('Time Sample')
    axes[1, 0].set_ylabel('Value')
    axes[1, 0].legend()
    
    # 4. Waterfall plot of small section
    small_section = data_sample[:20, :100]  # First 20 sensors, 100 time points
    im = axes[1, 1].imshow(small_section, aspect='auto', cmap='seismic')
    axes[1, 1].set_title('Waterfall Plot (20 sensors × 100 time points)')
    axes[1, 1].set_xlabel('Time Sample')
    axes[1, 1].set_ylabel('Sensor Index')
    plt.colorbar(im, ax=axes[1, 1])
    
    plt.tight_layout()
    plt.savefig('raw_data_diagnosis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Raw data visualization saved as 'raw_data_diagnosis.png'")

def main():
    """Main diagnostic function."""
    print("=== DAS Raw Data Diagnostic ===")
    
    # Find H5 file
    data_dir = Path("Data")
    h5_files = list(data_dir.rglob("*.h5"))
    
    if not h5_files:
        print("No H5 files found!")
        return
    
    test_file = h5_files[0]
    print(f"Using file: {test_file}")
    
    # 1. Examine raw H5 data
    raw_sample = examine_raw_h5_data(test_file)
    
    # 2. Test chronos loader
    dataset = test_chronos_loader_raw_data()
    
    # 3. Create visualization
    visualize_raw_data_sample(raw_sample)
    
    print("\n=== Diagnostic Complete ===")

if __name__ == "__main__":
    try:
        import torch
        print("✓ PyTorch available")
    except ImportError:
        print("✗ PyTorch not available")
    
    try:
        import matplotlib.pyplot as plt
        print("✓ Matplotlib available")
    except ImportError:
        print("✗ Matplotlib not available")
    
    main()
