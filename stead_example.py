#!/usr/bin/env python3
"""
STEAD Dataset Example Usage

This script demonstrates various ways to use the STEAD dataset loader
with different sampling strategies and filtering options.

Examples include:
1. Basic dataset loading and exploration
2. Event ID-based filtering
3. Magnitude-based filtering
4. Balanced sampling across events
5. Custom sampling strategies
6. Data visualization

Author: AI Assistant
Date: 2025-01-23
"""

import numpy as np
import matplotlib.pyplot as plt
import torch
from stead_dataloader import STEADDataset, create_stead_dataloader, EventBalancedSampler
import logging
from collections import Counter
import seaborn as sns

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_1_basic_loading():
    """Example 1: Basic dataset loading and exploration."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 1: Basic Dataset Loading")
    logger.info("="*60)
    
    # Load basic training dataset
    dataset = STEADDataset(
        split='train',
        component_order='ZNE',
        normalize=True
    )
    
    logger.info(f"Total samples: {len(dataset)}")
    logger.info(f"Unique events: {len(dataset.get_event_ids())}")
    
    # Get a sample
    sample = dataset[0]
    waveform = sample['waveform']
    event_id = sample['event_id']
    metadata = sample['metadata']
    
    logger.info(f"Sample waveform shape: {waveform.shape}")
    logger.info(f"Event ID: {event_id}")
    logger.info(f"Magnitude: {metadata.get('source_magnitude', 'N/A')}")
    logger.info(f"Depth: {metadata.get('source_depth_km', 'N/A')} km")
    
    return dataset


def example_2_event_filtering():
    """Example 2: Filter dataset by specific event IDs."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 2: Event ID Filtering")
    logger.info("="*60)
    
    # First, load dataset to see available events
    full_dataset = STEADDataset(split='train', max_samples_per_event=5)
    all_events = full_dataset.get_event_ids()
    
    logger.info(f"Total events available: {len(all_events)}")
    logger.info(f"First 10 event IDs: {all_events[:10]}")
    
    # Select specific events (first 5 for example)
    selected_events = all_events[:5]
    
    # Create filtered dataset
    filtered_dataset = STEADDataset(
        split='train',
        event_ids=selected_events,
        normalize=True
    )
    
    logger.info(f"Filtered dataset size: {len(filtered_dataset)}")
    logger.info(f"Selected events: {filtered_dataset.get_event_ids()}")
    
    # Show statistics for each event
    stats = filtered_dataset.get_event_statistics()
    for event_id, count in stats.items():
        logger.info(f"  Event {event_id}: {count} traces")
    
    return filtered_dataset


def example_3_magnitude_filtering():
    """Example 3: Filter dataset by magnitude range."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 3: Magnitude-based Filtering")
    logger.info("="*60)
    
    # Load dataset with magnitude filtering
    dataset = STEADDataset(
        split='train',
        magnitude_range=(3.0, 5.0),  # Only magnitude 3-5 events
        normalize=True,
        max_samples_per_event=20
    )
    
    logger.info(f"Dataset size after magnitude filtering: {len(dataset)}")
    logger.info(f"Number of events: {len(dataset.get_event_ids())}")
    
    # Analyze magnitude distribution
    magnitudes = []
    for i in range(min(1000, len(dataset))):  # Sample first 1000
        sample = dataset[i]
        mag = sample['metadata'].get('source_magnitude')
        if mag is not None:
            magnitudes.append(mag)
    
    if magnitudes:
        logger.info(f"Magnitude range in dataset: {min(magnitudes):.2f} - {max(magnitudes):.2f}")
        logger.info(f"Mean magnitude: {np.mean(magnitudes):.2f}")
        logger.info(f"Std magnitude: {np.std(magnitudes):.2f}")
    
    return dataset


def example_4_balanced_sampling():
    """Example 4: Demonstrate balanced sampling across events."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 4: Balanced Event Sampling")
    logger.info("="*60)
    
    # Create dataset with limited samples per event
    dataset = STEADDataset(
        split='train',
        magnitude_range=(2.0, 6.0),
        max_samples_per_event=5,  # Limit to 5 samples per event
        normalize=True
    )
    
    logger.info(f"Dataset created with {len(dataset)} samples")
    logger.info(f"Events: {len(dataset.get_event_ids())}")
    
    # Create balanced dataloader
    dataloader = create_stead_dataloader(
        split='train',
        batch_size=16,
        balanced_sampling=True,
        magnitude_range=(2.0, 6.0),
        max_samples_per_event=5,
        normalize=True
    )
    
    # Analyze event distribution in batches
    event_counts = Counter()
    batch_event_diversity = []
    
    for i, batch in enumerate(dataloader):
        if i >= 10:  # Analyze first 10 batches
            break
            
        event_ids = batch['event_id']
        unique_events = set(event_ids)
        batch_event_diversity.append(len(unique_events))
        
        for event_id in event_ids:
            event_counts[event_id] += 1
        
        logger.info(f"Batch {i+1}: {len(unique_events)} unique events out of {len(event_ids)} samples")
    
    logger.info(f"Average events per batch: {np.mean(batch_event_diversity):.2f}")
    logger.info(f"Most common events in batches: {event_counts.most_common(5)}")
    
    return dataloader


def example_5_custom_analysis():
    """Example 5: Custom analysis of dataset properties."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 5: Custom Dataset Analysis")
    logger.info("="*60)
    
    # Load dataset for analysis
    dataset = STEADDataset(
        split='train',
        normalize=True,
        max_samples_per_event=10
    )
    
    # Analyze various properties
    magnitudes = []
    depths = []
    waveform_lengths = []
    event_types = []
    
    # Sample subset for analysis (to avoid memory issues)
    sample_size = min(1000, len(dataset))
    indices = np.random.choice(len(dataset), sample_size, replace=False)
    
    logger.info(f"Analyzing {sample_size} samples...")
    
    for i in indices:
        sample = dataset[i]
        metadata = sample['metadata']
        waveform = sample['waveform']
        
        # Collect statistics
        if 'source_magnitude' in metadata and metadata['source_magnitude'] is not None:
            magnitudes.append(float(metadata['source_magnitude']))
        
        if 'source_depth_km' in metadata and metadata['source_depth_km'] is not None:
            depths.append(float(metadata['source_depth_km']))
        
        waveform_lengths.append(waveform.shape[1])  # Number of samples
        
        if 'source_event_category' in metadata:
            event_types.append(metadata['source_event_category'])
    
    # Print statistics
    if magnitudes:
        logger.info(f"Magnitude statistics:")
        logger.info(f"  Range: {min(magnitudes):.2f} - {max(magnitudes):.2f}")
        logger.info(f"  Mean: {np.mean(magnitudes):.2f} ± {np.std(magnitudes):.2f}")
    
    if depths:
        logger.info(f"Depth statistics:")
        logger.info(f"  Range: {min(depths):.1f} - {max(depths):.1f} km")
        logger.info(f"  Mean: {np.mean(depths):.1f} ± {np.std(depths):.1f} km")
    
    if waveform_lengths:
        logger.info(f"Waveform length statistics:")
        logger.info(f"  Range: {min(waveform_lengths)} - {max(waveform_lengths)} samples")
        logger.info(f"  Mean: {np.mean(waveform_lengths):.0f} samples")
    
    if event_types:
        type_counts = Counter(event_types)
        logger.info(f"Event type distribution:")
        for event_type, count in type_counts.most_common():
            logger.info(f"  {event_type}: {count} ({count/len(event_types)*100:.1f}%)")
    
    return dataset


def example_6_visualization():
    """Example 6: Visualize some waveforms from the dataset."""
    logger.info("\n" + "="*60)
    logger.info("EXAMPLE 6: Waveform Visualization")
    logger.info("="*60)
    
    # Load dataset
    dataset = STEADDataset(
        split='train',
        component_order='ZNE',
        sampling_rate=100,  # Standardize sampling rate
        normalize=True,
        magnitude_range=(3.0, 5.0),
        max_samples_per_event=5
    )
    
    # Select a few samples for visualization
    n_samples = 4
    fig, axes = plt.subplots(n_samples, 3, figsize=(15, 12))
    fig.suptitle('STEAD Dataset Waveform Examples', fontsize=16)
    
    component_names = ['Z (Vertical)', 'N (North)', 'E (East)']
    
    for i in range(n_samples):
        sample = dataset[i * 10]  # Space out samples
        waveform = sample['waveform'].numpy()
        metadata = sample['metadata']
        event_id = sample['event_id']
        
        # Time axis (assuming 100 Hz sampling rate)
        time = np.arange(waveform.shape[1]) / 100.0
        
        for j, component in enumerate(component_names):
            ax = axes[i, j]
            ax.plot(time, waveform[j], 'b-', linewidth=0.8)
            ax.set_ylabel('Amplitude')
            ax.grid(True, alpha=0.3)
            
            if i == 0:
                ax.set_title(component)
            if i == n_samples - 1:
                ax.set_xlabel('Time (s)')
            
            # Add event info to first column
            if j == 0:
                mag = metadata.get('source_magnitude', 'N/A')
                depth = metadata.get('source_depth_km', 'N/A')
                ax.text(0.02, 0.98, f'Event: {event_id[:8]}...\nMag: {mag}\nDepth: {depth} km', 
                       transform=ax.transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('stead_waveforms_example.png', dpi=150, bbox_inches='tight')
    logger.info("Waveform visualization saved as 'stead_waveforms_example.png'")
    
    return dataset


def main():
    """Run all examples."""
    logger.info("STEAD Dataset Examples")
    logger.info("=" * 80)
    
    try:
        # Run examples
        example_1_basic_loading()
        example_2_event_filtering()
        example_3_magnitude_filtering()
        example_4_balanced_sampling()
        example_5_custom_analysis()
        
        # Only run visualization if matplotlib is available
        try:
            example_6_visualization()
        except ImportError:
            logger.warning("Matplotlib not available, skipping visualization example")
        
        logger.info("\n" + "="*80)
        logger.info("All examples completed successfully!")
        logger.info("="*80)
        
    except Exception as e:
        logger.error(f"Error running examples: {e}")
        raise


if __name__ == "__main__":
    main()
