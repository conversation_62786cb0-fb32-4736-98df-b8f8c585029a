#!/usr/bin/env python3
"""
Demonstration of the frequency band fix for Valencia DAS spectral analysis.
Shows the problem and solution for frequency bands exceeding the spectrogram range.
"""

from enhanced_spectral_visualization import get_appropriate_freq_bands, suggest_analysis_parameters

def demonstrate_frequency_band_issue():
    """Show the frequency band issue and solution."""
    
    print("=" * 80)
    print("FREQUENCY BAND ISSUE DEMONSTRATION")
    print("=" * 80)
    
    # Show the problem with original bands
    print("\n❌ ORIGINAL PROBLEMATIC FREQUENCY BANDS:")
    original_bands = {
        'low_freq': (0.1, 2.0),      # OK
        'traffic': (2.0, 15.0),      # OK for 50 Hz
        'machinery': (15.0, 40.0),   # PROBLEM: Above 25 Hz (50/2)
        'high_freq': (40.0, 80.0)    # PROBLEM: Way above 25 Hz
    }
    
    sample_rate = 50
    nyquist = sample_rate / 2
    
    print(f"Sample rate: {sample_rate} Hz")
    print(f"Nyquist frequency: {nyquist} Hz")
    print(f"Spectrogram frequency range: 0 - {nyquist} Hz")
    print()
    
    for name, (f_low, f_high) in original_bands.items():
        status = "✓ OK" if f_high <= nyquist else "❌ EXCEEDS NYQUIST"
        print(f"  {name}: {f_low}-{f_high} Hz  {status}")
    
    print(f"\n⚠️  PROBLEM: machinery and high_freq bands exceed {nyquist} Hz!")
    print("   These bands won't appear on the spectrogram.")
    
    # Show the solution
    print("\n✅ SOLUTION: Use appropriate frequency bands")
    
    for environment in ['land', 'sea', 'transition']:
        print(f"\n--- {environment.upper()} ENVIRONMENT ---")
        
        for sr in [10, 25, 50]:
            bands = get_appropriate_freq_bands(sr, environment)
            nyq = sr / 2
            
            print(f"\nSample rate: {sr} Hz (Nyquist: {nyq} Hz)")
            for name, (f_low, f_high) in bands.items():
                print(f"  {name}: {f_low}-{f_high} Hz")

def show_sample_rate_recommendations():
    """Show recommended sample rates for different analysis goals."""
    
    print("\n" + "=" * 80)
    print("SAMPLE RATE RECOMMENDATIONS")
    print("=" * 80)
    
    recommendations = [
        {
            'goal': 'Traffic monitoring (land)',
            'sample_rate': 30,
            'reason': 'Captures traffic (2-15 Hz) with some headroom',
            'max_freq': 15
        },
        {
            'goal': 'Marine microseisms (sea)',
            'sample_rate': 10,
            'reason': 'Focuses on microseisms (0.1-2 Hz) efficiently',
            'max_freq': 5
        },
        {
            'goal': 'Mixed land-sea analysis',
            'sample_rate': 25,
            'reason': 'Good compromise for both environments',
            'max_freq': 12
        },
        {
            'goal': 'High-resolution short windows',
            'sample_rate': 50,
            'reason': 'Maximum detail for brief events',
            'max_freq': 25
        },
        {
            'goal': 'Long-term trend analysis',
            'sample_rate': 5,
            'reason': 'Efficient for hours-long analysis',
            'max_freq': 2
        }
    ]
    
    for rec in recommendations:
        print(f"\n🎯 {rec['goal']}:")
        print(f"   Recommended sample rate: {rec['sample_rate']} Hz")
        print(f"   Max useful frequency: {rec['max_freq']} Hz")
        print(f"   Reason: {rec['reason']}")
        
        # Show what bands would be available
        bands = get_appropriate_freq_bands(rec['sample_rate'], 'land')
        print(f"   Available bands: {list(bands.keys())}")

def demonstrate_corrected_analysis():
    """Show how to run corrected spectral analysis."""
    
    print("\n" + "=" * 80)
    print("CORRECTED ANALYSIS EXAMPLES")
    print("=" * 80)
    
    examples = [
        {
            'name': 'Land Traffic (Afternoon)',
            'sample_rate': 30,
            'duration': 600,
            'environment': 'land',
            'time': 14400
        },
        {
            'name': 'Marine Microseisms (Night)',
            'sample_rate': 10,
            'duration': 1800,
            'environment': 'sea',
            'time': 7200
        },
        {
            'name': 'Transition Zone',
            'sample_rate': 25,
            'duration': 900,
            'environment': 'transition',
            'time': 10800
        }
    ]
    
    for ex in examples:
        print(f"\n--- {ex['name']} ---")
        
        # Get suggested parameters
        params = suggest_analysis_parameters(
            ex['sample_rate'], 
            ex['duration'], 
            ex['environment']
        )
        
        print(f"Sample rate: {ex['sample_rate']} Hz")
        print(f"Duration: {ex['duration']} seconds")
        print(f"Environment: {ex['environment']}")
        print(f"Frequency bands:")
        
        for name, (f_low, f_high) in params['freq_bands'].items():
            print(f"  {name}: {f_low}-{f_high} Hz")
        
        print(f"Analysis parameters:")
        print(f"  Context length: {params['context_length']}")
        print(f"  Prediction length: {params['prediction_length']}")
        print(f"  Stride: {params['stride']}")
        print(f"  Max display frequency: {params['max_frequency']:.1f} Hz")
        
        # Show the corrected function call
        print(f"\nCorrected function call:")
        print(f"results = analyze_spectral_anomalies(")
        print(f"    data,")
        print(f"    start_time_seconds={ex['time']},")
        print(f"    duration_seconds={ex['duration']},")
        print(f"    target_sample_rate={ex['sample_rate']},")
        print(f"    freq_bands=params['freq_bands'],")
        print(f"    context_length={params['context_length']},")
        print(f"    prediction_length={params['prediction_length']},")
        print(f"    stride={params['stride']}")
        print(f")")

def main():
    """Main demonstration function."""
    
    print("Valencia DAS Frequency Band Fix Demonstration")
    print("Solving the issue: 'machinery and high frequency are off the spectrogram!'")
    
    # Show the problem and solution
    demonstrate_frequency_band_issue()
    
    # Show sample rate recommendations
    show_sample_rate_recommendations()
    
    # Show corrected analysis examples
    demonstrate_corrected_analysis()
    
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    print("✅ Problem identified: Frequency bands exceeded Nyquist frequency")
    print("✅ Solution implemented: Automatic frequency band adjustment")
    print("✅ Helper functions created: get_appropriate_freq_bands(), suggest_analysis_parameters()")
    print("✅ Visualization fixed: Spectrogram limits now match frequency bands")
    print("\n🎯 Use get_appropriate_freq_bands(sample_rate, environment) for correct bands!")

if __name__ == "__main__":
    main()
