#!/usr/bin/env python3
"""
Test script for enhanced spectral anomaly visualization.
This demonstrates the improved visualization with precise time-frequency anomaly locations.
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from enhanced_spectral_visualization import visualize_spectral_anomalies_enhanced

# Add the notebook functions (you would import these from your notebook)
sys.path.append('.')

def create_mock_spectral_results():
    """Create mock results for testing the visualization."""
    
    # Mock parameters
    start_time = 14400  # 4 hours
    duration = 600      # 10 minutes
    sample_rate = 50    # 50 Hz
    
    # Create mock time and frequency axes
    n_time_bins = 100
    n_freq_bins = 200
    t = np.linspace(start_time, start_time + duration, n_time_bins)
    f = np.linspace(0, sample_rate/2, n_freq_bins)
    
    # Create mock spectrogram
    Sxx = np.random.randn(n_freq_bins, n_time_bins) * 10 + 50
    
    # Create mock segment data
    segment_length = int(duration * sample_rate)
    segment_resampled = np.random.randn(segment_length) * 0.1
    
    # Define frequency bands
    freq_bands = {
        'low_freq': (0.1, 2.0),      # Low frequency rumble
        'traffic': (2.0, 15.0),      # Vehicle traffic
        'machinery': (15.0, 40.0),   # Industrial/mechanical
        'high_freq': (40.0, 80.0)    # High frequency events
    }
    
    # Create mock band anomalies
    band_anomalies = {}
    
    for band_name, (f_low, f_high) in freq_bands.items():
        # Create mock band power
        band_power = np.random.randn(n_time_bins) * 5 + 30
        
        # Create mock analysis windows
        n_windows = 40
        window_starts = np.linspace(start_time + 50, start_time + duration - 50, n_windows)
        mse_errors = np.random.exponential(0.01, n_windows)
        
        # Create some anomalies (top 15% of MSE errors)
        threshold = np.percentile(mse_errors, 85)
        anomaly_mask = mse_errors > threshold
        n_anomalies = np.sum(anomaly_mask)
        
        band_anomalies[band_name] = {
            'band_power': band_power,
            'band_normalized': (band_power - np.mean(band_power)) / np.std(band_power),
            'normalization': {'mean': np.mean(band_power), 'std': np.std(band_power)},
            'window_starts': window_starts,
            'mse_errors': mse_errors,
            'threshold': threshold,
            'anomaly_mask': anomaly_mask,
            'n_anomalies': n_anomalies,
            'anomaly_rate': n_anomalies / len(mse_errors),
            'predictions': [np.random.randn(16) for _ in range(n_windows)],
            'actuals': [np.random.randn(16) for _ in range(n_windows)],
            'freq_range': (f_low, f_high)
        }
    
    # Package mock results
    results = {
        'start_time': start_time,
        'duration': duration,
        'target_sample_rate': sample_rate,
        'downsample_factor': 1.0,
        'spectrogram_f': f,
        'spectrogram_t': t,
        'spectrogram_Sxx': Sxx,
        'freq_bands': freq_bands,
        'band_powers': {name: result['band_power'] for name, result in band_anomalies.items()},
        'band_anomalies': band_anomalies,
        'segment_resampled': segment_resampled
    }
    
    return results

def main():
    """Test the enhanced visualization."""
    print("Creating mock spectral anomaly results...")
    results = create_mock_spectral_results()
    
    print("Testing enhanced visualization...")
    fig, axes = visualize_spectral_anomalies_enhanced(results, "Test_Land_Channel")
    
    print("\n" + "="*60)
    print("ENHANCED VISUALIZATION FEATURES:")
    print("="*60)
    print("✓ Precise time-frequency rectangles for each anomaly")
    print("✓ Numbered markers for anomaly identification")
    print("✓ Consistent time axes across all subplots")
    print("✓ Frequency band boundaries clearly marked")
    print("✓ Enhanced color scheme and labeling")
    print("✓ Detailed anomaly breakdown in console output")
    print("="*60)
    
    # Show comparison with key improvements
    print("\nKEY IMPROVEMENTS OVER ORIGINAL:")
    print("1. Anomalies shown as rectangles at exact time-frequency locations")
    print("2. Numbered markers for easy anomaly identification")
    print("3. Consistent xlim bounds across all subplots")
    print("4. Frequency band boundaries with dashed lines")
    print("5. Enhanced legend with anomaly counts")
    print("6. Detailed console output with anomaly times")
    
    return results, fig, axes

if __name__ == "__main__":
    results, fig, axes = main()
