#!/usr/bin/env python3
"""
STEAD Dataset Loader with Event ID Sampling

This script provides functionality to load the STEAD (STanford EArthquake Dataset) 
from SEISBENCH and create a PyTorch DataLoader that samples based on event ID.

The STEAD dataset contains ~1.2 million time series of local seismic signals,
including both earthquake and non-earthquake events, along with noise examples.

Features:
- Load STEAD dataset from SEISBENCH
- Filter by event ID, magnitude, or other metadata
- Create PyTorch DataLoader with event ID-based sampling
- Support for train/dev/test splits
- Configurable waveform preprocessing

Author: AI Assistant
Date: 2025-01-23
"""

import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader, Sampler
import seisbench.data as sbd
from typing import List, Dict, Optional, Union, Tuple
import logging
from collections import defaultdict
import random

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class STEADDataset(Dataset):
    """
    PyTorch Dataset wrapper for STEAD dataset with event ID-based sampling.
    
    This dataset allows for flexible sampling based on event IDs, enabling
    balanced sampling across different events or specific event selection.
    """
    
    def __init__(
        self,
        split: str = 'train',
        component_order: str = 'ZNE',
        sampling_rate: Optional[int] = None,
        normalize: bool = True,
        event_ids: Optional[List[str]] = None,
        magnitude_range: Optional[Tuple[float, float]] = None,
        max_samples_per_event: Optional[int] = None,
        cache: str = 'trace'
    ):
        """
        Initialize STEAD dataset.
        
        Args:
            split: Dataset split ('train', 'dev', 'test', or None for full dataset)
            component_order: Order of seismic components ('ZNE', 'NEZ', etc.)
            sampling_rate: Target sampling rate (Hz). If None, uses original rate
            normalize: Whether to normalize waveforms
            event_ids: List of specific event IDs to include. If None, includes all
            magnitude_range: Tuple of (min_mag, max_mag) to filter events
            max_samples_per_event: Maximum number of samples per event ID
            cache: Caching strategy ('trace', 'full', or None)
        """
        self.split = split
        self.component_order = component_order
        self.sampling_rate = sampling_rate
        self.normalize = normalize
        self.max_samples_per_event = max_samples_per_event
        
        logger.info(f"Loading STEAD dataset (split: {split})...")
        
        # Load STEAD dataset from SEISBENCH
        try:
            self.stead = sbd.STEAD(
                component_order=component_order,
                sampling_rate=sampling_rate,
                cache=cache
            )
            logger.info(f"Successfully loaded STEAD dataset: {len(self.stead)} traces")
        except Exception as e:
            logger.error(f"Failed to load STEAD dataset: {e}")
            raise
        
        # Apply split if specified
        if split is not None:
            if split == 'train':
                self.stead = self.stead.train()
            elif split == 'dev':
                self.stead = self.stead.dev()
            elif split == 'test':
                self.stead = self.stead.test()
            else:
                raise ValueError(f"Invalid split: {split}. Must be 'train', 'dev', 'test', or None")
            
            logger.info(f"Applied {split} split: {len(self.stead)} traces")
        
        # Apply magnitude filtering if specified
        if magnitude_range is not None:
            min_mag, max_mag = magnitude_range
            mask = (
                (self.stead.metadata['source_magnitude'] >= min_mag) &
                (self.stead.metadata['source_magnitude'] <= max_mag)
            )
            self.stead.filter(mask)
            logger.info(f"Applied magnitude filter [{min_mag}, {max_mag}]: {len(self.stead)} traces")
        
        # Build event ID mapping
        self._build_event_mapping(event_ids)
        
        # Preload waveforms if using cache
        if cache is not None:
            logger.info("Preloading waveforms...")
            self.stead.preload_waveforms(pbar=True)
    
    def _build_event_mapping(self, event_ids: Optional[List[str]] = None):
        """Build mapping from event IDs to trace indices."""
        self.event_to_traces = defaultdict(list)
        
        # Use source_id as event identifier (you might need to adjust this based on STEAD structure)
        # STEAD might use different column names for event IDs
        event_id_column = self._find_event_id_column()
        
        for idx, row in self.stead.metadata.iterrows():
            event_id = str(row[event_id_column])
            self.event_to_traces[event_id].append(idx)
        
        # Filter by specific event IDs if provided
        if event_ids is not None:
            filtered_mapping = {}
            for event_id in event_ids:
                if event_id in self.event_to_traces:
                    filtered_mapping[event_id] = self.event_to_traces[event_id]
                else:
                    logger.warning(f"Event ID {event_id} not found in dataset")
            self.event_to_traces = filtered_mapping
        
        # Limit samples per event if specified
        if self.max_samples_per_event is not None:
            for event_id in self.event_to_traces:
                traces = self.event_to_traces[event_id]
                if len(traces) > self.max_samples_per_event:
                    self.event_to_traces[event_id] = random.sample(traces, self.max_samples_per_event)
        
        # Create flat list of all valid indices
        self.valid_indices = []
        for traces in self.event_to_traces.values():
            self.valid_indices.extend(traces)
        
        self.event_ids = list(self.event_to_traces.keys())
        logger.info(f"Found {len(self.event_ids)} unique events with {len(self.valid_indices)} total traces")
    
    def _find_event_id_column(self) -> str:
        """Find the appropriate column to use as event ID."""
        # Common event ID column names in seismic datasets
        possible_columns = [
            'source_id', 'event_id', 'source_origin_time', 
            'trace_name_original', 'source_event_id'
        ]
        
        for col in possible_columns:
            if col in self.stead.metadata.columns:
                logger.info(f"Using '{col}' as event ID column")
                return col
        
        # Fallback: use source coordinates + time as unique identifier
        logger.warning("No standard event ID column found, creating composite ID")
        self.stead.metadata['composite_event_id'] = (
            self.stead.metadata['source_latitude_deg'].astype(str) + '_' +
            self.stead.metadata['source_longitude_deg'].astype(str) + '_' +
            self.stead.metadata['trace_start_time'].astype(str)
        )
        return 'composite_event_id'
    
    def __len__(self) -> int:
        """Return total number of valid samples."""
        return len(self.valid_indices)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a single sample from the dataset.
        
        Args:
            idx: Index of the sample
            
        Returns:
            Dictionary containing:
            - 'waveform': Seismic waveform tensor [channels, samples]
            - 'event_id': Event ID string
            - 'metadata': Dictionary of metadata
        """
        # Get the actual trace index
        trace_idx = self.valid_indices[idx]
        
        # Get waveform data
        waveform = self.stead.get_waveforms(trace_idx)
        
        # Convert to tensor
        waveform_tensor = torch.from_numpy(waveform).float()
        
        # Normalize if requested
        if self.normalize:
            waveform_tensor = self._normalize_waveform(waveform_tensor)
        
        # Get metadata
        metadata = self.stead.metadata.iloc[trace_idx].to_dict()
        event_id_column = self._find_event_id_column()
        event_id = str(metadata[event_id_column])
        
        return {
            'waveform': waveform_tensor,
            'event_id': event_id,
            'metadata': metadata,
            'trace_idx': trace_idx
        }
    
    def _normalize_waveform(self, waveform: torch.Tensor) -> torch.Tensor:
        """Normalize waveform using z-score normalization."""
        # Normalize each channel independently
        normalized = torch.zeros_like(waveform)
        for i in range(waveform.shape[0]):
            channel = waveform[i]
            mean = torch.mean(channel)
            std = torch.std(channel)
            if std > 0:
                normalized[i] = (channel - mean) / std
            else:
                normalized[i] = channel
        return normalized
    
    def get_event_ids(self) -> List[str]:
        """Get list of all event IDs in the dataset."""
        return self.event_ids
    
    def get_traces_for_event(self, event_id: str) -> List[int]:
        """Get all trace indices for a specific event ID."""
        return self.event_to_traces.get(event_id, [])
    
    def get_event_statistics(self) -> Dict[str, int]:
        """Get statistics about events in the dataset."""
        stats = {}
        for event_id, traces in self.event_to_traces.items():
            stats[event_id] = len(traces)
        return stats


class EventBalancedSampler(Sampler):
    """
    Custom sampler that ensures balanced sampling across events.
    
    This sampler ensures that each batch contains samples from different events,
    promoting diversity in training batches.
    """
    
    def __init__(self, dataset: STEADDataset, batch_size: int, shuffle: bool = True):
        """
        Initialize event-balanced sampler.
        
        Args:
            dataset: STEADDataset instance
            batch_size: Size of each batch
            shuffle: Whether to shuffle the data
        """
        self.dataset = dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
        
        # Group indices by event ID
        self.event_indices = {}
        for i, idx in enumerate(dataset.valid_indices):
            metadata = dataset.stead.metadata.iloc[idx]
            event_id_column = dataset._find_event_id_column()
            event_id = str(metadata[event_id_column])
            
            if event_id not in self.event_indices:
                self.event_indices[event_id] = []
            self.event_indices[event_id].append(i)
        
        self.event_ids = list(self.event_indices.keys())
        
    def __iter__(self):
        """Generate batches with balanced event representation."""
        if self.shuffle:
            # Shuffle indices within each event
            for event_id in self.event_indices:
                random.shuffle(self.event_indices[event_id])
            # Shuffle event order
            random.shuffle(self.event_ids)
        
        # Create batches
        batch = []
        event_idx = 0
        
        while True:
            # Try to add one sample from each event to the batch
            added_to_batch = False
            
            for _ in range(len(self.event_ids)):
                current_event = self.event_ids[event_idx % len(self.event_ids)]
                
                if self.event_indices[current_event]:
                    sample_idx = self.event_indices[current_event].pop(0)
                    batch.append(sample_idx)
                    added_to_batch = True
                    
                    if len(batch) == self.batch_size:
                        yield batch
                        batch = []
                
                event_idx += 1
            
            # If no samples were added, we're done
            if not added_to_batch:
                break
        
        # Yield remaining samples if any
        if batch:
            yield batch
    
    def __len__(self):
        """Return number of batches."""
        return len(self.dataset) // self.batch_size


def create_stead_dataloader(
    split: str = 'train',
    batch_size: int = 32,
    num_workers: int = 4,
    shuffle: bool = True,
    balanced_sampling: bool = False,
    **dataset_kwargs
) -> DataLoader:
    """
    Create a DataLoader for STEAD dataset.
    
    Args:
        split: Dataset split ('train', 'dev', 'test')
        batch_size: Batch size
        num_workers: Number of worker processes
        shuffle: Whether to shuffle data (ignored if balanced_sampling=True)
        balanced_sampling: Whether to use event-balanced sampling
        **dataset_kwargs: Additional arguments for STEADDataset
        
    Returns:
        PyTorch DataLoader
    """
    dataset = STEADDataset(split=split, **dataset_kwargs)
    
    if balanced_sampling:
        sampler = EventBalancedSampler(dataset, batch_size, shuffle=shuffle)
        dataloader = DataLoader(
            dataset,
            batch_sampler=sampler,
            num_workers=num_workers,
            pin_memory=True
        )
    else:
        dataloader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=True,
            drop_last=True
        )
    
    return dataloader


def main():
    """Example usage of STEAD dataset loader."""
    logger.info("STEAD Dataset Loader Example")
    logger.info("=" * 50)
    
    try:
        # Create dataset with specific parameters
        dataset = STEADDataset(
            split='train',
            component_order='ZNE',
            sampling_rate=100,  # Resample to 100 Hz
            normalize=True,
            magnitude_range=(2.0, 6.0),  # Only events with magnitude 2-6
            max_samples_per_event=10,  # Limit to 10 samples per event
            cache='trace'
        )
        
        logger.info(f"Dataset created with {len(dataset)} samples")
        logger.info(f"Number of unique events: {len(dataset.get_event_ids())}")
        
        # Create DataLoader with balanced sampling
        dataloader = create_stead_dataloader(
            split='train',
            batch_size=16,
            balanced_sampling=True,
            component_order='ZNE',
            sampling_rate=100,
            normalize=True,
            magnitude_range=(2.0, 6.0),
            max_samples_per_event=10
        )
        
        logger.info(f"DataLoader created")
        
        # Test loading a few batches
        for i, batch in enumerate(dataloader):
            if i >= 3:  # Only test first 3 batches
                break
                
            waveforms = batch['waveform']
            event_ids = batch['event_id']
            
            logger.info(f"Batch {i+1}:")
            logger.info(f"  Waveform shape: {waveforms.shape}")
            logger.info(f"  Unique events in batch: {len(set(event_ids))}")
            logger.info(f"  Event IDs: {list(set(event_ids))[:5]}...")  # Show first 5
        
        # Show event statistics
        stats = dataset.get_event_statistics()
        logger.info(f"Event statistics (first 10):")
        for i, (event_id, count) in enumerate(list(stats.items())[:10]):
            logger.info(f"  {event_id}: {count} traces")
        
        logger.info("Example completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in example: {e}")
        raise


if __name__ == "__main__":
    main()
