#!/usr/bin/env python3
"""
Demo script showing spectral validation of anomalies.
Creates synthetic DAS data with known anomalous events and validates them spectrally.
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import spectrogram, welch
import pywt

def create_synthetic_das_with_events():
    """Create synthetic DAS data with known seismic-like events."""
    duration = 600  # 10 minutes
    fs = 250  # Hz
    n_samples = int(duration * fs)
    
    # Base signal: low frequency trend + noise
    time_axis = np.linspace(0, duration, n_samples)
    base_signal = 0.1 * np.sin(2 * np.pi * 0.02 * time_axis)  # 0.02 Hz trend
    noise = 0.05 * np.random.randn(n_samples)
    
    # Add realistic seismic events
    events = []
    
    # Event 1: High frequency burst (microseismic)
    event1_start = int(120 * fs)  # 2 minutes
    event1_duration = int(15 * fs)  # 15 seconds
    event1_freq = 25  # Hz
    event1_signal = 0.4 * np.sin(2 * np.pi * event1_freq * time_axis[event1_start:event1_start+event1_duration])
    event1_signal *= np.exp(-np.linspace(0, 3, event1_duration))  # Decay
    base_signal[event1_start:event1_start+event1_duration] += event1_signal
    events.append(('Microseismic', event1_start/fs, event1_duration/fs, event1_freq))
    
    # Event 2: Low frequency event (regional earthquake)
    event2_start = int(300 * fs)  # 5 minutes
    event2_duration = int(45 * fs)  # 45 seconds
    event2_freq = 2  # Hz
    event2_signal = 0.3 * np.sin(2 * np.pi * event2_freq * time_axis[event2_start:event2_start+event2_duration])
    event2_signal *= np.exp(-np.linspace(0, 1, event2_duration))  # Slow decay
    base_signal[event2_start:event2_start+event2_duration] += event2_signal
    events.append(('Regional Event', event2_start/fs, event2_duration/fs, event2_freq))
    
    # Event 3: Broadband event (local earthquake)
    event3_start = int(450 * fs)  # 7.5 minutes
    event3_duration = int(30 * fs)  # 30 seconds
    # Multiple frequency components
    for freq in [5, 12, 20]:
        event3_signal = 0.2 * np.sin(2 * np.pi * freq * time_axis[event3_start:event3_start+event3_duration])
        event3_signal *= np.exp(-np.linspace(0, 2, event3_duration))
        base_signal[event3_start:event3_start+event3_duration] += event3_signal
    events.append(('Local Earthquake', event3_start/fs, event3_duration/fs, 'Broadband'))
    
    synthetic_data = base_signal + noise
    
    return synthetic_data, events, fs

def compute_spectrogram_demo(data, fs=250):
    """Compute spectrogram for demo."""
    f, t, Sxx = spectrogram(data, fs=fs, nperseg=512, noverlap=256)
    return f, t, 10 * np.log10(Sxx + 1e-12)

def extract_event_segments(data, events, fs, context_duration=20):
    """Extract segments around known events."""
    event_segments = []
    normal_segments = []
    
    context_samples = int(context_duration * fs)
    
    # Extract event segments
    for event_name, start_time, duration, freq in events:
        center_sample = int((start_time + duration/2) * fs)
        start_idx = max(0, center_sample - context_samples//2)
        end_idx = min(len(data), center_sample + context_samples//2)
        
        if end_idx - start_idx >= context_samples//2:
            event_segments.append((data[start_idx:end_idx], event_name, freq))
    
    # Extract normal segments (avoiding events)
    event_times = [(start, start + dur) for _, start, dur, _ in events]
    
    for i in range(len(events)):
        # Find a quiet period
        search_start = 60 + i * 150  # Spread out normal segments
        if not any(start <= search_start <= end for start, end in event_times):
            center_sample = int(search_start * fs)
            start_idx = max(0, center_sample - context_samples//2)
            end_idx = min(len(data), center_sample + context_samples//2)
            
            if end_idx - start_idx >= context_samples//2:
                normal_segments.append(data[start_idx:end_idx])
    
    return event_segments, normal_segments

def main():
    print("=== Spectral Anomaly Validation Demo ===")
    
    # Create synthetic data with known events
    print("Creating synthetic DAS data with known seismic events...")
    synthetic_data, events, fs = create_synthetic_das_with_events()
    
    print(f"Generated {len(synthetic_data)} samples ({len(synthetic_data)/fs:.1f} seconds)")
    print(f"Embedded {len(events)} seismic events:")
    for event_name, start_time, duration, freq in events:
        print(f"  {event_name}: {start_time:.1f}s-{start_time+duration:.1f}s, {freq} Hz")
    
    # Extract event and normal segments
    event_segments, normal_segments = extract_event_segments(synthetic_data, events, fs)
    
    print(f"\nExtracted {len(event_segments)} event segments, {len(normal_segments)} normal segments")
    
    # Create comprehensive visualization
    fig, axes = plt.subplots(3, 3, figsize=(18, 15))
    fig.suptitle('Spectral Validation of Synthetic DAS Anomalies', fontsize=16)
    
    # 1. Full time series with events marked
    ax1 = axes[0, 0]
    time_axis = np.arange(len(synthetic_data)) / fs
    ax1.plot(time_axis, synthetic_data, 'b-', alpha=0.7, linewidth=0.8)

    # Mark events
    colors = ['red', 'orange', 'purple']
    for i, (event_name, start_time, duration, freq) in enumerate(events):
        ax1.axvspan(start_time, start_time + duration, alpha=0.3, color=colors[i], label=event_name)

    ax1.set_title('Synthetic DAS Data with Seismic Events')
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Strain Rate')
    ax1.legend()
    ax1.grid(True, alpha=0.3)\n",
    "    \n",
    "    # 2. Full spectrogram\n",
    "    ax2 = axes[0, 1]\n",
    "    f, t, Sxx = compute_spectrogram_demo(synthetic_data, fs)\n",
    "    im2 = ax2.pcolormesh(t, f, Sxx, shading='gouraud', cmap='viridis')\n",
    "    ax2.set_title('Full Spectrogram')\n",
    "    ax2.set_xlabel('Time (s)')\n",
    "    ax2.set_ylabel('Frequency (Hz)')\n",
    "    ax2.set_ylim([0, 50])\n",
    "    plt.colorbar(im2, ax=ax2, label='Power (dB)')\n",
    "    \n",
    "    # Mark events on spectrogram\n",
    "    for event_name, start_time, duration, freq in events:\n",
    "        ax2.axvline(start_time, color='white', linestyle='--', alpha=0.8)\n",
    "        ax2.axvline(start_time + duration, color='white', linestyle='--', alpha=0.8)\n",
    "    \n",
    "    # 3. Power spectral density comparison\n",
    "    ax3 = axes[0, 2]\n",
    "    \n",
    "    # Compute PSDs for event and normal segments\n",
    "    event_psds = []\n",
    "    for segment, _, _ in event_segments:\n",
    "        f_psd, psd = welch(segment, fs=fs, nperseg=512)\n",
    "        event_psds.append(10 * np.log10(psd + 1e-12))\n",
    "    \n",
    "    normal_psds = []\n",
    "    for segment in normal_segments:\n",
    "        f_psd, psd = welch(segment, fs=fs, nperseg=512)\n",
    "        normal_psds.append(10 * np.log10(psd + 1e-12))\n",
    "    \n",
    "    if event_psds and normal_psds:\n",
    "        event_psd_mean = np.mean(event_psds, axis=0)\n",
    "        event_psd_std = np.std(event_psds, axis=0)\n",
    "        normal_psd_mean = np.mean(normal_psds, axis=0)\n",
    "        normal_psd_std = np.std(normal_psds, axis=0)\n",
    "        \n",
    "        ax3.plot(f_psd, event_psd_mean, 'r-', linewidth=2, label=f'Events (n={len(event_psds)})')\n",
    "        ax3.fill_between(f_psd, event_psd_mean - event_psd_std, event_psd_mean + event_psd_std, \n",
    "                        alpha=0.3, color='red')\n",
    "        \n",
    "        ax3.plot(f_psd, normal_psd_mean, 'b-', linewidth=2, label=f'Normal (n={len(normal_psds)})')\n",
    "        ax3.fill_between(f_psd, normal_psd_mean - normal_psd_std, normal_psd_mean + normal_psd_std, \n",
    "                        alpha=0.3, color='blue')\n",
    "        \n",
    "        ax3.set_title('Power Spectral Density Comparison')\n",
    "        ax3.set_xlabel('Frequency (Hz)')\n",
    "        ax3.set_ylabel('Power (dB)')\n",
    "        ax3.set_xlim([0, 50])\n",
    "        ax3.legend()\n",
    "        ax3.grid(True, alpha=0.3)\n",
    "    \n",
    "    # 4-6. Individual event spectrograms\n",
    "    for i, (segment, event_name, freq) in enumerate(event_segments[:3]):\n",
    "        ax = axes[1, i]\n",
    "        f_seg, t_seg, Sxx_seg = compute_spectrogram_demo(segment, fs)\n",
    "        im = ax.pcolormesh(t_seg, f_seg, Sxx_seg, shading='gouraud', cmap='viridis')\n",
    "        ax.set_title(f'{event_name} Segment')\n",
    "        ax.set_xlabel('Time (s)')\n",
    "        ax.set_ylabel('Frequency (Hz)')\n",
    "        ax.set_ylim([0, 50])\n",
    "        plt.colorbar(im, ax=ax, label='Power (dB)')\n",
    "    \n",
    "    # 7-9. Normal segment spectrograms\n",
    "    for i, segment in enumerate(normal_segments[:3]):\n",
    "        ax = axes[2, i]\n",
    "        f_seg, t_seg, Sxx_seg = compute_spectrogram_demo(segment, fs)\n",
    "        im = ax.pcolormesh(t_seg, f_seg, Sxx_seg, shading='gouraud', cmap='viridis')\n",
    "        ax.set_title(f'Normal Segment {i+1}')\n",
    "        ax.set_xlabel('Time (s)')\n",
    "        ax.set_ylabel('Frequency (Hz)')\n",
    "        ax.set_ylim([0, 50])\n",
    "        plt.colorbar(im, ax=ax, label='Power (dB)')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.savefig('spectral_validation_demo.png', dpi=300, bbox_inches='tight')\n",
    "    plt.show()\n",
    "    \n",
    "    # Validation analysis\n",
    "    print(\"\\n=== Spectral Validation Results ===\")\n",
    "    \n",
    "    if event_psds and normal_psds:\n",
    "        spectral_ratio = event_psd_mean - normal_psd_mean\n",
    "        max_diff_idx = np.argmax(np.abs(spectral_ratio))\n",
    "        max_diff_freq = f_psd[max_diff_idx]\n",
    "        max_diff_value = spectral_ratio[max_diff_idx]\n",
    "        \n",
    "        print(f\"Strongest spectral difference: {max_diff_value:.1f} dB at {max_diff_freq:.1f} Hz\")\n",
    "        \n",
    "        if abs(max_diff_value) > 5:\n",
    "            print(\"✓ STRONG VALIDATION: Clear spectral signature confirms real seismic events\")\n",
    "        elif abs(max_diff_value) > 2:\n",
    "            print(\"✓ MODERATE VALIDATION: Spectral differences suggest real events\")\n",
    "        else:\n",
    "            print(\"⚠ WEAK VALIDATION: Limited spectral differences\")\n",
    "        \n",
    "        # Frequency band analysis\n",
    "        bands = {'Low (0-5 Hz)': (0, 5), 'Mid (5-25 Hz)': (5, 25), 'High (25-50 Hz)': (25, 50)}\n",
    "        \n",
    "        print(\"\\nFrequency band analysis:\")\n",
    "        for band_name, (f_low, f_high) in bands.items():\n",
    "            band_mask = (f_psd >= f_low) & (f_psd <= f_high)\n",
    "            if np.any(band_mask):\n",
    "                band_diff = np.mean(spectral_ratio[band_mask])\n",
    "                print(f\"  {band_name}: {band_diff:+.1f} dB ({'Enhanced' if band_diff > 0 else 'Reduced'} in events)\")\n",
    "    \n",
    "    print(\"\\n✓ Spectral validation demo complete!\")\n",
    "    print(\"✓ Visualization saved as 'spectral_validation_demo.png'\")\n",
    "    print(\"\\nThis demonstrates how spectral analysis can validate whether detected\")\n",
    "    print(\"anomalies represent real seismic events or just prediction artifacts.\")\n",
    "\n",
    "if __name__ == \"__main__\":\n",
    "    main()
