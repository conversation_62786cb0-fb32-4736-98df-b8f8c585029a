{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Valencia DAS Interactive Anomaly Detection\n", "\n", "This notebook provides an interactive environment for exploring anomaly detection in Valencia DAS data using Chronos T5 time series forecasting.\n", "\n", "## Features:\n", "- **Real Valencia DAS data loading**\n", "- **Chronos T5 time series prediction**\n", "- **Spectral anomaly detection**\n", "- **Interactive anomaly explorer with zoom/pan**\n", "- **Frequency band analysis**\n", "- **Whitened spectrograms for better anomaly visibility**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Intel MKL WARNING: Support of Intel(R) Streaming SIMD Extensions 4.2 (Intel(R) SSE4.2) enabled only processors has been deprecated. Intel oneAPI Math Kernel Library 2025.0 will require Intel(R) Advanced Vector Extensions (Intel(R) AVX) instructions.\n", "Intel MKL WARNING: Support of Intel(R) Streaming SIMD Extensions 4.2 (Intel(R) SSE4.2) enabled only processors has been deprecated. Intel oneAPI Math Kernel Library 2025.0 will require Intel(R) Advanced Vector Extensions (Intel(R) AVX) instructions.\n", "✓ Using notebook backend for interactive plots\n", "✓ Enhanced spectral visualization function ready\n", "✓ Frequency band suggestion functions ready\n", "✓ Log scale frequency visualization ready\n", "✓ Spectrogram whitening comparison ready\n", "✓ Interactive anomaly explorer ready\n", "✓ All imports successful\n"]}], "source": ["import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import h5py\n", "from pathlib import Path\n", "from scipy.signal import spectrogram, resample\n", "import torch\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Enable interactive plots\n", "# Try different backends in order of preference\n", "try:\n", "    %matplotlib widget\n", "    print('✓ Using widget backend for interactive plots')\n", "except:\n", "    try:\n", "        %matplotlib notebook\n", "        print('✓ Using notebook backend for interactive plots')\n", "    except:\n", "        %matplotlib inline\n", "        print('⚠️  Using inline backend - plots will not be interactive')\n", "\n", "# Import our custom modules\n", "from enhanced_spectral_visualization import (\n", "    visualize_spectral_anomalies_enhanced, \n", "    get_appropriate_freq_bands, \n", "    suggest_analysis_parameters,\n", "    compare_frequency_scales,\n", "    compare_whitened_spectrograms,\n", "    create_interactive_anomaly_explorer\n", ")\n", "\n", "print(\"✓ All imports successful\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> Chronos Model"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ ChronosPipeline imported successfully\n", "Loading Chronos model...\n", "✓ Chronos model loaded on cpu\n", "✓ USING REAL CHRONOS MODEL\n"]}], "source": ["# Try to load Chronos model\n", "try:\n", "    from chronos import ChronosPipeline\n", "    CHRONOS_AVAILABLE = True\n", "    print(\"✓ ChronosPipeline imported successfully\")\n", "except ImportError as e:\n", "    print(f\"❌ Chronos not available: {e}\")\n", "    print(\"To install: pip install chronos-forecasting\")\n", "    CHRONOS_AVAILABLE = False\n", "\n", "# Load Chronos model if available\n", "pipeline = None\n", "if CHRONOS_AVAILABLE:\n", "    try:\n", "        print(\"Loading Chronos model...\")\n", "        device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "        pipeline = ChronosPipeline.from_pretrained(\n", "            \"amazon/chronos-t5-tiny\",\n", "            device_map=device,\n", "            torch_dtype=torch.bfloat16 if device.type == \"cuda\" else torch.float32,\n", "        )\n", "\n", "        print(f\"✓ Chronos model loaded on {device}\")\n", "        print(\"✓ USING REAL CHRONOS MODEL\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed to load Chronos: {e}\")\n", "        print(\"⚠️  Will use mock predictions instead\")\n", "        pipeline = None\n", "else:\n", "    print(\"⚠️  Using mock predictions (Chronos not available)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Define Prediction Function"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Prediction functions ready\n"]}], "source": ["def predict_chronos(context, prediction_length=32):\n", "    \"\"\"Make prediction using Chronos or enhanced mock.\"\"\"\n", "    if pipeline is None:\n", "        # Enhanced mock prediction\n", "        return mock_prediction(context, prediction_length)\n", "    \n", "    try:\n", "        # Use real Chronos\n", "        context_tensor = torch.tensor(context, dtype=torch.float32).unsqueeze(0)\n", "        forecast = pipeline.predict(\n", "            context_tensor,\n", "            prediction_length=prediction_length,\n", "            num_samples=1\n", "        )\n", "        return forecast[0].cpu().numpy().flatten()\n", "    except Exception as e:\n", "        print(f\"❌ Chronos prediction failed: {e}\")\n", "        return mock_prediction(context, prediction_length)\n", "\n", "def mock_prediction(context, prediction_length):\n", "    \"\"\"Conservative mock prediction that will fail on anomalies.\"\"\"\n", "    if len(context) < 4:\n", "        base_value = context[-1] if len(context) > 0 else 0\n", "        prediction = np.full(prediction_length, base_value)\n", "        prediction += np.random.normal(0, 0.001, prediction_length)\n", "        return prediction\n", "    \n", "    # Use only stable part of context (ignore recent changes)\n", "    stable_context = context[:-max(1, len(context)//4)]\n", "    if len(stable_context) < 2:\n", "        stable_context = context[:2]\n", "    \n", "    # Conservative baseline from stable period\n", "    baseline = np.mean(stable_context)\n", "    \n", "    # Very small trend from stable period\n", "    if len(stable_context) >= 2:\n", "        mid_point = len(stable_context) // 2\n", "        early_mean = np.mean(stable_context[:mid_point]) if mid_point > 0 else stable_context[0]\n", "        late_mean = np.mean(stable_context[mid_point:]) if mid_point < len(stable_context) else stable_context[-1]\n", "        trend = (late_mean - early_mean) / len(stable_context) * 0.1  # Heavily dampened\n", "    else:\n", "        trend = 0\n", "    \n", "    # Generate conservative predictions\n", "    prediction = []\n", "    for i in range(prediction_length):\n", "        decay_factor = 0.8 ** i\n", "        pred_value = baseline + trend * (i + 1) * decay_factor\n", "        prediction.append(pred_value)\n", "    \n", "    prediction = np.array(prediction)\n", "    noise_level = np.std(stable_context) * 0.02 if len(stable_context) > 1 else 0.001\n", "    prediction += np.random.normal(0, noise_level, prediction_length)\n", "    \n", "    return prediction\n", "\n", "print(\"✓ Prediction functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Loading Functions"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Data loading functions ready\n"]}], "source": ["def load_valencia_data(data_dir=\"Data\", max_files=6, target_sensor=None):\n", "    \"\"\"Load Valencia DAS data from H5 files.\"\"\"\n", "    data_dir = Path(data_dir)\n", "    h5_files = sorted(list(data_dir.rglob(\"*.h5\")))\n", "    print(f\"Found {len(h5_files)} H5 files\")\n", "    \n", "    if len(h5_files) == 0:\n", "        raise FileNotFoundError(\"No H5 files found\")\n", "    \n", "    # Determine target sensor\n", "    if target_sensor is None:\n", "        with h5py.File(h5_files[0], 'r') as f:\n", "            das_name = list(f.keys())[0]\n", "            sr_dataset = f[f\"{das_name}/Source1/Zone1/SR_Valencia\"]\n", "            n_sensors = sr_dataset.shape[2]\n", "        target_sensor = n_sensors // 2\n", "        print(f\"Using middle sensor: {target_sensor} (out of {n_sensors} sensors)\")\n", "    \n", "    # Load data from files\n", "    all_data = []\n", "    files_to_load = min(max_files, len(h5_files))\n", "    print(f\"Loading sensor {target_sensor} from {files_to_load} files...\")\n", "    \n", "    for i, file_path in enumerate(h5_files[:files_to_load]):\n", "        try:\n", "            with h5py.File(file_path, 'r') as f:\n", "                das_name = list(f.keys())[0]\n", "                sr_dataset = f[f\"{das_name}/Source1/Zone1/SR_Valencia\"]\n", "                dims = sr_dataset.shape\n", "                nb_block = dims[0]\n", "                sampling_frequency = 250\n", "                \n", "                sensor_data_blocks = []\n", "                for tt in range(nb_block):\n", "                    block_data = sr_dataset[tt, :sampling_frequency, target_sensor]\n", "                    sensor_data_blocks.append(block_data)\n", "                \n", "                sensor_data = np.concatenate(sensor_data_blocks)\n", "                all_data.append(sensor_data.astype(np.float32))\n", "                print(f\"  Loaded {file_path.name}: {len(sensor_data)} samples\")\n", "        except Exception as e:\n", "            print(f\"  Warning: Failed to load {file_path.name}: {e}\")\n", "            continue\n", "    \n", "    if not all_data:\n", "        raise ValueError(\"No data could be loaded\")\n", "    \n", "    # Concatenate all data\n", "    full_data = np.concatenate(all_data)\n", "    total_duration = len(full_data) / 250\n", "    \n", "    print(f\"✓ Loaded {len(all_data)} files successfully\")\n", "    print(f\"✓ Total data: {len(full_data)} samples ({total_duration/3600:.1f} hours)\")\n", "    print(f\"✓ Sensor {target_sensor} ready for analysis\")\n", "    \n", "    return full_data, target_sensor\n", "\n", "def create_synthetic_data():\n", "    \"\"\"Create synthetic DAS data with known anomalies for testing.\"\"\"\n", "    print(\"Creating synthetic Valencia DAS data...\")\n", "    \n", "    duration_hours = 2\n", "    sample_rate = 250\n", "    n_samples = int(duration_hours * 3600 * sample_rate)\n", "    \n", "    t = np.arange(n_samples) / sample_rate\n", "    \n", "    # Base signals\n", "    traffic = 0.1 * np.sin(2 * np.pi * 5 * t) + 0.05 * np.sin(2 * np.pi * 12 * t)\n", "    rumble = 0.2 * np.sin(2 * np.pi * 0.5 * t) + 0.1 * np.sin(2 * np.pi * 1.5 * t)\n", "    \n", "    # Add strong anomalies\n", "    anomaly_events = [\n", "        {'time': 1800, 'duration': 45, 'freq': 8, 'amplitude': 2.0, 'type': 'high_freq_burst'},\n", "        {'time': 3600, 'duration': 60, 'freq': 2, 'amplitude': 3.0, 'type': 'low_freq_surge'}, \n", "        {'time': 5400, 'duration': 30, 'freq': 12, 'amplitude': 1.5, 'type': 'machinery_spike'},\n", "    ]\n", "    \n", "    for event in anomaly_events:\n", "        start_idx = int(event['time'] * sample_rate)\n", "        end_idx = start_idx + int(event['duration'] * sample_rate)\n", "        if end_idx < n_samples:\n", "            if event['type'] == 'high_freq_burst':\n", "                anomaly_signal = event['amplitude'] * np.sin(2 * np.pi * event['freq'] * t[start_idx:end_idx])\n", "            elif event['type'] == 'low_freq_surge':\n", "                ramp = np.linspace(0, 1, end_idx - start_idx)\n", "                anomaly_signal = event['amplitude'] * ramp * np.sin(2 * np.pi * event['freq'] * t[start_idx:end_idx])\n", "            else:  # machinery_spike\n", "                pulse_pattern = np.where(np.sin(2 * np.pi * 0.5 * t[start_idx:end_idx]) > 0, 1, 0)\n", "                anomaly_signal = event['amplitude'] * pulse_pattern * np.sin(2 * np.pi * event['freq'] * t[start_idx:end_idx])\n", "            \n", "            traffic[start_idx:end_idx] += anomaly_signal\n", "    \n", "    synthetic_data = traffic + rumble + np.random.normal(0, 0.02, n_samples)\n", "    \n", "    print(f\"✓ Created {duration_hours} hours of synthetic data\")\n", "    print(f\"✓ Anomaly events:\")\n", "    for event in anomaly_events:\n", "        print(f\"    {event['time']}s: {event['type']} ({event['duration']}s, {event['freq']} Hz)\")\n", "    \n", "    return synthetic_data, 1488\n", "\n", "print(\"✓ Data loading functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Spectral Anomaly Analysis Function"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ Analysis function ready\n"]}], "source": ["def analyze_spectral_anomalies(data, start_time_seconds, duration_seconds, \n", "                              target_sample_rate, original_sample_rate=250, \n", "                              freq_bands=None, context_length=64, \n", "                              prediction_length=16, stride=8):\n", "    \"\"\"Analyze spectral anomalies in DAS data.\"\"\"\n", "    \n", "    print(f\"\\n=== Spectral Anomaly Analysis ===\")\n", "    print(f\"Time window: {start_time_seconds}s + {duration_seconds}s\")\n", "    print(f\"Sample rate: {original_sample_rate} Hz → {target_sample_rate} Hz\")\n", "    \n", "    # Get appropriate frequency bands if not provided\n", "    if freq_bands is None:\n", "        freq_bands = get_appropriate_freq_bands(target_sample_rate, 'land')\n", "        print(f\"Using automatic frequency bands: {list(freq_bands.keys())}\")\n", "    \n", "    # Extract and preprocess segment\n", "    start_idx = int(start_time_seconds * original_sample_rate)\n", "    end_idx = int((start_time_seconds + duration_seconds) * original_sample_rate)\n", "    \n", "    if end_idx > len(data):\n", "        end_idx = len(data)\n", "        duration_seconds = (end_idx - start_idx) / original_sample_rate\n", "        print(f\"Truncated to available data: {duration_seconds:.1f}s\")\n", "    \n", "    segment_original = data[start_idx:end_idx]\n", "    \n", "    # Downsample if needed\n", "    if target_sample_rate != original_sample_rate:\n", "        downsample_factor = original_sample_rate / target_sample_rate\n", "        new_length = int(len(segment_original) / downsample_factor)\n", "        segment_resampled = resample(segment_original, new_length)\n", "        print(f\"Downsampled: {len(segment_original)} → {len(segment_resampled)} samples\")\n", "    else:\n", "        segment_resampled = segment_original\n", "        downsample_factor = 1\n", "    \n", "    # Compute spectrogram\n", "    nperseg = min(256, len(segment_resampled) // 8)\n", "    noverlap = nperseg // 2\n", "    f, t, Sxx = spectrogram(segment_resampled, fs=target_sample_rate,\n", "                           nperseg=nperseg, noverlap=noverlap)\n", "    \n", "    Sxx_db = 10 * np.log10(Sxx + 1e-12)\n", "    \n", "    # Whiten the spectrogram\n", "    print(f\"Whitening spectrogram (subtracting frequency-wise mean)...\")\n", "    freq_means = np.mean(Sxx_db, axis=1, keepdims=True)\n", "    Sxx_whitened = Sxx_db - freq_means\n", "    \n", "    print(f\"  Original power range: [{np.min(Sxx_db):.1f}, {np.max(Sxx_db):.1f}] dB\")\n", "    print(f\"  Whitened power range: [{np.min(Sxx_whitened):.1f}, {np.max(Sxx_whitened):.1f}] dB\")\n", "    \n", "    t_spectrogram = t + start_time_seconds\n", "    print(f\"Spectrogram: {len(f)} frequencies × {len(t)} time bins\")\n", "    \n", "    # Analyze each frequency band\n", "    band_anomalies = {}\n", "    \n", "    for band_name, (f_low, f_high) in freq_bands.items():\n", "        print(f\"\\nAnalyzing {band_name} band ({f_low}-{f_high} Hz)...\")\n", "        \n", "        # Find frequency indices\n", "        freq_mask = (f >= f_low) & (f <= f_high)\n", "        if not np.any(freq_mask):\n", "            print(f\"  Warning: No frequencies found in band\")\n", "            continue\n", "        \n", "        # Extract power in this frequency band\n", "        band_power = np.mean(Sxx_whitened[freq_mask, :], axis=0)\n", "        \n", "        # Normalize for prediction\n", "        band_mean = np.mean(band_power)\n", "        band_std = np.std(band_power)\n", "        band_normalized = (band_power - band_mean) / band_std\n", "        \n", "        # Apply prediction-based anomaly detection\n", "        total_length = context_length + prediction_length\n", "        n_windows = (len(band_normalized) - total_length) // stride + 1\n", "        \n", "        window_starts = []\n", "        mse_errors = []\n", "        \n", "        for i in range(n_windows):\n", "            window_start = i * stride\n", "            context_end = window_start + context_length\n", "            target_end = window_start + total_length\n", "            \n", "            if target_end > len(band_normalized):\n", "                break\n", "            \n", "            context = band_normalized[window_start:context_end]\n", "            target = band_normalized[context_end:target_end]\n", "            \n", "            prediction = predict_chronos(context, prediction_length)\n", "            mse_error = np.mean((prediction - target) ** 2)\n", "            \n", "            window_time = t_spectrogram[window_start] if window_start < len(t_spectrogram) else t_spectrogram[-1]\n", "            window_starts.append(window_time)\n", "            mse_errors.append(mse_error)\n", "        \n", "        window_starts = np.array(window_starts)\n", "        mse_errors = np.array(mse_errors)\n", "        \n", "        # Check if we have analysis windows\n", "        if len(mse_errors) == 0:\n", "            print(f\"  Warning: No analysis windows created\")\n", "            band_anomalies[band_name] = {\n", "                'band_power': band_power,\n", "                'window_starts': np.array([]),\n", "                'mse_errors': np.array([]),\n", "                'threshold': 0.0,\n", "                'anomaly_mask': np.array([], dtype=bool),\n", "                'n_anomalies': 0,\n", "                'anomaly_rate': 0.0,\n", "                'freq_range': (f_low, f_high)\n", "            }\n", "            continue\n", "        \n", "        # Detect anomalies\n", "        threshold = np.percentile(mse_errors, 85)\n", "        anomaly_mask = mse_errors > threshold\n", "        n_anomalies = np.sum(anomaly_mask)\n", "        \n", "        # Debug information\n", "        print(f\"  MSE errors: min={np.min(mse_errors):.6f}, max={np.max(mse_errors):.6f}, mean={np.mean(mse_errors):.6f}\")\n", "        print(f\"  Threshold (85th percentile): {threshold:.6f}\")\n", "        print(f\"  Anomalies: {n_anomalies}/{len(mse_errors)} ({100*n_anomalies/len(mse_errors):.1f}%)\")\n", "        \n", "        # Try lower threshold if no anomalies\n", "        if n_anomalies == 0:\n", "            threshold = np.percentile(mse_errors, 75)\n", "            anomaly_mask = mse_errors > threshold\n", "            n_anomalies = np.sum(anomaly_mask)\n", "            print(f\"  Trying 75th percentile: {threshold:.6f}, anomalies: {n_anomalies}\")\n", "        \n", "        band_anomalies[band_name] = {\n", "            'band_power': band_power,\n", "            'band_normalized': band_normalized,\n", "            'normalization': {'mean': band_mean, 'std': band_std},\n", "            'window_starts': window_starts,\n", "            'mse_errors': mse_errors,\n", "            'threshold': threshold,\n", "            'anomaly_mask': anomaly_mask,\n", "            'n_anomalies': n_anomalies,\n", "            'anomaly_rate': n_anomalies / len(mse_errors),\n", "            'freq_range': (f_low, f_high)\n", "        }\n", "        \n", "        print(f\"  {n_anomalies} anomalies ({100*band_anomalies[band_name]['anomaly_rate']:.1f}%)\")\n", "        \n", "        # Show worst prediction errors\n", "        if len(mse_errors) > 0:\n", "            worst_indices = np.argsort(mse_errors)[-3:]\n", "            print(f\"  Worst prediction errors:\")\n", "            for idx in reversed(worst_indices):\n", "                if idx < len(window_starts):\n", "                    error_time = window_starts[idx]\n", "                    error_value = mse_errors[idx]\n", "                    is_anomaly = \"🚨 ANOMALY\" if anomaly_mask[idx] else \"normal\"\n", "                    print(f\"    t={error_time:.1f}s: MSE={error_value:.6f} ({is_anomaly})\")\n", "    \n", "    # Package results\n", "    results = {\n", "        'start_time': start_time_seconds,\n", "        'duration': duration_seconds,\n", "        'target_sample_rate': target_sample_rate,\n", "        'downsample_factor': downsample_factor,\n", "        'spectrogram_f': f,\n", "        'spectrogram_t': t_spectrogram,\n", "        'spectrogram_Sxx': Sxx_whitened,\n", "        'spectrogram_Sxx_original': Sxx_db,\n", "        'freq_bands': freq_bands,\n", "        'band_anomalies': band_anomalies,\n", "        'segment_resampled': segment_resampled\n", "    }\n", "    \n", "    print(f\"\\n✓ Spectral analysis complete\")\n", "    return results\n", "\n", "print(\"✓ Analysis function ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to load REAL Valencia DAS data...\n", "Found 59 H5 files\n", "Using middle sensor: 1488 (out of 2977 sensors)\n", "Loading sensor 1488 from 6 files...\n", "  Loaded SR_Valencia_2020-09-01_13-21-28_UTC_10mins.h5: 150250 samples\n", "  Loaded SR_Valencia_2020-09-01_13-31-28_UTC_10mins.h5: 150250 samples\n", "  Loaded SR_Valencia_2020-09-01_13-41-28_UTC_10mins.h5: 150250 samples\n", "  Loaded SR_Valencia_2020-09-01_13-51-28_UTC_10mins.h5: 150250 samples\n", "  Loaded SR_Valencia_2020-09-01_14-01-28_UTC_10mins.h5: 150250 samples\n", "  Loaded SR_Valencia_2020-09-01_14-21-29_UTC_10mins.h5: 150250 samples\n", "✓ Loaded 6 files successfully\n", "✓ Total data: 901500 samples (1.0 hours)\n", "✓ Sensor 1488 ready for analysis\n", "✓ Using REAL data from sensor 1488\n", "\n", "Data loaded: 901500 samples (1.00 hours)\n"]}], "source": ["# Try to load real data first, fall back to synthetic\n", "try:\n", "    print(\"Attempting to load REAL Valencia DAS data...\")\n", "    data, sensor_id = load_valencia_data(max_files=6, target_sensor=None)\n", "    data_type = \"REAL\"\n", "    print(f\"✓ Using REAL data from sensor {sensor_id}\")\n", "except Exception as e:\n", "    print(f\"Failed to load real data: {e}\")\n", "    print(\"Falling back to synthetic data...\")\n", "    data, sensor_id = create_synthetic_data()\n", "    data_type = \"SYNTHETIC\"\n", "    print(f\"✓ Using SYNTHETIC data\")\n", "\n", "print(f\"\\nData loaded: {len(data)} samples ({len(data)/250/3600:.2f} hours)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Configure Analysis Parameters"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total data duration: 1.0 hours\n", "\n", "Analysis configuration:\n", "  Data type: REAL\n", "  Time segment: 1081s to 1681s\n", "  Description: 30% through dataset\n", "  Sample rate: 30 Hz\n", "  Duration: 600 seconds\n", "\n", "Suggested analysis parameters:\n", "  Context length: 64\n", "  Prediction length: 16\n", "  Stride: 8\n", "  Frequency bands: ['infrasound', 'low_rumble', 'traffic_low', 'traffic_high']\n"]}], "source": ["# Choose analysis parameters\n", "sample_rate = 30  # Hz - good compromise for DAS analysis\n", "analysis_duration = 600  # 10 minutes\n", "\n", "# Choose time segment\n", "total_duration = len(data) / 250\n", "print(f\"Total data duration: {total_duration/3600:.1f} hours\")\n", "\n", "if data_type == \"REAL\":\n", "    # For real data, try different interesting time periods\n", "    analysis_options = [\n", "        {\"start\": 3600, \"description\": \"1 hour mark (potential traffic changes)\"},\n", "        {\"start\": 7200, \"description\": \"2 hour mark (potential activity changes)\"},  \n", "        {\"start\": 14400, \"description\": \"4 hour mark (potential environmental changes)\"},\n", "        {\"start\": int(total_duration * 0.3), \"description\": \"30% through dataset\"},\n", "        {\"start\": int(total_duration * 0.7), \"description\": \"70% through dataset\"}\n", "    ]\n", "    \n", "    # Choose first valid option\n", "    analysis_start = None\n", "    for option in analysis_options:\n", "        if option[\"start\"] + analysis_duration < total_duration:\n", "            analysis_start = option[\"start\"]\n", "            description = option[\"description\"]\n", "            break\n", "    \n", "    if analysis_start is None:\n", "        analysis_start = int(total_duration * 0.5)\n", "        description = \"Middle of dataset (fallback)\"\n", "else:\n", "    # For synthetic data, choose segment with known anomaly\n", "    analysis_start = 3500  # Just before 3600s anomaly\n", "    description = \"Around 3600s synthetic anomaly\"\n", "\n", "print(f\"\\nAnalysis configuration:\")\n", "print(f\"  Data type: {data_type}\")\n", "print(f\"  Time segment: {analysis_start}s to {analysis_start + analysis_duration}s\")\n", "print(f\"  Description: {description}\")\n", "print(f\"  Sample rate: {sample_rate} Hz\")\n", "print(f\"  Duration: {analysis_duration} seconds\")\n", "\n", "# Get suggested parameters\n", "suggested_params = suggest_analysis_parameters(sample_rate, analysis_duration, 'land')\n", "print(f\"\\nSuggested analysis parameters:\")\n", "print(f\"  Context length: {suggested_params['context_length']}\")\n", "print(f\"  Prediction length: {suggested_params['prediction_length']}\")\n", "print(f\"  Stride: {suggested_params['stride']}\")\n", "print(f\"  Frequency bands: {list(suggested_params['freq_bands'].keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Run Spectral Anomaly Analysis"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "RUNNING SPECTRAL ANOMALY ANALYSIS\n", "============================================================\n", "\n", "=== Spectral Anomaly Analysis ===\n", "Time window: 1081s + 600s\n", "Sample rate: 250 Hz → 30 Hz\n", "Downsampled: 150000 → 18000 samples\n", "Whitening spectrogram (subtracting frequency-wise mean)...\n", "  Original power range: [-58.9, 26.4] dB\n", "  Whitened power range: [-53.5, 15.4] dB\n", "Spectrogram: 129 frequencies × 139 time bins\n", "\n", "Analyzing infrasound band (0.01-0.1 Hz)...\n", "  Warning: No frequencies found in band\n", "\n", "Analyzing low_rumble band (0.1-1.0 Hz)...\n", "  MSE errors: min=0.591455, max=2.320135, mean=1.412223\n", "  Threshold (85th percentile): 1.760729\n", "  Anomalies: 2/8 (25.0%)\n", "  2 anomalies (25.0%)\n", "  Worst prediction errors:\n", "    t=1324.2s: MSE=2.320135 (🚨 ANOMALY)\n", "    t=1119.4s: MSE=1.767735 (🚨 ANOMALY)\n", "    t=1255.9s: MSE=1.627625 (normal)\n", "\n", "Analyzing traffic_low band (1.0-5.0 Hz)...\n", "  MSE errors: min=0.507058, max=1.953141, mean=1.107118\n", "  Threshold (85th percentile): 1.621774\n", "  Anomalies: 2/8 (25.0%)\n", "  2 anomalies (25.0%)\n", "  Worst prediction errors:\n", "    t=1119.4s: MSE=1.953141 (🚨 ANOMALY)\n", "    t=1221.8s: MSE=1.643952 (🚨 ANOMALY)\n", "    t=1153.5s: MSE=1.200400 (normal)\n", "\n", "Analyzing traffic_high band (5.0-9.0 Hz)...\n", "  MSE errors: min=0.494289, max=1.838382, mean=0.995756\n", "  Threshold (85th percentile): 1.526844\n", "  Anomalies: 2/8 (25.0%)\n", "  2 anomalies (25.0%)\n", "  Worst prediction errors:\n", "    t=1255.9s: MSE=1.838382 (🚨 ANOMALY)\n", "    t=1324.2s: MSE=1.540000 (🚨 ANOMALY)\n", "    t=1119.4s: MSE=1.276880 (normal)\n", "\n", "✓ Spectral analysis complete\n", "\n", "🎯 ANALYSIS COMPLETE\n", "Total anomalies detected: 6\n", "Frequency bands analyzed: 3\n", "\n", "Anomalies by frequency band:\n", "  low_rumble: 2 anomalies (0.1-1.0 Hz)\n", "  traffic_low: 2 anomalies (1.0-5.0 Hz)\n", "  traffic_high: 2 anomalies (5.0-9.0 Hz)\n"]}], "source": ["# Run the analysis\n", "print(f\"\\n{'='*60}\")\n", "print(f\"RUNNING SPECTRAL ANOMALY ANALYSIS\")\n", "print(f\"{'='*60}\")\n", "\n", "results = analyze_spectral_anomalies(\n", "    data,\n", "    start_time_seconds=analysis_start,\n", "    duration_seconds=analysis_duration,\n", "    target_sample_rate=sample_rate,\n", "    freq_bands=suggested_params['freq_bands'],\n", "    context_length=suggested_params['context_length'],\n", "    prediction_length=suggested_params['prediction_length'],\n", "    stride=suggested_params['stride']\n", ")\n", "\n", "# Summary\n", "total_anomalies = sum([band['n_anomalies'] for band in results['band_anomalies'].values()])\n", "print(f\"\\n🎯 ANALYSIS COMPLETE\")\n", "print(f\"Total anomalies detected: {total_anomalies}\")\n", "print(f\"Frequency bands analyzed: {len(results['band_anomalies'])}\")\n", "\n", "if total_anomalies > 0:\n", "    print(f\"\\nAnomalies by frequency band:\")\n", "    for band_name, band_result in results['band_anomalies'].items():\n", "        n_anom = band_result['n_anomalies']\n", "        if n_anom > 0:\n", "            freq_range = band_result['freq_range']\n", "            print(f\"  {band_name}: {n_anom} anomalies ({freq_range[0]:.1f}-{freq_range[1]:.1f} Hz)\")\n", "else:\n", "    print(f\"\\n⚠️  No anomalies detected. This could mean:\")\n", "    print(f\"   - The data is very regular (normal for DAS)\")\n", "    print(f\"   - The detection threshold is too high\")\n", "    print(f\"   - The prediction model is too adaptive\")\n", "    print(f\"   - Try a different time segment or parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Enhanced Static Visualization"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating enhanced visualization with log frequency scale...\n"]}, {"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "/* global mpl */\n", "window.mpl = {};\n", "\n", "mpl.get_websocket_type = function () {\n", "    if (typeof WebSocket !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof MozWebSocket !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert(\n", "            'Your browser does not have WebSocket support. ' +\n", "                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "                'Firefox 4 and 5 are also supported but you ' +\n", "                'have to enable WebSockets in about:config.'\n", "        );\n", "    }\n", "};\n", "\n", "mpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = this.ws.binaryType !== undefined;\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById('mpl-warnings');\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent =\n", "                'This browser does not support binary websocket messages. ' +\n", "                'Performance may be slow.';\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = document.createElement('div');\n", "    this.root.setAttribute('style', 'display: inline-block');\n", "    this._root_extra_style(this.root);\n", "\n", "    parent_element.appendChild(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen = function () {\n", "        fig.send_message('supports_binary', { value: fig.supports_binary });\n", "        fig.send_message('send_image_mode', {});\n", "        if (fig.ratio !== 1) {\n", "            fig.send_message('set_device_pixel_ratio', {\n", "                device_pixel_ratio: fig.ratio,\n", "            });\n", "        }\n", "        fig.send_message('refresh', {});\n", "    };\n", "\n", "    this.imageObj.onload = function () {\n", "        if (fig.image_mode === 'full') {\n", "            // Full images could contain transparency (where diff images\n", "            // almost always do), so we need to clear the canvas so that\n", "            // there is no ghosting.\n", "            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "        }\n", "        fig.context.drawImage(fig.imageObj, 0, 0);\n", "    };\n", "\n", "    this.imageObj.onunload = function () {\n", "        fig.ws.close();\n", "    };\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "};\n", "\n", "mpl.figure.prototype._init_header = function () {\n", "    var titlebar = document.createElement('div');\n", "    titlebar.classList =\n", "        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n", "    var titletext = document.createElement('div');\n", "    titletext.classList = 'ui-dialog-title';\n", "    titletext.setAttribute(\n", "        'style',\n", "        'width: 100%; text-align: center; padding: 3px;'\n", "    );\n", "    titlebar.appendChild(titletext);\n", "    this.root.appendChild(titlebar);\n", "    this.header = titletext;\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._init_canvas = function () {\n", "    var fig = this;\n", "\n", "    var canvas_div = (this.canvas_div = document.createElement('div'));\n", "    canvas_div.setAttribute('tabindex', '0');\n", "    canvas_div.setAttribute(\n", "        'style',\n", "        'border: 1px solid #ddd;' +\n", "            'box-sizing: content-box;' +\n", "            'clear: both;' +\n", "            'min-height: 1px;' +\n", "            'min-width: 1px;' +\n", "            'outline: 0;' +\n", "            'overflow: hidden;' +\n", "            'position: relative;' +\n", "            'resize: both;' +\n", "            'z-index: 2;'\n", "    );\n", "\n", "    function on_keyboard_event_closure(name) {\n", "        return function (event) {\n", "            return fig.key_event(event, name);\n", "        };\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'keydown',\n", "        on_keyboard_event_closure('key_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'keyup',\n", "        on_keyboard_event_closure('key_release')\n", "    );\n", "\n", "    this._canvas_extra_style(canvas_div);\n", "    this.root.appendChild(canvas_div);\n", "\n", "    var canvas = (this.canvas = document.createElement('canvas'));\n", "    canvas.classList.add('mpl-canvas');\n", "    canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'pointer-events: none;' +\n", "            'position: relative;' +\n", "            'z-index: 0;'\n", "    );\n", "\n", "    this.context = canvas.getContext('2d');\n", "\n", "    var backingStore =\n", "        this.context.backingStorePixelRatio ||\n", "        this.context.webkitBackingStorePixelRatio ||\n", "        this.context.mozBackingStorePixelRatio ||\n", "        this.context.msBackingStorePixelRatio ||\n", "        this.context.oBackingStorePixelRatio ||\n", "        this.context.backingStorePixelRatio ||\n", "        1;\n", "\n", "    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n", "        'canvas'\n", "    ));\n", "    rubberband_canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'left: 0;' +\n", "            'pointer-events: none;' +\n", "            'position: absolute;' +\n", "            'top: 0;' +\n", "            'z-index: 1;'\n", "    );\n", "\n", "    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n", "    if (this.ResizeObserver === undefined) {\n", "        if (window.ResizeObserver !== undefined) {\n", "            this.ResizeObserver = window.ResizeObserver;\n", "        } else {\n", "            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n", "            this.ResizeObserver = obs.ResizeObserver;\n", "        }\n", "    }\n", "\n", "    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n", "        // There's no need to resize if the WebSocket is not connected:\n", "        // - If it is still connecting, then we will get an initial resize from\n", "        //   Python once it connects.\n", "        // - If it has disconnected, then resizing will clear the canvas and\n", "        //   never get anything back to refill it, so better to not resize and\n", "        //   keep something visible.\n", "        if (fig.ws.readyState != 1) {\n", "            return;\n", "        }\n", "        var nentries = entries.length;\n", "        for (var i = 0; i < nentries; i++) {\n", "            var entry = entries[i];\n", "            var width, height;\n", "            if (entry.contentBoxSize) {\n", "                if (entry.contentBoxSize instanceof Array) {\n", "                    // Chrome 84 implements new version of spec.\n", "                    width = entry.contentBoxSize[0].inlineSize;\n", "                    height = entry.contentBoxSize[0].blockSize;\n", "                } else {\n", "                    // Firefox implements old version of spec.\n", "                    width = entry.contentBoxSize.inlineSize;\n", "                    height = entry.contentBoxSize.blockSize;\n", "                }\n", "            } else {\n", "                // Chrome <84 implements even older version of spec.\n", "                width = entry.contentRect.width;\n", "                height = entry.contentRect.height;\n", "            }\n", "\n", "            // Keep the size of the canvas and rubber band canvas in sync with\n", "            // the canvas container.\n", "            if (entry.devicePixelContentBoxSize) {\n", "                // Chrome 84 implements new version of spec.\n", "                canvas.setAttribute(\n", "                    'width',\n", "                    entry.devicePixelContentBoxSize[0].inlineSize\n", "                );\n", "                canvas.setAttribute(\n", "                    'height',\n", "                    entry.devicePixelContentBoxSize[0].blockSize\n", "                );\n", "            } else {\n", "                canvas.setAttribute('width', width * fig.ratio);\n", "                canvas.setAttribute('height', height * fig.ratio);\n", "            }\n", "            /* This rescales the canvas back to display pixels, so that it\n", "             * appears correct on HiDPI screens. */\n", "            canvas.style.width = width + 'px';\n", "            canvas.style.height = height + 'px';\n", "\n", "            rubberband_canvas.setAttribute('width', width);\n", "            rubberband_canvas.setAttribute('height', height);\n", "\n", "            // And update the size in Python. We ignore the initial 0/0 size\n", "            // that occurs as the element is placed into the DOM, which should\n", "            // otherwise not happen due to the minimum size styling.\n", "            if (width != 0 && height != 0) {\n", "                fig.request_resize(width, height);\n", "            }\n", "        }\n", "    });\n", "    this.resizeObserverInstance.observe(canvas_div);\n", "\n", "    function on_mouse_event_closure(name) {\n", "        /* User Agent sniffing is bad, but WebKit is busted:\n", "         * https://bugs.webkit.org/show_bug.cgi?id=144526\n", "         * https://bugs.webkit.org/show_bug.cgi?id=181818\n", "         * The worst that happens here is that they get an extra browser\n", "         * selection when dragging, if this check fails to catch them.\n", "         */\n", "        var UA = navigator.userAgent;\n", "        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n", "        if(isWebKit) {\n", "            return function (event) {\n", "                /* This prevents the web browser from automatically changing to\n", "                 * the text insertion cursor when the button is pressed. We\n", "                 * want to control all of the cursor setting manually through\n", "                 * the 'cursor' event from matplotlib */\n", "                event.preventDefault()\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        } else {\n", "            return function (event) {\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        }\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'mousedown',\n", "        on_mouse_event_closure('button_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseup',\n", "        on_mouse_event_closure('button_release')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'dblclick',\n", "        on_mouse_event_closure('dblclick')\n", "    );\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    canvas_div.addEventListener(\n", "        'mousemove',\n", "        on_mouse_event_closure('motion_notify')\n", "    );\n", "\n", "    canvas_div.addEventListener(\n", "        'mouseenter',\n", "        on_mouse_event_closure('figure_enter')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseleave',\n", "        on_mouse_event_closure('figure_leave')\n", "    );\n", "\n", "    canvas_div.addEventListener('wheel', function (event) {\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        on_mouse_event_closure('scroll')(event);\n", "    });\n", "\n", "    canvas_div.appendChild(canvas);\n", "    canvas_div.appendChild(rubberband_canvas);\n", "\n", "    this.rubberband_context = rubberband_canvas.getContext('2d');\n", "    this.rubberband_context.strokeStyle = '#000000';\n", "\n", "    this._resize_canvas = function (width, height, forward) {\n", "        if (forward) {\n", "            canvas_div.style.width = width + 'px';\n", "            canvas_div.style.height = height + 'px';\n", "        }\n", "    };\n", "\n", "    // Disable right mouse context menu.\n", "    canvas_div.addEventListener('contextmenu', function (_e) {\n", "        event.preventDefault();\n", "        return false;\n", "    });\n", "\n", "    function set_focus() {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'mpl-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'mpl-button-group';\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'mpl-button-group';\n", "            continue;\n", "        }\n", "\n", "        var button = (fig.buttons[name] = document.createElement('button'));\n", "        button.classList = 'mpl-widget';\n", "        button.setAttribute('role', 'button');\n", "        button.setAttribute('aria-disabled', 'false');\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "\n", "        var icon_img = document.createElement('img');\n", "        icon_img.src = '_images/' + image + '.png';\n", "        icon_img.srcset = '_images/' + image + '_large.png 2x';\n", "        icon_img.alt = tooltip;\n", "        button.appendChild(icon_img);\n", "\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    var fmt_picker = document.createElement('select');\n", "    fmt_picker.classList = 'mpl-widget';\n", "    toolbar.appendChild(fmt_picker);\n", "    this.format_dropdown = fmt_picker;\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = document.createElement('option');\n", "        option.selected = fmt === mpl.default_extension;\n", "        option.innerHTML = fmt;\n", "        fmt_picker.appendChild(option);\n", "    }\n", "\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "};\n", "\n", "mpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', { width: x_pixels, height: y_pixels });\n", "};\n", "\n", "mpl.figure.prototype.send_message = function (type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "};\n", "\n", "mpl.figure.prototype.send_draw_message = function () {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "};\n", "\n", "mpl.figure.prototype.handle_resize = function (fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1], msg['forward']);\n", "        fig.send_message('refresh', {});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_rubberband = function (fig, msg) {\n", "    var x0 = msg['x0'] / fig.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n", "    var x1 = msg['x1'] / fig.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0,\n", "        0,\n", "        fig.canvas.width / fig.ratio,\n", "        fig.canvas.height / fig.ratio\n", "    );\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "};\n", "\n", "mpl.figure.prototype.handle_figure_label = function (fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "};\n", "\n", "mpl.figure.prototype.handle_cursor = function (fig, msg) {\n", "    fig.canvas_div.style.cursor = msg['cursor'];\n", "};\n", "\n", "mpl.figure.prototype.handle_message = function (fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "};\n", "\n", "mpl.figure.prototype.handle_draw = function (fig, _msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "};\n", "\n", "mpl.figure.prototype.handle_image_mode = function (fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "};\n", "\n", "mpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n", "    for (var key in msg) {\n", "        if (!(key in fig.buttons)) {\n", "            continue;\n", "        }\n", "        fig.buttons[key].disabled = !msg[key];\n", "        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n", "    if (msg['mode'] === 'PAN') {\n", "        fig.buttons['Pan'].classList.add('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    } else if (msg['mode'] === 'ZOOM') {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.add('active');\n", "    } else {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message('ack', {});\n", "};\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function (fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            var img = evt.data;\n", "            if (img.type !== 'image/png') {\n", "                /* FIXME: We get \"Resource interpreted as Image but\n", "                 * transferred with MIME type text/plain:\" errors on\n", "                 * Chrome.  But how to set the MIME type?  It doesn't seem\n", "                 * to be part of the websocket stream */\n", "                img.type = 'image/png';\n", "            }\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src\n", "                );\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                img\n", "            );\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        } else if (\n", "            typeof evt.data === 'string' &&\n", "            evt.data.slice(0, 21) === 'data:image/png;base64'\n", "        ) {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig['handle_' + msg_type];\n", "        } catch (e) {\n", "            console.log(\n", "                \"No handler for the '%s' message type: \",\n", "                msg_type,\n", "                msg\n", "            );\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '%s' message: \", msg_type, msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\n", "                    \"Exception inside the 'handler_%s' callback:\",\n", "                    msg_type,\n", "                    e,\n", "                    e.stack,\n", "                    msg\n", "                );\n", "            }\n", "        }\n", "    };\n", "};\n", "\n", "function getModifiers(event) {\n", "    var mods = [];\n", "    if (event.ctrlKey) {\n", "        mods.push('ctrl');\n", "    }\n", "    if (event.altKey) {\n", "        mods.push('alt');\n", "    }\n", "    if (event.shiftKey) {\n", "        mods.push('shift');\n", "    }\n", "    if (event.metaKey) {\n", "        mods.push('meta');\n", "    }\n", "    return mods;\n", "}\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * https://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s(original) {\n", "    return Object.keys(original).reduce(function (obj, key) {\n", "        if (typeof original[key] !== 'object') {\n", "            obj[key] = original[key];\n", "        }\n", "        return obj;\n", "    }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function (event, name) {\n", "    if (name === 'button_press') {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    // from https://stackoverflow.com/q/1114465\n", "    var boundingRect = this.canvas.getBoundingClientRect();\n", "    var x = (event.clientX - boundingRect.left) * this.ratio;\n", "    var y = (event.clientY - boundingRect.top) * this.ratio;\n", "\n", "    this.send_message(name, {\n", "        x: x,\n", "        y: y,\n", "        button: event.button,\n", "        step: event.step,\n", "        buttons: event.buttons,\n", "        modifiers: getModifiers(event),\n", "        guiEvent: simple<PERSON>eys(event),\n", "    });\n", "\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (_event, _name) {\n", "    // Handle any extra behaviour associated with a key event\n", "};\n", "\n", "mpl.figure.prototype.key_event = function (event, name) {\n", "    // Prevent repeat events\n", "    if (name === 'key_press') {\n", "        if (event.key === this._key) {\n", "            return;\n", "        } else {\n", "            this._key = event.key;\n", "        }\n", "    }\n", "    if (name === 'key_release') {\n", "        this._key = null;\n", "    }\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.key !== 'Control') {\n", "        value += 'ctrl+';\n", "    }\n", "    else if (event.altKey && event.key !== 'Alt') {\n", "        value += 'alt+';\n", "    }\n", "    else if (event.shiftKey && event.key !== 'Shift') {\n", "        value += 'shift+';\n", "    }\n", "\n", "    value += 'k' + event.key;\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function (name) {\n", "    if (name === 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message('toolbar_button', { name: name });\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "\n", "///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n", "// prettier-ignore\n", "var _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n", "\n", "mpl.default_extension = \"png\";/* global mpl */\n", "\n", "var comm_websocket_adapter = function (comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.binaryType = comm.kernel.ws.binaryType;\n", "    ws.readyState = comm.kernel.ws.readyState;\n", "    function updateReadyState(_event) {\n", "        if (comm.kernel.ws) {\n", "            ws.readyState = comm.kernel.ws.readyState;\n", "        } else {\n", "            ws.readyState = 3; // Closed state.\n", "        }\n", "    }\n", "    comm.kernel.ws.addEventListener('open', updateReadyState);\n", "    comm.kernel.ws.addEventListener('close', updateReadyState);\n", "    comm.kernel.ws.addEventListener('error', updateReadyState);\n", "\n", "    ws.close = function () {\n", "        comm.close();\n", "    };\n", "    ws.send = function (m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function (msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        var data = msg['content']['data'];\n", "        if (data['blob'] !== undefined) {\n", "            data = {\n", "                data: new Blob(msg['buffers'], { type: data['blob'] }),\n", "            };\n", "        }\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(data);\n", "    });\n", "    return ws;\n", "};\n", "\n", "mpl.mpl_figure_comm = function (comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = document.getElementById(id);\n", "    var ws_proxy = comm_websocket_adapter(comm);\n", "\n", "    function ondownload(figure, _format) {\n", "        window.open(figure.canvas.toDataURL());\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element;\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error('Failed to find cell for figure', id, fig);\n", "        return;\n", "    }\n", "    fig.cell_info[0].output_area.element.on(\n", "        'cleared',\n", "        { fig: fig },\n", "        fig._remove_fig_handler\n", "    );\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function (fig, msg) {\n", "    var width = fig.canvas.width / fig.ratio;\n", "    fig.cell_info[0].output_area.element.off(\n", "        'cleared',\n", "        fig._remove_fig_handler\n", "    );\n", "    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable();\n", "    fig.parent_element.innerHTML =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "    fig.close_ws(fig, msg);\n", "};\n", "\n", "mpl.figure.prototype.close_ws = function (fig, msg) {\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "};\n", "\n", "mpl.figure.prototype.push_to_output = function (_remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width / this.ratio;\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message('ack', {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () {\n", "        fig.push_to_output();\n", "    }, 1000);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'btn-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'btn-group';\n", "    var button;\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'btn-group';\n", "            continue;\n", "        }\n", "\n", "        button = fig.buttons[name] = document.createElement('button');\n", "        button.classList = 'btn btn-default';\n", "        button.href = '#';\n", "        button.title = name;\n", "        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message pull-right';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = document.createElement('div');\n", "    buttongrp.classList = 'btn-group inline pull-right';\n", "    button = document.createElement('button');\n", "    button.classList = 'btn btn-mini btn-primary';\n", "    button.href = '#';\n", "    button.title = 'Stop Interaction';\n", "    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n", "    button.addEventListener('click', function (_evt) {\n", "        fig.handle_close(fig, {});\n", "    });\n", "    button.addEventListener(\n", "        'mouseover',\n", "        on_mouseover_closure('Stop Interaction')\n", "    );\n", "    buttongrp.appendChild(button);\n", "    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n", "    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n", "};\n", "\n", "mpl.figure.prototype._remove_fig_handler = function (event) {\n", "    var fig = event.data.fig;\n", "    if (event.target !== this) {\n", "        // Ignore bubbled events from children.\n", "        return;\n", "    }\n", "    fig.close_ws(fig, {});\n", "};\n", "\n", "mpl.figure.prototype._root_extra_style = function (el) {\n", "    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (el) {\n", "    // this is important to make the div 'focusable\n", "    el.setAttribute('tabindex', 0);\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    } else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (event, _name) {\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which === 13) {\n", "        this.canvas_div.blur();\n", "        // select the cell after this one\n", "        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n", "        IPython.notebook.select(index + 1);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    fig.ondownload(fig, null);\n", "};\n", "\n", "mpl.find_output_cell = function (html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i = 0; i < ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code') {\n", "            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] === html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "};\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel !== null) {\n", "    IPython.notebook.kernel.comm_manager.register_target(\n", "        'matplotlib',\n", "        mpl.mpl_figure_comm\n", "    );\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div id='36b71271-cc17-4e4c-ac06-8b32f47a6944'></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Estimated anomaly window duration: 341.33 seconds\n", "Using log frequency scale: 0.010 - 15.0 Hz\n", "\n", "Visualization details:\n", "  Window duration: 341.33 seconds\n", "  Time range: 1085.3 - 1674.1 seconds\n", "  Total anomalies: 6\n", "\n", "Detailed anomaly breakdown:\n", "  low_rumble (0.1-1.0 Hz): 2 anomalies\n", "    Times: ['1119.4s', '1324.2s']\n", "  traffic_low (1.0-5.0 Hz): 2 anomalies\n", "    Times: ['1119.4s', '1221.8s']\n", "  traffic_high (5.0-9.0 Hz): 2 anomalies\n", "    Times: ['1255.9s', '1324.2s']\n"]}], "source": ["# Create enhanced static visualization with log frequency scale\n", "print(\"Creating enhanced visualization with log frequency scale...\")\n", "fig, axes = visualize_spectral_anomalies_enhanced(\n", "    results, \n", "    f\"{data_type}_Data_Analysis\",\n", "    log_freq=True\n", ")\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Frequency Scale Comparison"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating frequency scale comparison...\n"]}, {"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "/* global mpl */\n", "window.mpl = {};\n", "\n", "mpl.get_websocket_type = function () {\n", "    if (typeof WebSocket !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof MozWebSocket !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert(\n", "            'Your browser does not have WebSocket support. ' +\n", "                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "                'Firefox 4 and 5 are also supported but you ' +\n", "                'have to enable WebSockets in about:config.'\n", "        );\n", "    }\n", "};\n", "\n", "mpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = this.ws.binaryType !== undefined;\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById('mpl-warnings');\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent =\n", "                'This browser does not support binary websocket messages. ' +\n", "                'Performance may be slow.';\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = document.createElement('div');\n", "    this.root.setAttribute('style', 'display: inline-block');\n", "    this._root_extra_style(this.root);\n", "\n", "    parent_element.appendChild(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen = function () {\n", "        fig.send_message('supports_binary', { value: fig.supports_binary });\n", "        fig.send_message('send_image_mode', {});\n", "        if (fig.ratio !== 1) {\n", "            fig.send_message('set_device_pixel_ratio', {\n", "                device_pixel_ratio: fig.ratio,\n", "            });\n", "        }\n", "        fig.send_message('refresh', {});\n", "    };\n", "\n", "    this.imageObj.onload = function () {\n", "        if (fig.image_mode === 'full') {\n", "            // Full images could contain transparency (where diff images\n", "            // almost always do), so we need to clear the canvas so that\n", "            // there is no ghosting.\n", "            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "        }\n", "        fig.context.drawImage(fig.imageObj, 0, 0);\n", "    };\n", "\n", "    this.imageObj.onunload = function () {\n", "        fig.ws.close();\n", "    };\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "};\n", "\n", "mpl.figure.prototype._init_header = function () {\n", "    var titlebar = document.createElement('div');\n", "    titlebar.classList =\n", "        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n", "    var titletext = document.createElement('div');\n", "    titletext.classList = 'ui-dialog-title';\n", "    titletext.setAttribute(\n", "        'style',\n", "        'width: 100%; text-align: center; padding: 3px;'\n", "    );\n", "    titlebar.appendChild(titletext);\n", "    this.root.appendChild(titlebar);\n", "    this.header = titletext;\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._init_canvas = function () {\n", "    var fig = this;\n", "\n", "    var canvas_div = (this.canvas_div = document.createElement('div'));\n", "    canvas_div.setAttribute('tabindex', '0');\n", "    canvas_div.setAttribute(\n", "        'style',\n", "        'border: 1px solid #ddd;' +\n", "            'box-sizing: content-box;' +\n", "            'clear: both;' +\n", "            'min-height: 1px;' +\n", "            'min-width: 1px;' +\n", "            'outline: 0;' +\n", "            'overflow: hidden;' +\n", "            'position: relative;' +\n", "            'resize: both;' +\n", "            'z-index: 2;'\n", "    );\n", "\n", "    function on_keyboard_event_closure(name) {\n", "        return function (event) {\n", "            return fig.key_event(event, name);\n", "        };\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'keydown',\n", "        on_keyboard_event_closure('key_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'keyup',\n", "        on_keyboard_event_closure('key_release')\n", "    );\n", "\n", "    this._canvas_extra_style(canvas_div);\n", "    this.root.appendChild(canvas_div);\n", "\n", "    var canvas = (this.canvas = document.createElement('canvas'));\n", "    canvas.classList.add('mpl-canvas');\n", "    canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'pointer-events: none;' +\n", "            'position: relative;' +\n", "            'z-index: 0;'\n", "    );\n", "\n", "    this.context = canvas.getContext('2d');\n", "\n", "    var backingStore =\n", "        this.context.backingStorePixelRatio ||\n", "        this.context.webkitBackingStorePixelRatio ||\n", "        this.context.mozBackingStorePixelRatio ||\n", "        this.context.msBackingStorePixelRatio ||\n", "        this.context.oBackingStorePixelRatio ||\n", "        this.context.backingStorePixelRatio ||\n", "        1;\n", "\n", "    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n", "        'canvas'\n", "    ));\n", "    rubberband_canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'left: 0;' +\n", "            'pointer-events: none;' +\n", "            'position: absolute;' +\n", "            'top: 0;' +\n", "            'z-index: 1;'\n", "    );\n", "\n", "    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n", "    if (this.ResizeObserver === undefined) {\n", "        if (window.ResizeObserver !== undefined) {\n", "            this.ResizeObserver = window.ResizeObserver;\n", "        } else {\n", "            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n", "            this.ResizeObserver = obs.ResizeObserver;\n", "        }\n", "    }\n", "\n", "    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n", "        // There's no need to resize if the WebSocket is not connected:\n", "        // - If it is still connecting, then we will get an initial resize from\n", "        //   Python once it connects.\n", "        // - If it has disconnected, then resizing will clear the canvas and\n", "        //   never get anything back to refill it, so better to not resize and\n", "        //   keep something visible.\n", "        if (fig.ws.readyState != 1) {\n", "            return;\n", "        }\n", "        var nentries = entries.length;\n", "        for (var i = 0; i < nentries; i++) {\n", "            var entry = entries[i];\n", "            var width, height;\n", "            if (entry.contentBoxSize) {\n", "                if (entry.contentBoxSize instanceof Array) {\n", "                    // Chrome 84 implements new version of spec.\n", "                    width = entry.contentBoxSize[0].inlineSize;\n", "                    height = entry.contentBoxSize[0].blockSize;\n", "                } else {\n", "                    // Firefox implements old version of spec.\n", "                    width = entry.contentBoxSize.inlineSize;\n", "                    height = entry.contentBoxSize.blockSize;\n", "                }\n", "            } else {\n", "                // Chrome <84 implements even older version of spec.\n", "                width = entry.contentRect.width;\n", "                height = entry.contentRect.height;\n", "            }\n", "\n", "            // Keep the size of the canvas and rubber band canvas in sync with\n", "            // the canvas container.\n", "            if (entry.devicePixelContentBoxSize) {\n", "                // Chrome 84 implements new version of spec.\n", "                canvas.setAttribute(\n", "                    'width',\n", "                    entry.devicePixelContentBoxSize[0].inlineSize\n", "                );\n", "                canvas.setAttribute(\n", "                    'height',\n", "                    entry.devicePixelContentBoxSize[0].blockSize\n", "                );\n", "            } else {\n", "                canvas.setAttribute('width', width * fig.ratio);\n", "                canvas.setAttribute('height', height * fig.ratio);\n", "            }\n", "            /* This rescales the canvas back to display pixels, so that it\n", "             * appears correct on HiDPI screens. */\n", "            canvas.style.width = width + 'px';\n", "            canvas.style.height = height + 'px';\n", "\n", "            rubberband_canvas.setAttribute('width', width);\n", "            rubberband_canvas.setAttribute('height', height);\n", "\n", "            // And update the size in Python. We ignore the initial 0/0 size\n", "            // that occurs as the element is placed into the DOM, which should\n", "            // otherwise not happen due to the minimum size styling.\n", "            if (width != 0 && height != 0) {\n", "                fig.request_resize(width, height);\n", "            }\n", "        }\n", "    });\n", "    this.resizeObserverInstance.observe(canvas_div);\n", "\n", "    function on_mouse_event_closure(name) {\n", "        /* User Agent sniffing is bad, but WebKit is busted:\n", "         * https://bugs.webkit.org/show_bug.cgi?id=144526\n", "         * https://bugs.webkit.org/show_bug.cgi?id=181818\n", "         * The worst that happens here is that they get an extra browser\n", "         * selection when dragging, if this check fails to catch them.\n", "         */\n", "        var UA = navigator.userAgent;\n", "        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n", "        if(isWebKit) {\n", "            return function (event) {\n", "                /* This prevents the web browser from automatically changing to\n", "                 * the text insertion cursor when the button is pressed. We\n", "                 * want to control all of the cursor setting manually through\n", "                 * the 'cursor' event from matplotlib */\n", "                event.preventDefault()\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        } else {\n", "            return function (event) {\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        }\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'mousedown',\n", "        on_mouse_event_closure('button_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseup',\n", "        on_mouse_event_closure('button_release')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'dblclick',\n", "        on_mouse_event_closure('dblclick')\n", "    );\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    canvas_div.addEventListener(\n", "        'mousemove',\n", "        on_mouse_event_closure('motion_notify')\n", "    );\n", "\n", "    canvas_div.addEventListener(\n", "        'mouseenter',\n", "        on_mouse_event_closure('figure_enter')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseleave',\n", "        on_mouse_event_closure('figure_leave')\n", "    );\n", "\n", "    canvas_div.addEventListener('wheel', function (event) {\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        on_mouse_event_closure('scroll')(event);\n", "    });\n", "\n", "    canvas_div.appendChild(canvas);\n", "    canvas_div.appendChild(rubberband_canvas);\n", "\n", "    this.rubberband_context = rubberband_canvas.getContext('2d');\n", "    this.rubberband_context.strokeStyle = '#000000';\n", "\n", "    this._resize_canvas = function (width, height, forward) {\n", "        if (forward) {\n", "            canvas_div.style.width = width + 'px';\n", "            canvas_div.style.height = height + 'px';\n", "        }\n", "    };\n", "\n", "    // Disable right mouse context menu.\n", "    canvas_div.addEventListener('contextmenu', function (_e) {\n", "        event.preventDefault();\n", "        return false;\n", "    });\n", "\n", "    function set_focus() {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'mpl-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'mpl-button-group';\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'mpl-button-group';\n", "            continue;\n", "        }\n", "\n", "        var button = (fig.buttons[name] = document.createElement('button'));\n", "        button.classList = 'mpl-widget';\n", "        button.setAttribute('role', 'button');\n", "        button.setAttribute('aria-disabled', 'false');\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "\n", "        var icon_img = document.createElement('img');\n", "        icon_img.src = '_images/' + image + '.png';\n", "        icon_img.srcset = '_images/' + image + '_large.png 2x';\n", "        icon_img.alt = tooltip;\n", "        button.appendChild(icon_img);\n", "\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    var fmt_picker = document.createElement('select');\n", "    fmt_picker.classList = 'mpl-widget';\n", "    toolbar.appendChild(fmt_picker);\n", "    this.format_dropdown = fmt_picker;\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = document.createElement('option');\n", "        option.selected = fmt === mpl.default_extension;\n", "        option.innerHTML = fmt;\n", "        fmt_picker.appendChild(option);\n", "    }\n", "\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "};\n", "\n", "mpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', { width: x_pixels, height: y_pixels });\n", "};\n", "\n", "mpl.figure.prototype.send_message = function (type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "};\n", "\n", "mpl.figure.prototype.send_draw_message = function () {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "};\n", "\n", "mpl.figure.prototype.handle_resize = function (fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1], msg['forward']);\n", "        fig.send_message('refresh', {});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_rubberband = function (fig, msg) {\n", "    var x0 = msg['x0'] / fig.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n", "    var x1 = msg['x1'] / fig.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0,\n", "        0,\n", "        fig.canvas.width / fig.ratio,\n", "        fig.canvas.height / fig.ratio\n", "    );\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "};\n", "\n", "mpl.figure.prototype.handle_figure_label = function (fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "};\n", "\n", "mpl.figure.prototype.handle_cursor = function (fig, msg) {\n", "    fig.canvas_div.style.cursor = msg['cursor'];\n", "};\n", "\n", "mpl.figure.prototype.handle_message = function (fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "};\n", "\n", "mpl.figure.prototype.handle_draw = function (fig, _msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "};\n", "\n", "mpl.figure.prototype.handle_image_mode = function (fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "};\n", "\n", "mpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n", "    for (var key in msg) {\n", "        if (!(key in fig.buttons)) {\n", "            continue;\n", "        }\n", "        fig.buttons[key].disabled = !msg[key];\n", "        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n", "    if (msg['mode'] === 'PAN') {\n", "        fig.buttons['Pan'].classList.add('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    } else if (msg['mode'] === 'ZOOM') {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.add('active');\n", "    } else {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message('ack', {});\n", "};\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function (fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            var img = evt.data;\n", "            if (img.type !== 'image/png') {\n", "                /* FIXME: We get \"Resource interpreted as Image but\n", "                 * transferred with MIME type text/plain:\" errors on\n", "                 * Chrome.  But how to set the MIME type?  It doesn't seem\n", "                 * to be part of the websocket stream */\n", "                img.type = 'image/png';\n", "            }\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src\n", "                );\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                img\n", "            );\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        } else if (\n", "            typeof evt.data === 'string' &&\n", "            evt.data.slice(0, 21) === 'data:image/png;base64'\n", "        ) {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig['handle_' + msg_type];\n", "        } catch (e) {\n", "            console.log(\n", "                \"No handler for the '%s' message type: \",\n", "                msg_type,\n", "                msg\n", "            );\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '%s' message: \", msg_type, msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\n", "                    \"Exception inside the 'handler_%s' callback:\",\n", "                    msg_type,\n", "                    e,\n", "                    e.stack,\n", "                    msg\n", "                );\n", "            }\n", "        }\n", "    };\n", "};\n", "\n", "function getModifiers(event) {\n", "    var mods = [];\n", "    if (event.ctrlKey) {\n", "        mods.push('ctrl');\n", "    }\n", "    if (event.altKey) {\n", "        mods.push('alt');\n", "    }\n", "    if (event.shiftKey) {\n", "        mods.push('shift');\n", "    }\n", "    if (event.metaKey) {\n", "        mods.push('meta');\n", "    }\n", "    return mods;\n", "}\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * https://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s(original) {\n", "    return Object.keys(original).reduce(function (obj, key) {\n", "        if (typeof original[key] !== 'object') {\n", "            obj[key] = original[key];\n", "        }\n", "        return obj;\n", "    }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function (event, name) {\n", "    if (name === 'button_press') {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    // from https://stackoverflow.com/q/1114465\n", "    var boundingRect = this.canvas.getBoundingClientRect();\n", "    var x = (event.clientX - boundingRect.left) * this.ratio;\n", "    var y = (event.clientY - boundingRect.top) * this.ratio;\n", "\n", "    this.send_message(name, {\n", "        x: x,\n", "        y: y,\n", "        button: event.button,\n", "        step: event.step,\n", "        buttons: event.buttons,\n", "        modifiers: getModifiers(event),\n", "        guiEvent: simple<PERSON>eys(event),\n", "    });\n", "\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (_event, _name) {\n", "    // Handle any extra behaviour associated with a key event\n", "};\n", "\n", "mpl.figure.prototype.key_event = function (event, name) {\n", "    // Prevent repeat events\n", "    if (name === 'key_press') {\n", "        if (event.key === this._key) {\n", "            return;\n", "        } else {\n", "            this._key = event.key;\n", "        }\n", "    }\n", "    if (name === 'key_release') {\n", "        this._key = null;\n", "    }\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.key !== 'Control') {\n", "        value += 'ctrl+';\n", "    }\n", "    else if (event.altKey && event.key !== 'Alt') {\n", "        value += 'alt+';\n", "    }\n", "    else if (event.shiftKey && event.key !== 'Shift') {\n", "        value += 'shift+';\n", "    }\n", "\n", "    value += 'k' + event.key;\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function (name) {\n", "    if (name === 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message('toolbar_button', { name: name });\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "\n", "///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n", "// prettier-ignore\n", "var _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n", "\n", "mpl.default_extension = \"png\";/* global mpl */\n", "\n", "var comm_websocket_adapter = function (comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.binaryType = comm.kernel.ws.binaryType;\n", "    ws.readyState = comm.kernel.ws.readyState;\n", "    function updateReadyState(_event) {\n", "        if (comm.kernel.ws) {\n", "            ws.readyState = comm.kernel.ws.readyState;\n", "        } else {\n", "            ws.readyState = 3; // Closed state.\n", "        }\n", "    }\n", "    comm.kernel.ws.addEventListener('open', updateReadyState);\n", "    comm.kernel.ws.addEventListener('close', updateReadyState);\n", "    comm.kernel.ws.addEventListener('error', updateReadyState);\n", "\n", "    ws.close = function () {\n", "        comm.close();\n", "    };\n", "    ws.send = function (m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function (msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        var data = msg['content']['data'];\n", "        if (data['blob'] !== undefined) {\n", "            data = {\n", "                data: new Blob(msg['buffers'], { type: data['blob'] }),\n", "            };\n", "        }\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(data);\n", "    });\n", "    return ws;\n", "};\n", "\n", "mpl.mpl_figure_comm = function (comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = document.getElementById(id);\n", "    var ws_proxy = comm_websocket_adapter(comm);\n", "\n", "    function ondownload(figure, _format) {\n", "        window.open(figure.canvas.toDataURL());\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element;\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error('Failed to find cell for figure', id, fig);\n", "        return;\n", "    }\n", "    fig.cell_info[0].output_area.element.on(\n", "        'cleared',\n", "        { fig: fig },\n", "        fig._remove_fig_handler\n", "    );\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function (fig, msg) {\n", "    var width = fig.canvas.width / fig.ratio;\n", "    fig.cell_info[0].output_area.element.off(\n", "        'cleared',\n", "        fig._remove_fig_handler\n", "    );\n", "    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable();\n", "    fig.parent_element.innerHTML =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "    fig.close_ws(fig, msg);\n", "};\n", "\n", "mpl.figure.prototype.close_ws = function (fig, msg) {\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "};\n", "\n", "mpl.figure.prototype.push_to_output = function (_remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width / this.ratio;\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message('ack', {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () {\n", "        fig.push_to_output();\n", "    }, 1000);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'btn-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'btn-group';\n", "    var button;\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'btn-group';\n", "            continue;\n", "        }\n", "\n", "        button = fig.buttons[name] = document.createElement('button');\n", "        button.classList = 'btn btn-default';\n", "        button.href = '#';\n", "        button.title = name;\n", "        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message pull-right';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = document.createElement('div');\n", "    buttongrp.classList = 'btn-group inline pull-right';\n", "    button = document.createElement('button');\n", "    button.classList = 'btn btn-mini btn-primary';\n", "    button.href = '#';\n", "    button.title = 'Stop Interaction';\n", "    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n", "    button.addEventListener('click', function (_evt) {\n", "        fig.handle_close(fig, {});\n", "    });\n", "    button.addEventListener(\n", "        'mouseover',\n", "        on_mouseover_closure('Stop Interaction')\n", "    );\n", "    buttongrp.appendChild(button);\n", "    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n", "    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n", "};\n", "\n", "mpl.figure.prototype._remove_fig_handler = function (event) {\n", "    var fig = event.data.fig;\n", "    if (event.target !== this) {\n", "        // Ignore bubbled events from children.\n", "        return;\n", "    }\n", "    fig.close_ws(fig, {});\n", "};\n", "\n", "mpl.figure.prototype._root_extra_style = function (el) {\n", "    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (el) {\n", "    // this is important to make the div 'focusable\n", "    el.setAttribute('tabindex', 0);\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    } else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (event, _name) {\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which === 13) {\n", "        this.canvas_div.blur();\n", "        // select the cell after this one\n", "        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n", "        IPython.notebook.select(index + 1);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    fig.ondownload(fig, null);\n", "};\n", "\n", "mpl.find_output_cell = function (html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i = 0; i < ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code') {\n", "            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] === html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "};\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel !== null) {\n", "    IPython.notebook.kernel.comm_manager.register_target(\n", "        'matplotlib',\n", "        mpl.mpl_figure_comm\n", "    );\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div id='4412ff6e-9f74-4801-b91a-3e9707e100aa'></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Linear scale: Good for high-frequency details\n", "Log scale: Better for low-frequency bands (microseisms, infrasound)\n"]}], "source": ["# Compare linear vs log frequency scales\n", "print(\"Creating frequency scale comparison...\")\n", "fig_comp, axes_comp = compare_frequency_scales(results, f\"{data_type}_Scale_Comparison\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. <PERSON><PERSON> Comparison"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating whitening comparison...\n"]}, {"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "/* global mpl */\n", "window.mpl = {};\n", "\n", "mpl.get_websocket_type = function () {\n", "    if (typeof WebSocket !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof MozWebSocket !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert(\n", "            'Your browser does not have WebSocket support. ' +\n", "                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "                'Firefox 4 and 5 are also supported but you ' +\n", "                'have to enable WebSockets in about:config.'\n", "        );\n", "    }\n", "};\n", "\n", "mpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = this.ws.binaryType !== undefined;\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById('mpl-warnings');\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent =\n", "                'This browser does not support binary websocket messages. ' +\n", "                'Performance may be slow.';\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = document.createElement('div');\n", "    this.root.setAttribute('style', 'display: inline-block');\n", "    this._root_extra_style(this.root);\n", "\n", "    parent_element.appendChild(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen = function () {\n", "        fig.send_message('supports_binary', { value: fig.supports_binary });\n", "        fig.send_message('send_image_mode', {});\n", "        if (fig.ratio !== 1) {\n", "            fig.send_message('set_device_pixel_ratio', {\n", "                device_pixel_ratio: fig.ratio,\n", "            });\n", "        }\n", "        fig.send_message('refresh', {});\n", "    };\n", "\n", "    this.imageObj.onload = function () {\n", "        if (fig.image_mode === 'full') {\n", "            // Full images could contain transparency (where diff images\n", "            // almost always do), so we need to clear the canvas so that\n", "            // there is no ghosting.\n", "            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "        }\n", "        fig.context.drawImage(fig.imageObj, 0, 0);\n", "    };\n", "\n", "    this.imageObj.onunload = function () {\n", "        fig.ws.close();\n", "    };\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "};\n", "\n", "mpl.figure.prototype._init_header = function () {\n", "    var titlebar = document.createElement('div');\n", "    titlebar.classList =\n", "        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n", "    var titletext = document.createElement('div');\n", "    titletext.classList = 'ui-dialog-title';\n", "    titletext.setAttribute(\n", "        'style',\n", "        'width: 100%; text-align: center; padding: 3px;'\n", "    );\n", "    titlebar.appendChild(titletext);\n", "    this.root.appendChild(titlebar);\n", "    this.header = titletext;\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._init_canvas = function () {\n", "    var fig = this;\n", "\n", "    var canvas_div = (this.canvas_div = document.createElement('div'));\n", "    canvas_div.setAttribute('tabindex', '0');\n", "    canvas_div.setAttribute(\n", "        'style',\n", "        'border: 1px solid #ddd;' +\n", "            'box-sizing: content-box;' +\n", "            'clear: both;' +\n", "            'min-height: 1px;' +\n", "            'min-width: 1px;' +\n", "            'outline: 0;' +\n", "            'overflow: hidden;' +\n", "            'position: relative;' +\n", "            'resize: both;' +\n", "            'z-index: 2;'\n", "    );\n", "\n", "    function on_keyboard_event_closure(name) {\n", "        return function (event) {\n", "            return fig.key_event(event, name);\n", "        };\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'keydown',\n", "        on_keyboard_event_closure('key_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'keyup',\n", "        on_keyboard_event_closure('key_release')\n", "    );\n", "\n", "    this._canvas_extra_style(canvas_div);\n", "    this.root.appendChild(canvas_div);\n", "\n", "    var canvas = (this.canvas = document.createElement('canvas'));\n", "    canvas.classList.add('mpl-canvas');\n", "    canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'pointer-events: none;' +\n", "            'position: relative;' +\n", "            'z-index: 0;'\n", "    );\n", "\n", "    this.context = canvas.getContext('2d');\n", "\n", "    var backingStore =\n", "        this.context.backingStorePixelRatio ||\n", "        this.context.webkitBackingStorePixelRatio ||\n", "        this.context.mozBackingStorePixelRatio ||\n", "        this.context.msBackingStorePixelRatio ||\n", "        this.context.oBackingStorePixelRatio ||\n", "        this.context.backingStorePixelRatio ||\n", "        1;\n", "\n", "    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n", "        'canvas'\n", "    ));\n", "    rubberband_canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'left: 0;' +\n", "            'pointer-events: none;' +\n", "            'position: absolute;' +\n", "            'top: 0;' +\n", "            'z-index: 1;'\n", "    );\n", "\n", "    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n", "    if (this.ResizeObserver === undefined) {\n", "        if (window.ResizeObserver !== undefined) {\n", "            this.ResizeObserver = window.ResizeObserver;\n", "        } else {\n", "            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n", "            this.ResizeObserver = obs.ResizeObserver;\n", "        }\n", "    }\n", "\n", "    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n", "        // There's no need to resize if the WebSocket is not connected:\n", "        // - If it is still connecting, then we will get an initial resize from\n", "        //   Python once it connects.\n", "        // - If it has disconnected, then resizing will clear the canvas and\n", "        //   never get anything back to refill it, so better to not resize and\n", "        //   keep something visible.\n", "        if (fig.ws.readyState != 1) {\n", "            return;\n", "        }\n", "        var nentries = entries.length;\n", "        for (var i = 0; i < nentries; i++) {\n", "            var entry = entries[i];\n", "            var width, height;\n", "            if (entry.contentBoxSize) {\n", "                if (entry.contentBoxSize instanceof Array) {\n", "                    // Chrome 84 implements new version of spec.\n", "                    width = entry.contentBoxSize[0].inlineSize;\n", "                    height = entry.contentBoxSize[0].blockSize;\n", "                } else {\n", "                    // Firefox implements old version of spec.\n", "                    width = entry.contentBoxSize.inlineSize;\n", "                    height = entry.contentBoxSize.blockSize;\n", "                }\n", "            } else {\n", "                // Chrome <84 implements even older version of spec.\n", "                width = entry.contentRect.width;\n", "                height = entry.contentRect.height;\n", "            }\n", "\n", "            // Keep the size of the canvas and rubber band canvas in sync with\n", "            // the canvas container.\n", "            if (entry.devicePixelContentBoxSize) {\n", "                // Chrome 84 implements new version of spec.\n", "                canvas.setAttribute(\n", "                    'width',\n", "                    entry.devicePixelContentBoxSize[0].inlineSize\n", "                );\n", "                canvas.setAttribute(\n", "                    'height',\n", "                    entry.devicePixelContentBoxSize[0].blockSize\n", "                );\n", "            } else {\n", "                canvas.setAttribute('width', width * fig.ratio);\n", "                canvas.setAttribute('height', height * fig.ratio);\n", "            }\n", "            /* This rescales the canvas back to display pixels, so that it\n", "             * appears correct on HiDPI screens. */\n", "            canvas.style.width = width + 'px';\n", "            canvas.style.height = height + 'px';\n", "\n", "            rubberband_canvas.setAttribute('width', width);\n", "            rubberband_canvas.setAttribute('height', height);\n", "\n", "            // And update the size in Python. We ignore the initial 0/0 size\n", "            // that occurs as the element is placed into the DOM, which should\n", "            // otherwise not happen due to the minimum size styling.\n", "            if (width != 0 && height != 0) {\n", "                fig.request_resize(width, height);\n", "            }\n", "        }\n", "    });\n", "    this.resizeObserverInstance.observe(canvas_div);\n", "\n", "    function on_mouse_event_closure(name) {\n", "        /* User Agent sniffing is bad, but WebKit is busted:\n", "         * https://bugs.webkit.org/show_bug.cgi?id=144526\n", "         * https://bugs.webkit.org/show_bug.cgi?id=181818\n", "         * The worst that happens here is that they get an extra browser\n", "         * selection when dragging, if this check fails to catch them.\n", "         */\n", "        var UA = navigator.userAgent;\n", "        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n", "        if(isWebKit) {\n", "            return function (event) {\n", "                /* This prevents the web browser from automatically changing to\n", "                 * the text insertion cursor when the button is pressed. We\n", "                 * want to control all of the cursor setting manually through\n", "                 * the 'cursor' event from matplotlib */\n", "                event.preventDefault()\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        } else {\n", "            return function (event) {\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        }\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'mousedown',\n", "        on_mouse_event_closure('button_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseup',\n", "        on_mouse_event_closure('button_release')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'dblclick',\n", "        on_mouse_event_closure('dblclick')\n", "    );\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    canvas_div.addEventListener(\n", "        'mousemove',\n", "        on_mouse_event_closure('motion_notify')\n", "    );\n", "\n", "    canvas_div.addEventListener(\n", "        'mouseenter',\n", "        on_mouse_event_closure('figure_enter')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseleave',\n", "        on_mouse_event_closure('figure_leave')\n", "    );\n", "\n", "    canvas_div.addEventListener('wheel', function (event) {\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        on_mouse_event_closure('scroll')(event);\n", "    });\n", "\n", "    canvas_div.appendChild(canvas);\n", "    canvas_div.appendChild(rubberband_canvas);\n", "\n", "    this.rubberband_context = rubberband_canvas.getContext('2d');\n", "    this.rubberband_context.strokeStyle = '#000000';\n", "\n", "    this._resize_canvas = function (width, height, forward) {\n", "        if (forward) {\n", "            canvas_div.style.width = width + 'px';\n", "            canvas_div.style.height = height + 'px';\n", "        }\n", "    };\n", "\n", "    // Disable right mouse context menu.\n", "    canvas_div.addEventListener('contextmenu', function (_e) {\n", "        event.preventDefault();\n", "        return false;\n", "    });\n", "\n", "    function set_focus() {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'mpl-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'mpl-button-group';\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'mpl-button-group';\n", "            continue;\n", "        }\n", "\n", "        var button = (fig.buttons[name] = document.createElement('button'));\n", "        button.classList = 'mpl-widget';\n", "        button.setAttribute('role', 'button');\n", "        button.setAttribute('aria-disabled', 'false');\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "\n", "        var icon_img = document.createElement('img');\n", "        icon_img.src = '_images/' + image + '.png';\n", "        icon_img.srcset = '_images/' + image + '_large.png 2x';\n", "        icon_img.alt = tooltip;\n", "        button.appendChild(icon_img);\n", "\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    var fmt_picker = document.createElement('select');\n", "    fmt_picker.classList = 'mpl-widget';\n", "    toolbar.appendChild(fmt_picker);\n", "    this.format_dropdown = fmt_picker;\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = document.createElement('option');\n", "        option.selected = fmt === mpl.default_extension;\n", "        option.innerHTML = fmt;\n", "        fmt_picker.appendChild(option);\n", "    }\n", "\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "};\n", "\n", "mpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', { width: x_pixels, height: y_pixels });\n", "};\n", "\n", "mpl.figure.prototype.send_message = function (type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "};\n", "\n", "mpl.figure.prototype.send_draw_message = function () {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "};\n", "\n", "mpl.figure.prototype.handle_resize = function (fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1], msg['forward']);\n", "        fig.send_message('refresh', {});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_rubberband = function (fig, msg) {\n", "    var x0 = msg['x0'] / fig.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n", "    var x1 = msg['x1'] / fig.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0,\n", "        0,\n", "        fig.canvas.width / fig.ratio,\n", "        fig.canvas.height / fig.ratio\n", "    );\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "};\n", "\n", "mpl.figure.prototype.handle_figure_label = function (fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "};\n", "\n", "mpl.figure.prototype.handle_cursor = function (fig, msg) {\n", "    fig.canvas_div.style.cursor = msg['cursor'];\n", "};\n", "\n", "mpl.figure.prototype.handle_message = function (fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "};\n", "\n", "mpl.figure.prototype.handle_draw = function (fig, _msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "};\n", "\n", "mpl.figure.prototype.handle_image_mode = function (fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "};\n", "\n", "mpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n", "    for (var key in msg) {\n", "        if (!(key in fig.buttons)) {\n", "            continue;\n", "        }\n", "        fig.buttons[key].disabled = !msg[key];\n", "        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n", "    if (msg['mode'] === 'PAN') {\n", "        fig.buttons['Pan'].classList.add('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    } else if (msg['mode'] === 'ZOOM') {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.add('active');\n", "    } else {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message('ack', {});\n", "};\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function (fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            var img = evt.data;\n", "            if (img.type !== 'image/png') {\n", "                /* FIXME: We get \"Resource interpreted as Image but\n", "                 * transferred with MIME type text/plain:\" errors on\n", "                 * Chrome.  But how to set the MIME type?  It doesn't seem\n", "                 * to be part of the websocket stream */\n", "                img.type = 'image/png';\n", "            }\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src\n", "                );\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                img\n", "            );\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        } else if (\n", "            typeof evt.data === 'string' &&\n", "            evt.data.slice(0, 21) === 'data:image/png;base64'\n", "        ) {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig['handle_' + msg_type];\n", "        } catch (e) {\n", "            console.log(\n", "                \"No handler for the '%s' message type: \",\n", "                msg_type,\n", "                msg\n", "            );\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '%s' message: \", msg_type, msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\n", "                    \"Exception inside the 'handler_%s' callback:\",\n", "                    msg_type,\n", "                    e,\n", "                    e.stack,\n", "                    msg\n", "                );\n", "            }\n", "        }\n", "    };\n", "};\n", "\n", "function getModifiers(event) {\n", "    var mods = [];\n", "    if (event.ctrlKey) {\n", "        mods.push('ctrl');\n", "    }\n", "    if (event.altKey) {\n", "        mods.push('alt');\n", "    }\n", "    if (event.shiftKey) {\n", "        mods.push('shift');\n", "    }\n", "    if (event.metaKey) {\n", "        mods.push('meta');\n", "    }\n", "    return mods;\n", "}\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * https://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s(original) {\n", "    return Object.keys(original).reduce(function (obj, key) {\n", "        if (typeof original[key] !== 'object') {\n", "            obj[key] = original[key];\n", "        }\n", "        return obj;\n", "    }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function (event, name) {\n", "    if (name === 'button_press') {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    // from https://stackoverflow.com/q/1114465\n", "    var boundingRect = this.canvas.getBoundingClientRect();\n", "    var x = (event.clientX - boundingRect.left) * this.ratio;\n", "    var y = (event.clientY - boundingRect.top) * this.ratio;\n", "\n", "    this.send_message(name, {\n", "        x: x,\n", "        y: y,\n", "        button: event.button,\n", "        step: event.step,\n", "        buttons: event.buttons,\n", "        modifiers: getModifiers(event),\n", "        guiEvent: simple<PERSON>eys(event),\n", "    });\n", "\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (_event, _name) {\n", "    // Handle any extra behaviour associated with a key event\n", "};\n", "\n", "mpl.figure.prototype.key_event = function (event, name) {\n", "    // Prevent repeat events\n", "    if (name === 'key_press') {\n", "        if (event.key === this._key) {\n", "            return;\n", "        } else {\n", "            this._key = event.key;\n", "        }\n", "    }\n", "    if (name === 'key_release') {\n", "        this._key = null;\n", "    }\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.key !== 'Control') {\n", "        value += 'ctrl+';\n", "    }\n", "    else if (event.altKey && event.key !== 'Alt') {\n", "        value += 'alt+';\n", "    }\n", "    else if (event.shiftKey && event.key !== 'Shift') {\n", "        value += 'shift+';\n", "    }\n", "\n", "    value += 'k' + event.key;\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function (name) {\n", "    if (name === 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message('toolbar_button', { name: name });\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "\n", "///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n", "// prettier-ignore\n", "var _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n", "\n", "mpl.default_extension = \"png\";/* global mpl */\n", "\n", "var comm_websocket_adapter = function (comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.binaryType = comm.kernel.ws.binaryType;\n", "    ws.readyState = comm.kernel.ws.readyState;\n", "    function updateReadyState(_event) {\n", "        if (comm.kernel.ws) {\n", "            ws.readyState = comm.kernel.ws.readyState;\n", "        } else {\n", "            ws.readyState = 3; // Closed state.\n", "        }\n", "    }\n", "    comm.kernel.ws.addEventListener('open', updateReadyState);\n", "    comm.kernel.ws.addEventListener('close', updateReadyState);\n", "    comm.kernel.ws.addEventListener('error', updateReadyState);\n", "\n", "    ws.close = function () {\n", "        comm.close();\n", "    };\n", "    ws.send = function (m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function (msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        var data = msg['content']['data'];\n", "        if (data['blob'] !== undefined) {\n", "            data = {\n", "                data: new Blob(msg['buffers'], { type: data['blob'] }),\n", "            };\n", "        }\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(data);\n", "    });\n", "    return ws;\n", "};\n", "\n", "mpl.mpl_figure_comm = function (comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = document.getElementById(id);\n", "    var ws_proxy = comm_websocket_adapter(comm);\n", "\n", "    function ondownload(figure, _format) {\n", "        window.open(figure.canvas.toDataURL());\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element;\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error('Failed to find cell for figure', id, fig);\n", "        return;\n", "    }\n", "    fig.cell_info[0].output_area.element.on(\n", "        'cleared',\n", "        { fig: fig },\n", "        fig._remove_fig_handler\n", "    );\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function (fig, msg) {\n", "    var width = fig.canvas.width / fig.ratio;\n", "    fig.cell_info[0].output_area.element.off(\n", "        'cleared',\n", "        fig._remove_fig_handler\n", "    );\n", "    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable();\n", "    fig.parent_element.innerHTML =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "    fig.close_ws(fig, msg);\n", "};\n", "\n", "mpl.figure.prototype.close_ws = function (fig, msg) {\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "};\n", "\n", "mpl.figure.prototype.push_to_output = function (_remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width / this.ratio;\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message('ack', {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () {\n", "        fig.push_to_output();\n", "    }, 1000);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'btn-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'btn-group';\n", "    var button;\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'btn-group';\n", "            continue;\n", "        }\n", "\n", "        button = fig.buttons[name] = document.createElement('button');\n", "        button.classList = 'btn btn-default';\n", "        button.href = '#';\n", "        button.title = name;\n", "        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message pull-right';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = document.createElement('div');\n", "    buttongrp.classList = 'btn-group inline pull-right';\n", "    button = document.createElement('button');\n", "    button.classList = 'btn btn-mini btn-primary';\n", "    button.href = '#';\n", "    button.title = 'Stop Interaction';\n", "    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n", "    button.addEventListener('click', function (_evt) {\n", "        fig.handle_close(fig, {});\n", "    });\n", "    button.addEventListener(\n", "        'mouseover',\n", "        on_mouseover_closure('Stop Interaction')\n", "    );\n", "    buttongrp.appendChild(button);\n", "    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n", "    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n", "};\n", "\n", "mpl.figure.prototype._remove_fig_handler = function (event) {\n", "    var fig = event.data.fig;\n", "    if (event.target !== this) {\n", "        // Ignore bubbled events from children.\n", "        return;\n", "    }\n", "    fig.close_ws(fig, {});\n", "};\n", "\n", "mpl.figure.prototype._root_extra_style = function (el) {\n", "    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (el) {\n", "    // this is important to make the div 'focusable\n", "    el.setAttribute('tabindex', 0);\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    } else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (event, _name) {\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which === 13) {\n", "        this.canvas_div.blur();\n", "        // select the cell after this one\n", "        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n", "        IPython.notebook.select(index + 1);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    fig.ondownload(fig, null);\n", "};\n", "\n", "mpl.find_output_cell = function (html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i = 0; i < ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code') {\n", "            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] === html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "};\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel !== null) {\n", "    IPython.notebook.kernel.comm_manager.register_target(\n", "        'matplotlib',\n", "        mpl.mpl_figure_comm\n", "    );\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div id='701e6f53-123e-4a63-ac6c-0e9f451f9bd6'></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Original Spectrogram: Power range [-58.9, 26.4] dB\n", "Whitened Spectrogram (Mean Removed): Power range [-53.5, 15.4] dB\n", "Original: Shows absolute power levels\n", "Whitened: Highlights temporal variations (anomalies more visible)\n"]}], "source": ["# Compare original vs whitened spectrograms\n", "print(\"Creating whitening comparison...\")\n", "fig_white, axes_white = compare_whitened_spectrograms(results, f\"{data_type}_Whitening_Comparison\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. 🎯 Interactive Anomaly Explorer\n", "\n", "**This is the main interactive tool for exploring anomalies in detail!**\n", "\n", "### Controls:\n", "- **Click and drag** on the main time series plot to zoom to a time range\n", "- **Press 'r'** to reset zoom to full range\n", "- **Anomalies** are marked with dashed vertical lines and colored dots\n", "- **Each color** represents a different frequency band\n", "- **Console output** shows anomalies in selected time ranges"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🚀 LAUNCHING INTERACTIVE ANOMALY EXPLORER\n", "==================================================\n"]}, {"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "/* global mpl */\n", "window.mpl = {};\n", "\n", "mpl.get_websocket_type = function () {\n", "    if (typeof WebSocket !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof MozWebSocket !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert(\n", "            'Your browser does not have WebSocket support. ' +\n", "                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "                'Firefox 4 and 5 are also supported but you ' +\n", "                'have to enable WebSockets in about:config.'\n", "        );\n", "    }\n", "};\n", "\n", "mpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = this.ws.binaryType !== undefined;\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById('mpl-warnings');\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent =\n", "                'This browser does not support binary websocket messages. ' +\n", "                'Performance may be slow.';\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = document.createElement('div');\n", "    this.root.setAttribute('style', 'display: inline-block');\n", "    this._root_extra_style(this.root);\n", "\n", "    parent_element.appendChild(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen = function () {\n", "        fig.send_message('supports_binary', { value: fig.supports_binary });\n", "        fig.send_message('send_image_mode', {});\n", "        if (fig.ratio !== 1) {\n", "            fig.send_message('set_device_pixel_ratio', {\n", "                device_pixel_ratio: fig.ratio,\n", "            });\n", "        }\n", "        fig.send_message('refresh', {});\n", "    };\n", "\n", "    this.imageObj.onload = function () {\n", "        if (fig.image_mode === 'full') {\n", "            // Full images could contain transparency (where diff images\n", "            // almost always do), so we need to clear the canvas so that\n", "            // there is no ghosting.\n", "            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "        }\n", "        fig.context.drawImage(fig.imageObj, 0, 0);\n", "    };\n", "\n", "    this.imageObj.onunload = function () {\n", "        fig.ws.close();\n", "    };\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "};\n", "\n", "mpl.figure.prototype._init_header = function () {\n", "    var titlebar = document.createElement('div');\n", "    titlebar.classList =\n", "        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n", "    var titletext = document.createElement('div');\n", "    titletext.classList = 'ui-dialog-title';\n", "    titletext.setAttribute(\n", "        'style',\n", "        'width: 100%; text-align: center; padding: 3px;'\n", "    );\n", "    titlebar.appendChild(titletext);\n", "    this.root.appendChild(titlebar);\n", "    this.header = titletext;\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._init_canvas = function () {\n", "    var fig = this;\n", "\n", "    var canvas_div = (this.canvas_div = document.createElement('div'));\n", "    canvas_div.setAttribute('tabindex', '0');\n", "    canvas_div.setAttribute(\n", "        'style',\n", "        'border: 1px solid #ddd;' +\n", "            'box-sizing: content-box;' +\n", "            'clear: both;' +\n", "            'min-height: 1px;' +\n", "            'min-width: 1px;' +\n", "            'outline: 0;' +\n", "            'overflow: hidden;' +\n", "            'position: relative;' +\n", "            'resize: both;' +\n", "            'z-index: 2;'\n", "    );\n", "\n", "    function on_keyboard_event_closure(name) {\n", "        return function (event) {\n", "            return fig.key_event(event, name);\n", "        };\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'keydown',\n", "        on_keyboard_event_closure('key_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'keyup',\n", "        on_keyboard_event_closure('key_release')\n", "    );\n", "\n", "    this._canvas_extra_style(canvas_div);\n", "    this.root.appendChild(canvas_div);\n", "\n", "    var canvas = (this.canvas = document.createElement('canvas'));\n", "    canvas.classList.add('mpl-canvas');\n", "    canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'pointer-events: none;' +\n", "            'position: relative;' +\n", "            'z-index: 0;'\n", "    );\n", "\n", "    this.context = canvas.getContext('2d');\n", "\n", "    var backingStore =\n", "        this.context.backingStorePixelRatio ||\n", "        this.context.webkitBackingStorePixelRatio ||\n", "        this.context.mozBackingStorePixelRatio ||\n", "        this.context.msBackingStorePixelRatio ||\n", "        this.context.oBackingStorePixelRatio ||\n", "        this.context.backingStorePixelRatio ||\n", "        1;\n", "\n", "    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n", "        'canvas'\n", "    ));\n", "    rubberband_canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'left: 0;' +\n", "            'pointer-events: none;' +\n", "            'position: absolute;' +\n", "            'top: 0;' +\n", "            'z-index: 1;'\n", "    );\n", "\n", "    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n", "    if (this.ResizeObserver === undefined) {\n", "        if (window.ResizeObserver !== undefined) {\n", "            this.ResizeObserver = window.ResizeObserver;\n", "        } else {\n", "            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n", "            this.ResizeObserver = obs.ResizeObserver;\n", "        }\n", "    }\n", "\n", "    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n", "        // There's no need to resize if the WebSocket is not connected:\n", "        // - If it is still connecting, then we will get an initial resize from\n", "        //   Python once it connects.\n", "        // - If it has disconnected, then resizing will clear the canvas and\n", "        //   never get anything back to refill it, so better to not resize and\n", "        //   keep something visible.\n", "        if (fig.ws.readyState != 1) {\n", "            return;\n", "        }\n", "        var nentries = entries.length;\n", "        for (var i = 0; i < nentries; i++) {\n", "            var entry = entries[i];\n", "            var width, height;\n", "            if (entry.contentBoxSize) {\n", "                if (entry.contentBoxSize instanceof Array) {\n", "                    // Chrome 84 implements new version of spec.\n", "                    width = entry.contentBoxSize[0].inlineSize;\n", "                    height = entry.contentBoxSize[0].blockSize;\n", "                } else {\n", "                    // Firefox implements old version of spec.\n", "                    width = entry.contentBoxSize.inlineSize;\n", "                    height = entry.contentBoxSize.blockSize;\n", "                }\n", "            } else {\n", "                // Chrome <84 implements even older version of spec.\n", "                width = entry.contentRect.width;\n", "                height = entry.contentRect.height;\n", "            }\n", "\n", "            // Keep the size of the canvas and rubber band canvas in sync with\n", "            // the canvas container.\n", "            if (entry.devicePixelContentBoxSize) {\n", "                // Chrome 84 implements new version of spec.\n", "                canvas.setAttribute(\n", "                    'width',\n", "                    entry.devicePixelContentBoxSize[0].inlineSize\n", "                );\n", "                canvas.setAttribute(\n", "                    'height',\n", "                    entry.devicePixelContentBoxSize[0].blockSize\n", "                );\n", "            } else {\n", "                canvas.setAttribute('width', width * fig.ratio);\n", "                canvas.setAttribute('height', height * fig.ratio);\n", "            }\n", "            /* This rescales the canvas back to display pixels, so that it\n", "             * appears correct on HiDPI screens. */\n", "            canvas.style.width = width + 'px';\n", "            canvas.style.height = height + 'px';\n", "\n", "            rubberband_canvas.setAttribute('width', width);\n", "            rubberband_canvas.setAttribute('height', height);\n", "\n", "            // And update the size in Python. We ignore the initial 0/0 size\n", "            // that occurs as the element is placed into the DOM, which should\n", "            // otherwise not happen due to the minimum size styling.\n", "            if (width != 0 && height != 0) {\n", "                fig.request_resize(width, height);\n", "            }\n", "        }\n", "    });\n", "    this.resizeObserverInstance.observe(canvas_div);\n", "\n", "    function on_mouse_event_closure(name) {\n", "        /* User Agent sniffing is bad, but WebKit is busted:\n", "         * https://bugs.webkit.org/show_bug.cgi?id=144526\n", "         * https://bugs.webkit.org/show_bug.cgi?id=181818\n", "         * The worst that happens here is that they get an extra browser\n", "         * selection when dragging, if this check fails to catch them.\n", "         */\n", "        var UA = navigator.userAgent;\n", "        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n", "        if(isWebKit) {\n", "            return function (event) {\n", "                /* This prevents the web browser from automatically changing to\n", "                 * the text insertion cursor when the button is pressed. We\n", "                 * want to control all of the cursor setting manually through\n", "                 * the 'cursor' event from matplotlib */\n", "                event.preventDefault()\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        } else {\n", "            return function (event) {\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        }\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'mousedown',\n", "        on_mouse_event_closure('button_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseup',\n", "        on_mouse_event_closure('button_release')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'dblclick',\n", "        on_mouse_event_closure('dblclick')\n", "    );\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    canvas_div.addEventListener(\n", "        'mousemove',\n", "        on_mouse_event_closure('motion_notify')\n", "    );\n", "\n", "    canvas_div.addEventListener(\n", "        'mouseenter',\n", "        on_mouse_event_closure('figure_enter')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseleave',\n", "        on_mouse_event_closure('figure_leave')\n", "    );\n", "\n", "    canvas_div.addEventListener('wheel', function (event) {\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        on_mouse_event_closure('scroll')(event);\n", "    });\n", "\n", "    canvas_div.appendChild(canvas);\n", "    canvas_div.appendChild(rubberband_canvas);\n", "\n", "    this.rubberband_context = rubberband_canvas.getContext('2d');\n", "    this.rubberband_context.strokeStyle = '#000000';\n", "\n", "    this._resize_canvas = function (width, height, forward) {\n", "        if (forward) {\n", "            canvas_div.style.width = width + 'px';\n", "            canvas_div.style.height = height + 'px';\n", "        }\n", "    };\n", "\n", "    // Disable right mouse context menu.\n", "    canvas_div.addEventListener('contextmenu', function (_e) {\n", "        event.preventDefault();\n", "        return false;\n", "    });\n", "\n", "    function set_focus() {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'mpl-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'mpl-button-group';\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'mpl-button-group';\n", "            continue;\n", "        }\n", "\n", "        var button = (fig.buttons[name] = document.createElement('button'));\n", "        button.classList = 'mpl-widget';\n", "        button.setAttribute('role', 'button');\n", "        button.setAttribute('aria-disabled', 'false');\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "\n", "        var icon_img = document.createElement('img');\n", "        icon_img.src = '_images/' + image + '.png';\n", "        icon_img.srcset = '_images/' + image + '_large.png 2x';\n", "        icon_img.alt = tooltip;\n", "        button.appendChild(icon_img);\n", "\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    var fmt_picker = document.createElement('select');\n", "    fmt_picker.classList = 'mpl-widget';\n", "    toolbar.appendChild(fmt_picker);\n", "    this.format_dropdown = fmt_picker;\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = document.createElement('option');\n", "        option.selected = fmt === mpl.default_extension;\n", "        option.innerHTML = fmt;\n", "        fmt_picker.appendChild(option);\n", "    }\n", "\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "};\n", "\n", "mpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', { width: x_pixels, height: y_pixels });\n", "};\n", "\n", "mpl.figure.prototype.send_message = function (type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "};\n", "\n", "mpl.figure.prototype.send_draw_message = function () {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "};\n", "\n", "mpl.figure.prototype.handle_resize = function (fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1], msg['forward']);\n", "        fig.send_message('refresh', {});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_rubberband = function (fig, msg) {\n", "    var x0 = msg['x0'] / fig.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n", "    var x1 = msg['x1'] / fig.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0,\n", "        0,\n", "        fig.canvas.width / fig.ratio,\n", "        fig.canvas.height / fig.ratio\n", "    );\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "};\n", "\n", "mpl.figure.prototype.handle_figure_label = function (fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "};\n", "\n", "mpl.figure.prototype.handle_cursor = function (fig, msg) {\n", "    fig.canvas_div.style.cursor = msg['cursor'];\n", "};\n", "\n", "mpl.figure.prototype.handle_message = function (fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "};\n", "\n", "mpl.figure.prototype.handle_draw = function (fig, _msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "};\n", "\n", "mpl.figure.prototype.handle_image_mode = function (fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "};\n", "\n", "mpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n", "    for (var key in msg) {\n", "        if (!(key in fig.buttons)) {\n", "            continue;\n", "        }\n", "        fig.buttons[key].disabled = !msg[key];\n", "        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n", "    if (msg['mode'] === 'PAN') {\n", "        fig.buttons['Pan'].classList.add('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    } else if (msg['mode'] === 'ZOOM') {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.add('active');\n", "    } else {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message('ack', {});\n", "};\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function (fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            var img = evt.data;\n", "            if (img.type !== 'image/png') {\n", "                /* FIXME: We get \"Resource interpreted as Image but\n", "                 * transferred with MIME type text/plain:\" errors on\n", "                 * Chrome.  But how to set the MIME type?  It doesn't seem\n", "                 * to be part of the websocket stream */\n", "                img.type = 'image/png';\n", "            }\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src\n", "                );\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                img\n", "            );\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        } else if (\n", "            typeof evt.data === 'string' &&\n", "            evt.data.slice(0, 21) === 'data:image/png;base64'\n", "        ) {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig['handle_' + msg_type];\n", "        } catch (e) {\n", "            console.log(\n", "                \"No handler for the '%s' message type: \",\n", "                msg_type,\n", "                msg\n", "            );\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '%s' message: \", msg_type, msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\n", "                    \"Exception inside the 'handler_%s' callback:\",\n", "                    msg_type,\n", "                    e,\n", "                    e.stack,\n", "                    msg\n", "                );\n", "            }\n", "        }\n", "    };\n", "};\n", "\n", "function getModifiers(event) {\n", "    var mods = [];\n", "    if (event.ctrlKey) {\n", "        mods.push('ctrl');\n", "    }\n", "    if (event.altKey) {\n", "        mods.push('alt');\n", "    }\n", "    if (event.shiftKey) {\n", "        mods.push('shift');\n", "    }\n", "    if (event.metaKey) {\n", "        mods.push('meta');\n", "    }\n", "    return mods;\n", "}\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * https://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s(original) {\n", "    return Object.keys(original).reduce(function (obj, key) {\n", "        if (typeof original[key] !== 'object') {\n", "            obj[key] = original[key];\n", "        }\n", "        return obj;\n", "    }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function (event, name) {\n", "    if (name === 'button_press') {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    // from https://stackoverflow.com/q/1114465\n", "    var boundingRect = this.canvas.getBoundingClientRect();\n", "    var x = (event.clientX - boundingRect.left) * this.ratio;\n", "    var y = (event.clientY - boundingRect.top) * this.ratio;\n", "\n", "    this.send_message(name, {\n", "        x: x,\n", "        y: y,\n", "        button: event.button,\n", "        step: event.step,\n", "        buttons: event.buttons,\n", "        modifiers: getModifiers(event),\n", "        guiEvent: simple<PERSON>eys(event),\n", "    });\n", "\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (_event, _name) {\n", "    // Handle any extra behaviour associated with a key event\n", "};\n", "\n", "mpl.figure.prototype.key_event = function (event, name) {\n", "    // Prevent repeat events\n", "    if (name === 'key_press') {\n", "        if (event.key === this._key) {\n", "            return;\n", "        } else {\n", "            this._key = event.key;\n", "        }\n", "    }\n", "    if (name === 'key_release') {\n", "        this._key = null;\n", "    }\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.key !== 'Control') {\n", "        value += 'ctrl+';\n", "    }\n", "    else if (event.altKey && event.key !== 'Alt') {\n", "        value += 'alt+';\n", "    }\n", "    else if (event.shiftKey && event.key !== 'Shift') {\n", "        value += 'shift+';\n", "    }\n", "\n", "    value += 'k' + event.key;\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function (name) {\n", "    if (name === 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message('toolbar_button', { name: name });\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "\n", "///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n", "// prettier-ignore\n", "var _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n", "\n", "mpl.default_extension = \"png\";/* global mpl */\n", "\n", "var comm_websocket_adapter = function (comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.binaryType = comm.kernel.ws.binaryType;\n", "    ws.readyState = comm.kernel.ws.readyState;\n", "    function updateReadyState(_event) {\n", "        if (comm.kernel.ws) {\n", "            ws.readyState = comm.kernel.ws.readyState;\n", "        } else {\n", "            ws.readyState = 3; // Closed state.\n", "        }\n", "    }\n", "    comm.kernel.ws.addEventListener('open', updateReadyState);\n", "    comm.kernel.ws.addEventListener('close', updateReadyState);\n", "    comm.kernel.ws.addEventListener('error', updateReadyState);\n", "\n", "    ws.close = function () {\n", "        comm.close();\n", "    };\n", "    ws.send = function (m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function (msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        var data = msg['content']['data'];\n", "        if (data['blob'] !== undefined) {\n", "            data = {\n", "                data: new Blob(msg['buffers'], { type: data['blob'] }),\n", "            };\n", "        }\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(data);\n", "    });\n", "    return ws;\n", "};\n", "\n", "mpl.mpl_figure_comm = function (comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = document.getElementById(id);\n", "    var ws_proxy = comm_websocket_adapter(comm);\n", "\n", "    function ondownload(figure, _format) {\n", "        window.open(figure.canvas.toDataURL());\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element;\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error('Failed to find cell for figure', id, fig);\n", "        return;\n", "    }\n", "    fig.cell_info[0].output_area.element.on(\n", "        'cleared',\n", "        { fig: fig },\n", "        fig._remove_fig_handler\n", "    );\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function (fig, msg) {\n", "    var width = fig.canvas.width / fig.ratio;\n", "    fig.cell_info[0].output_area.element.off(\n", "        'cleared',\n", "        fig._remove_fig_handler\n", "    );\n", "    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable();\n", "    fig.parent_element.innerHTML =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "    fig.close_ws(fig, msg);\n", "};\n", "\n", "mpl.figure.prototype.close_ws = function (fig, msg) {\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "};\n", "\n", "mpl.figure.prototype.push_to_output = function (_remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width / this.ratio;\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message('ack', {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () {\n", "        fig.push_to_output();\n", "    }, 1000);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'btn-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'btn-group';\n", "    var button;\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'btn-group';\n", "            continue;\n", "        }\n", "\n", "        button = fig.buttons[name] = document.createElement('button');\n", "        button.classList = 'btn btn-default';\n", "        button.href = '#';\n", "        button.title = name;\n", "        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message pull-right';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = document.createElement('div');\n", "    buttongrp.classList = 'btn-group inline pull-right';\n", "    button = document.createElement('button');\n", "    button.classList = 'btn btn-mini btn-primary';\n", "    button.href = '#';\n", "    button.title = 'Stop Interaction';\n", "    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n", "    button.addEventListener('click', function (_evt) {\n", "        fig.handle_close(fig, {});\n", "    });\n", "    button.addEventListener(\n", "        'mouseover',\n", "        on_mouseover_closure('Stop Interaction')\n", "    );\n", "    buttongrp.appendChild(button);\n", "    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n", "    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n", "};\n", "\n", "mpl.figure.prototype._remove_fig_handler = function (event) {\n", "    var fig = event.data.fig;\n", "    if (event.target !== this) {\n", "        // Ignore bubbled events from children.\n", "        return;\n", "    }\n", "    fig.close_ws(fig, {});\n", "};\n", "\n", "mpl.figure.prototype._root_extra_style = function (el) {\n", "    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (el) {\n", "    // this is important to make the div 'focusable\n", "    el.setAttribute('tabindex', 0);\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    } else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (event, _name) {\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which === 13) {\n", "        this.canvas_div.blur();\n", "        // select the cell after this one\n", "        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n", "        IPython.notebook.select(index + 1);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    fig.ondownload(fig, null);\n", "};\n", "\n", "mpl.find_output_cell = function (html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i = 0; i < ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code') {\n", "            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] === html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "};\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel !== null) {\n", "    IPython.notebook.kernel.comm_manager.register_target(\n", "        'matplotlib',\n", "        mpl.mpl_figure_comm\n", "    );\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div id='6c791fd5-3302-464c-86ba-8bbbc5c47e23'></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🎯 INTERACTIVE ANOMALY EXPLORER LAUNCHED\n", "Total anomalies found: 6\n", "Time range: 1081.0 - 1681.0 seconds\n", "Frequency bands analyzed: 3\n", "\n", "Anomalies detected:\n", "  1. t=1119.4s: low_rumble band\n", "  2. t=1324.2s: low_rumble band\n", "  3. t=1119.4s: traffic_low band\n", "  4. t=1221.8s: traffic_low band\n", "  5. t=1255.9s: traffic_high band\n", "  6. t=1324.2s: traffic_high band\n"]}], "source": ["# Launch the interactive anomaly explorer\n", "print(\"\\n🚀 LAUNCHING INTERACTIVE ANOMALY EXPLORER\")\n", "print(\"=\" * 50)\n", "\n", "fig_interactive, axes_interactive, anomaly_info = create_interactive_anomaly_explorer(\n", "    results, f\"{data_type}_Interactive_Explorer\"\n", ")\n", "\n", "# The interactive plot will appear above this cell\n", "# Use the controls described in the markdown cell above"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 13. Anomaly Details and Analysis"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 DETAILED ANOMALY ANALYSIS\n", "========================================\n", "\n", "Anomaly #1:\n", "  Time: 1119.4 seconds\n", "  Frequency band: low_rumble\n", "  Frequency range: 0.1-1.0 Hz\n", "  MSE error: 1.767735 (threshold: 1.760729)\n", "  Severity: 1.00x threshold\n", "\n", "Anomaly #2:\n", "  Time: 1324.2 seconds\n", "  Frequency band: low_rumble\n", "  Frequency range: 0.1-1.0 Hz\n", "  MSE error: 2.320135 (threshold: 1.760729)\n", "  Severity: 1.32x threshold\n", "\n", "Anomaly #3:\n", "  Time: 1119.4 seconds\n", "  Frequency band: traffic_low\n", "  Frequency range: 1.0-5.0 Hz\n", "  MSE error: 1.953141 (threshold: 1.621774)\n", "  Severity: 1.20x threshold\n", "\n", "Anomaly #4:\n", "  Time: 1221.8 seconds\n", "  Frequency band: traffic_low\n", "  Frequency range: 1.0-5.0 Hz\n", "  MSE error: 1.643952 (threshold: 1.621774)\n", "  Severity: 1.01x threshold\n", "\n", "Anomaly #5:\n", "  Time: 1255.9 seconds\n", "  Frequency band: traffic_high\n", "  Frequency range: 5.0-9.0 Hz\n", "  MSE error: 1.838382 (threshold: 1.526844)\n", "  Severity: 1.20x threshold\n", "\n", "Anomaly #6:\n", "  Time: 1324.2 seconds\n", "  Frequency band: traffic_high\n", "  Frequency range: 5.0-9.0 Hz\n", "  MSE error: 1.540000 (threshold: 1.526844)\n", "  Severity: 1.01x threshold\n", "\n", "📈 SUMMARY STATISTICS\n", "==============================\n", "Anomalies by frequency band:\n", "  low_rumble: 2 anomalies (0.1-1.0 Hz)\n", "  traffic_low: 2 anomalies (1.0-5.0 Hz)\n", "  traffic_high: 2 anomalies (5.0-9.0 Hz)\n", "\n", "Temporal distribution:\n", "  First anomaly: 1119.4s\n", "  Last anomaly: 1324.2s\n", "  Time span: 204.8s\n", "  Average interval: 41.0s\n"]}], "source": ["# Detailed analysis of detected anomalies\n", "if anomaly_info:\n", "    print(f\"\\n📊 DETAILED ANOMALY ANALYSIS\")\n", "    print(f\"=\" * 40)\n", "    \n", "    for i, anom in enumerate(anomaly_info):\n", "        print(f\"\\nAnomaly #{i+1}:\")\n", "        print(f\"  Time: {anom['time']:.1f} seconds\")\n", "        print(f\"  Frequency band: {anom['band']}\")\n", "        print(f\"  Frequency range: {anom['freq_range'][0]:.1f}-{anom['freq_range'][1]:.1f} Hz\")\n", "        \n", "        # Get the band result for more details\n", "        band_result = results['band_anomalies'][anom['band']]\n", "        \n", "        # Find the specific anomaly in the band results\n", "        anomaly_times = band_result['window_starts'][band_result['anomaly_mask']]\n", "        if len(anomaly_times) > 0:\n", "            # Find closest anomaly time\n", "            closest_idx = np.argmin(np.abs(anomaly_times - anom['time']))\n", "            if closest_idx < len(band_result['mse_errors'][band_result['anomaly_mask']]):\n", "                mse_errors_anom = band_result['mse_errors'][band_result['anomaly_mask']]\n", "                mse_value = mse_errors_anom[closest_idx]\n", "                threshold = band_result['threshold']\n", "                print(f\"  MSE error: {mse_value:.6f} (threshold: {threshold:.6f})\")\n", "                print(f\"  Severity: {mse_value/threshold:.2f}x threshold\")\n", "    \n", "    # Summary statistics\n", "    print(f\"\\n📈 SUMMARY STATISTICS\")\n", "    print(f\"=\" * 30)\n", "    \n", "    # Group anomalies by frequency band\n", "    band_counts = {}\n", "    for anom in anomaly_info:\n", "        band = anom['band']\n", "        if band not in band_counts:\n", "            band_counts[band] = 0\n", "        band_counts[band] += 1\n", "    \n", "    print(f\"Anomalies by frequency band:\")\n", "    for band, count in band_counts.items():\n", "        freq_range = results['band_anomalies'][band]['freq_range']\n", "        print(f\"  {band}: {count} anomalies ({freq_range[0]:.1f}-{freq_range[1]:.1f} Hz)\")\n", "    \n", "    # Time distribution\n", "    times = [anom['time'] for anom in anomaly_info]\n", "    if len(times) > 1:\n", "        time_span = max(times) - min(times)\n", "        print(f\"\\nTemporal distribution:\")\n", "        print(f\"  First anomaly: {min(times):.1f}s\")\n", "        print(f\"  Last anomaly: {max(times):.1f}s\")\n", "        print(f\"  Time span: {time_span:.1f}s\")\n", "        print(f\"  Average interval: {time_span/(len(times)-1):.1f}s\")\n", "\n", "else:\n", "    print(f\"\\n⚠️  No anomalies detected for detailed analysis\")\n", "    print(f\"\\nTroubleshooting suggestions:\")\n", "    print(f\"1. Try a different time segment\")\n", "    print(f\"2. Adjust detection thresholds (lower percentile)\")\n", "    print(f\"3. Use different analysis parameters\")\n", "    print(f\"4. Check if Chronos model is working properly\")\n", "    \n", "    if pipeline is None:\n", "        print(f\"\\n🔧 Chronos model not loaded - using mock predictions\")\n", "        print(f\"   Install Chronos for better anomaly detection:\")\n", "        print(f\"   pip install chronos-forecasting\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 14. Next Steps and Experimentation\n", "\n", "### Try Different Parameters:\n", "- **Different time segments**: Change `analysis_start` in cell 7\n", "- **Different sample rates**: Try 10, 25, or 50 Hz\n", "- **Different durations**: Try 300s (5 min) or 1200s (20 min)\n", "\n", "### Experiment with Detection:\n", "- **Lower thresholds**: Modify percentile from 85 to 75 or 70\n", "- **Different context lengths**: Try 32, 128, or 256\n", "- **Different prediction lengths**: Try 8, 32, or 64\n", "\n", "### Advanced Analysis:\n", "- **Multiple sensors**: Load different sensor indices\n", "- **Cross-sensor correlation**: Compare anomalies across sensors\n", "- **Temporal patterns**: Look for recurring anomalies\n", "- **Environmental correlation**: Match anomalies to external factors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Experimental cell - modify parameters and re-run analysis\n", "print(\"\\n🧪 EXPERIMENTAL ANALYSIS\")\n", "print(\"Modify the parameters below and re-run this cell to experiment:\")\n", "\n", "# Experimental parameters - modify these!\n", "exp_start_time = 7200  # Try different start time\n", "exp_duration = 300     # Try different duration\n", "exp_sample_rate = 25   # Try different sample rate\n", "exp_threshold_percentile = 75  # Try lower threshold\n", "\n", "print(f\"\\nExperimental parameters:\")\n", "print(f\"  Start time: {exp_start_time}s\")\n", "print(f\"  Duration: {exp_duration}s\")\n", "print(f\"  Sample rate: {exp_sample_rate} Hz\")\n", "print(f\"  Threshold percentile: {exp_threshold_percentile}\")\n", "\n", "# Check if parameters are valid\n", "if exp_start_time + exp_duration < len(data) / 250:\n", "    print(f\"\\n✓ Parameters valid, running experimental analysis...\")\n", "    \n", "    # Get parameters for experimental analysis\n", "    exp_params = suggest_analysis_parameters(exp_sample_rate, exp_duration, 'land')\n", "    \n", "    # Run experimental analysis\n", "    exp_results = analyze_spectral_anomalies(\n", "        data,\n", "        start_time_seconds=exp_start_time,\n", "        duration_seconds=exp_duration,\n", "        target_sample_rate=exp_sample_rate,\n", "        freq_bands=exp_params['freq_bands'],\n", "        context_length=exp_params['context_length'],\n", "        prediction_length=exp_params['prediction_length'],\n", "        stride=exp_params['stride']\n", "    )\n", "    \n", "    # Quick visualization\n", "    exp_fig, exp_axes = visualize_spectral_anomalies_enhanced(\n", "        exp_results, f\"Experimental_{data_type}_Analysis\", log_freq=True\n", "    )\n", "    plt.show()\n", "    \n", "    # Launch experimental interactive explorer\n", "    exp_fig_int, exp_axes_int, exp_anomaly_info = create_interactive_anomaly_explorer(\n", "        exp_results, f\"Experimental_{data_type}_Explorer\"\n", "    )\n", "    \n", "else:\n", "    print(f\"\\n❌ Invalid parameters - time segment exceeds available data\")\n", "    print(f\"   Available data: {len(data)/250:.1f} seconds\")\n", "    print(f\"   Requested: {exp_start_time + exp_duration} seconds\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 4}