{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Matplotlib Configuration Test\n", "\n", "This notebook tests matplotlib configuration and helps diagnose plotting issues."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic imports\n", "import sys\n", "import numpy as np\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "print(f\"Python version: {sys.version}\")\n", "print(f\"Matplotlib version: {matplotlib.__version__}\")\n", "print(f\"Numpy version: {np.__version__}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if we're in Jupyter\n", "try:\n", "    get_ipython()\n", "    print(\"✓ Running in Jupyter environment\")\n", "    \n", "    # Get IPython info\n", "    ipython = get_ipython()\n", "    print(f\"IPython version: {ipython.__class__.__name__}\")\n", "    \n", "except NameError:\n", "    print(\"❌ Not running in Jupyter environment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different matplotlib backends\n", "print(\"Available matplotlib backends:\")\n", "from matplotlib import backend_bases\n", "print(matplotlib.backend_bases.Backend.__subclasses__())\n", "\n", "print(f\"\\nCurrent backend: {plt.get_backend()}\")\n", "print(f\"Interactive mode: {matplotlib.is_interactive()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try to set inline backend explicitly\n", "%matplotlib inline\n", "print(f\"Backend after %matplotlib inline: {plt.get_backend()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test basic plotting\n", "print(\"Creating test plot...\")\n", "\n", "fig, ax = plt.subplots(figsize=(8, 4))\n", "x = np.linspace(0, 10, 100)\n", "y = np.sin(x)\n", "\n", "ax.plot(x, y, 'b-', linewidth=2, label='sin(x)')\n", "ax.set_xlabel('X')\n", "ax.set_ylabel('Y')\n", "ax.set_title('Test Plot - If you see this, mat<PERSON>lot<PERSON><PERSON> is working!')\n", "ax.legend()\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✓ Test plot created\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try widget backend if available\n", "try:\n", "    %matplotlib widget\n", "    print(f\"✓ Widget backend enabled: {plt.get_backend()}\")\n", "    \n", "    # Test interactive plot\n", "    fig, ax = plt.subplots(figsize=(8, 4))\n", "    x = np.linspace(0, 10, 100)\n", "    y = np.cos(x)\n", "    \n", "    ax.plot(x, y, 'r-', linewidth=2, label='cos(x)')\n", "    ax.set_xlabel('X')\n", "    ax.set_ylabel('Y')\n", "    ax.set_title('Interactive Test Plot - Try zooming/panning!')\n", "    ax.legend()\n", "    ax.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"✓ Interactive plot created\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Widget backend failed: {e}\")\n", "    print(\"To install widget support:\")\n", "    print(\"  pip install ipywidgets\")\n", "    print(\"  jupyter nbextension enable --py widgetsnbextension\")\n", "    print(\"  # For JupyterLab:\")\n", "    print(\"  pip install jupyterlab-widgets\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for common issues\n", "print(\"Diagnostic information:\")\n", "print(f\"  DISPLAY environment variable: {os.environ.get('DISPLAY', 'Not set')}\")\n", "print(f\"  Platform: {sys.platform}\")\n", "\n", "# Check if running in a container or remote environment\n", "import os\n", "if 'JUPYTER_SERVER_ROOT' in os.environ:\n", "    print(\"  Running in JupyterHub/remote environment\")\n", "elif 'COLAB_GPU' in os.environ:\n", "    print(\"  Running in Google Colab\")\n", "else:\n", "    print(\"  Running in local environment\")\n", "\n", "print(\"\\n📋 Summary:\")\n", "print(\"If you can see the test plots above, mat<PERSON><PERSON><PERSON><PERSON> is working correctly.\")\n", "print(\"If not, try the following:\")\n", "print(\"1. Restart the kernel and run all cells\")\n", "print(\"2. Install missing packages: pip install ipywidgets jupyterlab-widgets\")\n", "print(\"3. Enable extensions: jupyter nbextension enable --py widgetsnbextension\")\n", "print(\"4. For JupyterLab: jupyter labextension install @jupyter-widgets/jupyterlab-manager\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}