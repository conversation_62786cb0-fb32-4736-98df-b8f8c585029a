#!/usr/bin/env python3
"""
Single sensor windowed anomaly detection analysis.
Provides deep insight into how well Chronos anomaly detection works over time.
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import torch
from typing import List, Tuple, Dict
import warnings

# Add src directory to path
sys.path.append('src')

try:
    from chronos_loader import DASChronosDataset
    from chronos import ChronosPipeline
    print("✓ Successfully imported required modules")
except ImportError as e:
    print(f"✗ Failed to import modules: {e}")
    sys.exit(1)

def setup_plotting():
    """Set up matplotlib for clean plots."""
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (16, 12)
    plt.rcParams['font.size'] = 11
    plt.rcParams['axes.grid'] = True
    plt.rcParams['grid.alpha'] = 0.3
    plt.rcParams['legend.frameon'] = True
    plt.rcParams['legend.fancybox'] = True

class SingleSensorAnomalyAnalyzer:
    """Analyze anomaly detection for a single sensor over time."""
    
    def __init__(self, model_size: str = "tiny"):
        """Initialize with Chronos model."""
        self.model_size = model_size
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self._load_model()
    
    def _load_model(self):
        """Load pre-trained Chronos model."""
        try:
            model_name = f"amazon/chronos-t5-{self.model_size}"
            print(f"Loading {model_name}...")
            
            self.pipeline = ChronosPipeline.from_pretrained(
                model_name,
                device_map=self.device,
                torch_dtype=torch.bfloat16 if self.device.type == "cuda" else torch.float32,
            )
            print(f"✓ Chronos model loaded on {self.device}")
            
        except Exception as e:
            print(f"✗ Error loading model: {e}")
            self.pipeline = None
    
    def predict(self, context: np.ndarray, prediction_length: int = 32) -> np.ndarray:
        """Predict using Chronos."""
        if self.pipeline is None:
            # Fallback prediction
            return np.full(prediction_length, context[-1]) + np.random.normal(0, 0.01, prediction_length)
        
        try:
            context_tensor = torch.tensor(context, dtype=torch.float32).unsqueeze(0)
            forecast = self.pipeline.predict(
                context_tensor,
                prediction_length=prediction_length,
                num_samples=1
            )
            return forecast[0].cpu().numpy()
        except Exception as e:
            print(f"Warning: Prediction failed: {e}")
            return np.full(prediction_length, context[-1]) + np.random.normal(0, 0.01, prediction_length)
    
    def analyze_sensor_over_time(self, 
                               sensor_data: np.ndarray,
                               sensor_idx: int,
                               context_length: int = 128,
                               prediction_length: int = 32,
                               stride: int = 16,
                               max_windows: int = 500) -> Dict:
        """
        Analyze anomaly detection for one sensor over time.
        
        Parameters:
        -----------
        sensor_data : np.ndarray
            Time series data for one sensor [timesteps]
        sensor_idx : int
            Sensor index for labeling
        context_length : int
            Length of context window
        prediction_length : int
            Length of prediction window
        stride : int
            Stride between windows
        max_windows : int
            Maximum number of windows to analyze
            
        Returns:
        --------
        results : Dict
            Analysis results
        """
        print(f"Analyzing sensor {sensor_idx} over {len(sensor_data)} timesteps...")
        
        # Create sliding windows
        total_length = context_length + prediction_length
        n_windows = min(max_windows, (len(sensor_data) - total_length) // stride + 1)
        
        print(f"Creating {n_windows} sliding windows (stride={stride})...")
        
        # Storage for results
        window_starts = []
        predictions = []
        actuals = []
        mse_errors = []
        mae_errors = []
        max_errors = []
        
        for i in range(n_windows):
            start_idx = i * stride
            end_context = start_idx + context_length
            end_total = start_idx + total_length
            
            if end_total > len(sensor_data):
                break
            
            # Extract context and target
            context = sensor_data[start_idx:end_context]
            target = sensor_data[end_context:end_total]
            
            # Predict
            prediction = self.predict(context, prediction_length)
            
            # Compute errors
            mse_error = np.mean((prediction - target) ** 2)
            mae_error = np.mean(np.abs(prediction - target))
            max_error = np.max(np.abs(prediction - target))
            
            # Store results
            window_starts.append(start_idx)
            predictions.append(prediction)
            actuals.append(target)
            mse_errors.append(mse_error)
            mae_errors.append(mae_error)
            max_errors.append(max_error)
            
            if (i + 1) % 50 == 0:
                print(f"  Processed {i + 1}/{n_windows} windows")
        
        print(f"✓ Analyzed {len(window_starts)} windows")
        
        return {
            'sensor_idx': sensor_idx,
            'window_starts': np.array(window_starts),
            'predictions': predictions,
            'actuals': actuals,
            'mse_errors': np.array(mse_errors),
            'mae_errors': np.array(mae_errors),
            'max_errors': np.array(max_errors),
            'context_length': context_length,
            'prediction_length': prediction_length,
            'stride': stride,
            'sensor_data': sensor_data
        }
    
    def compute_anomaly_thresholds(self, results: Dict, percentiles: List[float] = [90, 95, 99]) -> Dict:
        """Compute anomaly thresholds at different percentiles."""
        thresholds = {}
        
        for p in percentiles:
            thresholds[f'mse_{p}'] = np.percentile(results['mse_errors'], p)
            thresholds[f'mae_{p}'] = np.percentile(results['mae_errors'], p)
            thresholds[f'max_{p}'] = np.percentile(results['max_errors'], p)
        
        return thresholds
    
    def visualize_temporal_anomalies(self, results: Dict, thresholds: Dict):
        """Create comprehensive temporal anomaly visualization."""
        print("Creating temporal anomaly visualization...")
        
        fig, axes = plt.subplots(4, 2, figsize=(18, 16))
        fig.suptitle(f'Temporal Anomaly Analysis - Sensor {results["sensor_idx"]}', fontsize=16)
        
        window_starts = results['window_starts']
        sensor_data = results['sensor_data']
        
        # Convert window starts to time (assuming 250 Hz sampling)
        time_axis = window_starts / 250.0  # Convert to seconds
        
        # 1. Raw sensor data with anomaly windows highlighted
        ax1 = axes[0, 0]
        full_time = np.arange(len(sensor_data)) / 250.0
        ax1.plot(full_time, sensor_data, 'b-', alpha=0.7, linewidth=0.5)
        
        # Highlight anomalous windows
        anomaly_mask_90 = results['mse_errors'] > thresholds['mse_90']
        anomaly_mask_95 = results['mse_errors'] > thresholds['mse_95']
        
        for i, is_anomaly in enumerate(anomaly_mask_90):
            if is_anomaly:
                start_time = window_starts[i] / 250.0
                end_time = (window_starts[i] + results['context_length'] + results['prediction_length']) / 250.0
                ax1.axvspan(start_time, end_time, alpha=0.3, color='orange')
        
        for i, is_anomaly in enumerate(anomaly_mask_95):
            if is_anomaly:
                start_time = window_starts[i] / 250.0
                end_time = (window_starts[i] + results['context_length'] + results['prediction_length']) / 250.0
                ax1.axvspan(start_time, end_time, alpha=0.5, color='red')
        
        ax1.set_title('Raw Sensor Data with Anomaly Windows')
        ax1.set_xlabel('Time (s)')
        ax1.set_ylabel('Strain Rate')
        
        # Add legend
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='orange', alpha=0.3, label='90th percentile anomalies'),
            Patch(facecolor='red', alpha=0.5, label='95th percentile anomalies')
        ]
        ax1.legend(handles=legend_elements, loc='upper right')
        
        # 2. MSE errors over time
        ax2 = axes[0, 1]
        ax2.plot(time_axis, results['mse_errors'], 'g-', linewidth=1)
        ax2.axhline(thresholds['mse_90'], color='orange', linestyle='--', label='90th percentile')
        ax2.axhline(thresholds['mse_95'], color='red', linestyle='--', label='95th percentile')
        ax2.set_title('MSE Prediction Errors Over Time')
        ax2.set_xlabel('Time (s)')
        ax2.set_ylabel('MSE Error')
        ax2.legend()
        ax2.set_yscale('log')
        
        # 3. Distribution of errors
        ax3 = axes[1, 0]
        ax3.hist(results['mse_errors'], bins=50, alpha=0.7, edgecolor='black')
        ax3.axvline(thresholds['mse_90'], color='orange', linestyle='--', label='90th percentile')
        ax3.axvline(thresholds['mse_95'], color='red', linestyle='--', label='95th percentile')
        ax3.set_title('Distribution of MSE Errors')
        ax3.set_xlabel('MSE Error')
        ax3.set_ylabel('Frequency')
        ax3.legend()
        ax3.set_yscale('log')
        
        # 4. Example normal prediction
        ax4 = axes[1, 1]
        normal_indices = np.where(~anomaly_mask_90)[0]
        if len(normal_indices) > 0:
            idx = normal_indices[len(normal_indices)//2]  # Middle normal example
            pred = results['predictions'][idx]
            actual = results['actuals'][idx]

            # Ensure arrays are proper length
            if hasattr(pred, '__len__') and hasattr(actual, '__len__'):
                min_len = min(len(pred), len(actual))
                pred_time = np.arange(min_len)

                ax4.plot(pred_time, actual[:min_len], 'b-', linewidth=2, label='Actual')
                ax4.plot(pred_time, pred[:min_len], 'g--', linewidth=2, label='Predicted')
                ax4.set_title(f'Normal Prediction (MSE: {results["mse_errors"][idx]:.4f})')
                ax4.set_xlabel('Prediction Steps')
                ax4.set_ylabel('Normalized Strain Rate')
                ax4.legend()

        # 5. Example anomalous prediction
        ax5 = axes[2, 0]
        anomaly_indices = np.where(anomaly_mask_95)[0]
        if len(anomaly_indices) > 0:
            idx = anomaly_indices[0]  # First high anomaly
            pred = results['predictions'][idx]
            actual = results['actuals'][idx]

            # Ensure arrays are proper length
            if hasattr(pred, '__len__') and hasattr(actual, '__len__'):
                min_len = min(len(pred), len(actual))
                pred_time = np.arange(min_len)

                ax5.plot(pred_time, actual[:min_len], 'b-', linewidth=2, label='Actual')
                ax5.plot(pred_time, pred[:min_len], 'r--', linewidth=2, label='Predicted')
                ax5.set_title(f'Anomalous Prediction (MSE: {results["mse_errors"][idx]:.4f})')
                ax5.set_xlabel('Prediction Steps')
                ax5.set_ylabel('Normalized Strain Rate')
                ax5.legend()
        
        # 6. Anomaly detection statistics
        ax6 = axes[2, 1]
        percentiles = [90, 95, 99]
        anomaly_counts = []
        
        for p in percentiles:
            threshold = thresholds[f'mse_{p}']
            count = np.sum(results['mse_errors'] > threshold)
            anomaly_counts.append(count)
        
        bars = ax6.bar(percentiles, anomaly_counts, alpha=0.7, color=['orange', 'red', 'darkred'])
        ax6.set_title('Anomaly Counts by Threshold')
        ax6.set_xlabel('Percentile Threshold')
        ax6.set_ylabel('Number of Anomalies')
        
        # Add count labels on bars
        for bar, count in zip(bars, anomaly_counts):
            height = bar.get_height()
            ax6.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{count}\n({100*count/len(results["mse_errors"]):.1f}%)',
                    ha='center', va='bottom')
        
        # 7. Error correlation analysis
        ax7 = axes[3, 0]
        scatter = ax7.scatter(results['mse_errors'], results['mae_errors'], 
                            c=time_axis, cmap='viridis', alpha=0.6, s=20)
        ax7.set_xlabel('MSE Error')
        ax7.set_ylabel('MAE Error')
        ax7.set_title('MSE vs MAE Errors (colored by time)')
        plt.colorbar(scatter, ax=ax7, label='Time (s)')
        
        # 8. Prediction quality over time
        ax8 = axes[3, 1]
        # Compute rolling statistics
        window_size = 20
        if len(results['mse_errors']) >= window_size:
            rolling_mean = np.convolve(results['mse_errors'], np.ones(window_size)/window_size, mode='valid')
            rolling_time = time_axis[window_size-1:]
            ax8.plot(rolling_time, rolling_mean, 'purple', linewidth=2)
            ax8.set_title(f'Rolling Mean MSE Error (window={window_size})')
            ax8.set_xlabel('Time (s)')
            ax8.set_ylabel('Rolling Mean MSE')
        
        plt.tight_layout()
        plt.savefig('single_sensor_temporal_anomaly_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✓ Temporal anomaly visualization saved")

def main():
    """Main analysis function."""
    print("=== Single Sensor Temporal Anomaly Analysis ===")
    
    setup_plotting()
    
    # Load dataset
    data_dir = Path("Data")
    h5_files = list(data_dir.rglob("*.h5"))
    
    if not h5_files:
        print("✗ No H5 files found")
        return False
    
    test_file = h5_files[0]
    print(f"Using file: {test_file}")
    
    try:
        # Create dataset
        dataset = DASChronosDataset(
            data_path=test_file,
            context_length=128,
            prediction_length=32,
            stride=16,
            normalize=True,
            normalization_method='z_score',
            apply_filtering=False
        )
        
        # Find an active sensor with good signal
        print("Finding active sensor with strong signal...")
        active_sensors = []
        for i in range(min(500, dataset.data.shape[0])):  # Check first 500 sensors
            sensor_data = dataset.data[i, :5000]  # First 5000 samples
            sensor_std = np.std(sensor_data)
            if sensor_std > 0.5:  # Lower threshold for normalized data
                active_sensors.append((i, sensor_std))
                if len(active_sensors) >= 20:  # Stop after finding 20 good sensors
                    break
        
        if not active_sensors:
            print("✗ No active sensors found")
            return False
        
        # Sort by signal strength and pick the strongest
        active_sensors.sort(key=lambda x: x[1], reverse=True)
        selected_sensor = active_sensors[0][0]
        
        print(f"Selected sensor {selected_sensor} (std: {active_sensors[0][1]:.3f})")
        
        # Extract longer time series for this sensor
        sensor_data = dataset.data[selected_sensor, :20000]  # ~80 seconds at 250 Hz
        
        # Initialize analyzer
        analyzer = SingleSensorAnomalyAnalyzer(model_size="tiny")
        
        # Analyze sensor over time
        results = analyzer.analyze_sensor_over_time(
            sensor_data=sensor_data,
            sensor_idx=selected_sensor,
            context_length=128,
            prediction_length=32,
            stride=16,
            max_windows=300
        )
        
        # Compute thresholds
        thresholds = analyzer.compute_anomaly_thresholds(results)
        
        # Print summary statistics
        print(f"\n=== Analysis Summary ===")
        print(f"Sensor: {selected_sensor}")
        print(f"Data length: {len(sensor_data)} samples ({len(sensor_data)/250:.1f} seconds)")
        print(f"Windows analyzed: {len(results['window_starts'])}")
        print(f"Mean MSE error: {np.mean(results['mse_errors']):.6f}")
        print(f"Std MSE error: {np.std(results['mse_errors']):.6f}")
        print(f"90th percentile threshold: {thresholds['mse_90']:.6f}")
        print(f"95th percentile threshold: {thresholds['mse_95']:.6f}")
        print(f"Anomalies (90th): {np.sum(results['mse_errors'] > thresholds['mse_90'])}")
        print(f"Anomalies (95th): {np.sum(results['mse_errors'] > thresholds['mse_95'])}")
        
        # Create visualization
        analyzer.visualize_temporal_anomalies(results, thresholds)
        
        print(f"\n=== Analysis Complete ===")
        return True
        
    except Exception as e:
        print(f"✗ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
