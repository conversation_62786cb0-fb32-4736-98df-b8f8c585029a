#!/usr/bin/env python3
"""
Validation script for the chronos_loader.py module.
Tests the DASChronosDataset with actual H5 files from the Data directory.
"""

import sys
import os
import numpy as np
import h5py
from pathlib import Path

# Add src directory to path to import chronos_loader
sys.path.append('src')

try:
    from chronos_loader import DASChronosDataset, DASChronosDataLoader, create_chronos_dataloaders
    print("✓ Successfully imported chronos_loader modules")
except ImportError as e:
    print(f"✗ Failed to import chronos_loader: {e}")
    sys.exit(1)

def inspect_h5_file(file_path):
    """Inspect the structure of an H5 file to understand its contents."""
    print(f"\n=== Inspecting H5 file: {file_path} ===")
    
    try:
        with h5py.File(file_path, 'r') as f:
            print("File structure:")
            
            def print_structure(name, obj):
                indent = "  " * name.count('/')
                if isinstance(obj, h5py.Dataset):
                    print(f"{indent}{name}: Dataset {obj.shape} {obj.dtype}")
                    # Print some basic stats if it's numeric
                    if obj.dtype.kind in ['f', 'i']:
                        data_sample = obj[...]
                        if data_sample.size > 0:
                            print(f"{indent}  └─ Range: [{np.min(data_sample):.6f}, {np.max(data_sample):.6f}]")
                            print(f"{indent}  └─ Mean: {np.mean(data_sample):.6f}, Std: {np.std(data_sample):.6f}")
                else:
                    print(f"{indent}{name}: Group")
            
            f.visititems(print_structure)
            
    except Exception as e:
        print(f"✗ Error inspecting file: {e}")
        return False
    
    return True

def test_dataset_creation(file_path):
    """Test creating a DASChronosDataset with the H5 file."""
    print(f"\n=== Testing Dataset Creation ===")
    
    try:
        # Test with default parameters
        dataset = DASChronosDataset(
            data_path=file_path,
            context_length=128,  # Smaller for testing
            prediction_length=32,
            stride=16,
            normalize=True,
            normalization_method='z_score'
        )
        
        print(f"✓ Dataset created successfully")
        print(f"  └─ Number of samples: {len(dataset)}")
        print(f"  └─ Data shape: {dataset.data.shape}")
        print(f"  └─ Normalization stats available for {len(dataset.normalization_stats)} sensors")
        
        return dataset
        
    except Exception as e:
        print(f"✗ Failed to create dataset: {e}")
        return None

def test_data_loading(dataset):
    """Test loading individual samples from the dataset."""
    print(f"\n=== Testing Data Loading ===")
    
    if dataset is None:
        print("✗ No dataset to test")
        return False
    
    try:
        # Test getting a few samples
        for i in range(min(3, len(dataset))):
            sample = dataset[i]
            
            print(f"Sample {i}:")
            print(f"  └─ past_values shape: {sample['past_values'].shape}")
            print(f"  └─ future_values shape: {sample['future_values'].shape}")
            print(f"  └─ sensor_idx: {sample['sensor_idx'].item()}")
            
            # Check for NaN/inf values
            past_finite = torch.isfinite(sample['past_values']).all()
            future_finite = torch.isfinite(sample['future_values']).all()
            
            print(f"  └─ past_values finite: {past_finite}")
            print(f"  └─ future_values finite: {future_finite}")
            
            if not (past_finite and future_finite):
                print("  ⚠️  Warning: Non-finite values detected!")
        
        print("✓ Data loading test passed")
        return True
        
    except Exception as e:
        print(f"✗ Data loading test failed: {e}")
        return False

def test_dataloader_creation(dataset):
    """Test creating PyTorch DataLoaders."""
    print(f"\n=== Testing DataLoader Creation ===")
    
    if dataset is None:
        print("✗ No dataset to test")
        return False
    
    try:
        # Test creating train and validation loaders
        train_loader = DASChronosDataLoader.create_train_loader(
            dataset, 
            batch_size=4,  # Small batch for testing
            num_workers=0  # Avoid multiprocessing issues
        )
        
        val_loader = DASChronosDataLoader.create_val_loader(
            dataset,
            batch_size=8,
            num_workers=0
        )
        
        print(f"✓ DataLoaders created successfully")
        print(f"  └─ Train loader batches: {len(train_loader)}")
        print(f"  └─ Val loader batches: {len(val_loader)}")
        
        # Test loading a batch
        for batch_idx, batch in enumerate(train_loader):
            print(f"Batch {batch_idx}:")
            print(f"  └─ past_values shape: {batch['past_values'].shape}")
            print(f"  └─ future_values shape: {batch['future_values'].shape}")
            print(f"  └─ sensor_idx shape: {batch['sensor_idx'].shape}")
            
            # Only test first batch
            break
        
        print("✓ DataLoader test passed")
        return True
        
    except Exception as e:
        print(f"✗ DataLoader test failed: {e}")
        return False

def test_normalization_denormalization(dataset):
    """Test normalization and denormalization functionality."""
    print(f"\n=== Testing Normalization/Denormalization ===")
    
    if dataset is None:
        print("✗ No dataset to test")
        return False
    
    try:
        # Get a sample
        sample = dataset[0]
        sensor_idx = sample['sensor_idx'].item()
        past_values = sample['past_values']
        
        # Test denormalization
        denormalized = dataset.denormalize(past_values, sensor_idx)
        
        print(f"✓ Denormalization test passed")
        print(f"  └─ Original range: [{past_values.min():.6f}, {past_values.max():.6f}]")
        print(f"  └─ Denormalized range: [{denormalized.min():.6f}, {denormalized.max():.6f}]")
        
        return True
        
    except Exception as e:
        print(f"✗ Normalization test failed: {e}")
        return False

def main():
    """Main validation function."""
    print("=== Chronos Loader Validation ===")
    
    # Find the first available H5 file
    data_dir = Path("Data")
    h5_files = list(data_dir.rglob("*.h5"))
    
    if not h5_files:
        print("✗ No H5 files found in Data directory")
        return False
    
    # Use the first H5 file for testing
    test_file = h5_files[0]
    print(f"Using test file: {test_file}")
    
    # Run validation tests
    success = True
    
    # 1. Inspect H5 file structure
    success &= inspect_h5_file(test_file)
    
    # 2. Test dataset creation
    dataset = test_dataset_creation(test_file)
    success &= (dataset is not None)
    
    # 3. Test data loading
    success &= test_data_loading(dataset)
    
    # 4. Test DataLoader creation
    success &= test_dataloader_creation(dataset)
    
    # 5. Test normalization/denormalization
    success &= test_normalization_denormalization(dataset)
    
    # Summary
    print(f"\n=== Validation Summary ===")
    if success:
        print("✓ All tests passed! The chronos_loader is working correctly.")
    else:
        print("✗ Some tests failed. Please check the output above for details.")
    
    return success

if __name__ == "__main__":
    # Import torch here to avoid issues if not installed
    try:
        import torch
        print("✓ PyTorch available")
    except ImportError:
        print("✗ PyTorch not available - some tests may fail")
    
    success = main()
    sys.exit(0 if success else 1)
