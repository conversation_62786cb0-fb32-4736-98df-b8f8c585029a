{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Valencia DAS Interactive Anomaly Detection\n", "\n", "This notebook provides an interactive environment for exploring anomaly detection in Valencia DAS data using Chronos T5 time series forecasting.\n", "\n", "## Features:\n", "- **Real Valencia DAS data loading**\n", "- **Chronos T5 time series prediction**\n", "- **Spectral anomaly detection**\n", "- **Interactive anomaly explorer with zoom/pan**\n", "- **Frequency band analysis**\n", "- **Whitened spectrograms for better anomaly visibility**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports\n", "\n", "**Note**: For interactive plots, you may need to install additional packages:\n", "```bash\n", "# For widget backend (recommended)\n", "pip install ipywidgets\n", "jupyter nbextension enable --py widgetsnbextension\n", "\n", "# For JupyterLab\n", "pip install jupyterlab-widgets\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import h5py\n", "from pathlib import Path\n", "from scipy.signal import spectrogram, resample\n", "import torch\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure matplotlib for Jupyter notebooks\n", "import matplotlib\n", "matplotlib.use('Agg')  # Use non-interactive backend first\n", "\n", "# Then try to enable interactive plotting\n", "try:\n", "    # Check if we're in a Jupyter environment\n", "    get_ipython()\n", "    \n", "    # Try widget backend first (best for <PERSON><PERSON><PERSON><PERSON>ab)\n", "    try:\n", "        %matplotlib widget\n", "        print('✓ Using widget backend for interactive plots')\n", "        print('  Install: pip install ipywidgets jupyterlab-widgets')\n", "    except Exception as e:\n", "        try:\n", "            # Try notebook backend (for classic <PERSON><PERSON><PERSON>)\n", "            %matplotlib notebook\n", "            print('✓ Using notebook backend for interactive plots')\n", "        except Exception as e2:\n", "            # Fall back to inline\n", "            %matplotlib inline\n", "            print('⚠️  Using inline backend - plots will not be interactive')\n", "            print('  To enable interactive plots:')\n", "            print('    pip install ipywidgets')\n", "            print('    jupyter nbextension enable --py widgetsnbextension')\n", "            print('    # For JupyterLab: pip install jupyterlab-widgets')\n", "            \n", "except NameError:\n", "    # Not in Jupyter, use inline\n", "    %matplotlib inline\n", "    print('⚠️  Not in Jupyter environment, using inline backend')\n", "\n", "# Import our custom modules\n", "from enhanced_spectral_visualization import (\n", "    visualize_spectral_anomalies_enhanced, \n", "    get_appropriate_freq_bands, \n", "    suggest_analysis_parameters,\n", "    compare_frequency_scales,\n", "    compare_whitened_spectrograms,\n", "    create_interactive_anomaly_explorer\n", ")\n", "\n", "print(\"✓ All imports successful\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> Chronos Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try to load Chronos model\n", "try:\n", "    from chronos import ChronosPipeline\n", "    CHRONOS_AVAILABLE = True\n", "    print(\"✓ ChronosPipeline imported successfully\")\n", "except ImportError as e:\n", "    print(f\"❌ Chronos not available: {e}\")\n", "    print(\"To install: pip install chronos-forecasting\")\n", "    CHRONOS_AVAILABLE = False\n", "\n", "# Load Chronos model if available\n", "pipeline = None\n", "if CHRONOS_AVAILABLE:\n", "    try:\n", "        print(\"Loading Chronos model...\")\n", "        device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "        pipeline = ChronosPipeline.from_pretrained(\n", "            \"amazon/chronos-t5-tiny\",\n", "            device_map=device,\n", "            torch_dtype=torch.bfloat16 if device.type == \"cuda\" else torch.float32,\n", "        )\n", "\n", "        print(f\"✓ Chronos model loaded on {device}\")\n", "        print(\"✓ USING REAL CHRONOS MODEL\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed to load Chronos: {e}\")\n", "        print(\"⚠️  Will use mock predictions instead\")\n", "        pipeline = None\n", "else:\n", "    print(\"⚠️  Using mock predictions (Chronos not available)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Define Prediction Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict_chronos(context, prediction_length=32):\n", "    \"\"\"Make prediction using Chronos or enhanced mock.\"\"\"\n", "    if pipeline is None:\n", "        # Enhanced mock prediction\n", "        return mock_prediction(context, prediction_length)\n", "    \n", "    try:\n", "        # Use real Chronos\n", "        context_tensor = torch.tensor(context, dtype=torch.float32).unsqueeze(0)\n", "        forecast = pipeline.predict(\n", "            context_tensor,\n", "            prediction_length=prediction_length,\n", "            num_samples=1\n", "        )\n", "        return forecast[0].cpu().numpy().flatten()\n", "    except Exception as e:\n", "        print(f\"❌ Chronos prediction failed: {e}\")\n", "        return mock_prediction(context, prediction_length)\n", "\n", "def mock_prediction(context, prediction_length):\n", "    \"\"\"Conservative mock prediction that will fail on anomalies.\"\"\"\n", "    if len(context) < 4:\n", "        base_value = context[-1] if len(context) > 0 else 0\n", "        prediction = np.full(prediction_length, base_value)\n", "        prediction += np.random.normal(0, 0.001, prediction_length)\n", "        return prediction\n", "    \n", "    # Use only stable part of context (ignore recent changes)\n", "    stable_context = context[:-max(1, len(context)//4)]\n", "    if len(stable_context) < 2:\n", "        stable_context = context[:2]\n", "    \n", "    # Conservative baseline from stable period\n", "    baseline = np.mean(stable_context)\n", "    \n", "    # Very small trend from stable period\n", "    if len(stable_context) >= 2:\n", "        mid_point = len(stable_context) // 2\n", "        early_mean = np.mean(stable_context[:mid_point]) if mid_point > 0 else stable_context[0]\n", "        late_mean = np.mean(stable_context[mid_point:]) if mid_point < len(stable_context) else stable_context[-1]\n", "        trend = (late_mean - early_mean) / len(stable_context) * 0.1  # Heavily dampened\n", "    else:\n", "        trend = 0\n", "    \n", "    # Generate conservative predictions\n", "    prediction = []\n", "    for i in range(prediction_length):\n", "        decay_factor = 0.8 ** i\n", "        pred_value = baseline + trend * (i + 1) * decay_factor\n", "        prediction.append(pred_value)\n", "    \n", "    prediction = np.array(prediction)\n", "    noise_level = np.std(stable_context) * 0.02 if len(stable_context) > 1 else 0.001\n", "    prediction += np.random.normal(0, noise_level, prediction_length)\n", "    \n", "    return prediction\n", "\n", "print(\"✓ Prediction functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Loading Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_valencia_data(data_dir=\"Data\", max_files=6, target_sensor=None):\n", "    \"\"\"Load Valencia DAS data from H5 files.\"\"\"\n", "    data_dir = Path(data_dir)\n", "    h5_files = sorted(list(data_dir.rglob(\"*.h5\")))\n", "    print(f\"Found {len(h5_files)} H5 files\")\n", "    \n", "    if len(h5_files) == 0:\n", "        raise FileNotFoundError(\"No H5 files found\")\n", "    \n", "    # Determine target sensor\n", "    if target_sensor is None:\n", "        with h5py.File(h5_files[0], 'r') as f:\n", "            das_name = list(f.keys())[0]\n", "            sr_dataset = f[f\"{das_name}/Source1/Zone1/SR_Valencia\"]\n", "            n_sensors = sr_dataset.shape[2]\n", "        target_sensor = n_sensors // 2\n", "        print(f\"Using middle sensor: {target_sensor} (out of {n_sensors} sensors)\")\n", "    \n", "    # Load data from files\n", "    all_data = []\n", "    files_to_load = min(max_files, len(h5_files))\n", "    print(f\"Loading sensor {target_sensor} from {files_to_load} files...\")\n", "    \n", "    for i, file_path in enumerate(h5_files[:files_to_load]):\n", "        try:\n", "            with h5py.File(file_path, 'r') as f:\n", "                das_name = list(f.keys())[0]\n", "                sr_dataset = f[f\"{das_name}/Source1/Zone1/SR_Valencia\"]\n", "                dims = sr_dataset.shape\n", "                nb_block = dims[0]\n", "                sampling_frequency = 250\n", "                \n", "                sensor_data_blocks = []\n", "                for tt in range(nb_block):\n", "                    block_data = sr_dataset[tt, :sampling_frequency, target_sensor]\n", "                    sensor_data_blocks.append(block_data)\n", "                \n", "                sensor_data = np.concatenate(sensor_data_blocks)\n", "                all_data.append(sensor_data.astype(np.float32))\n", "                print(f\"  Loaded {file_path.name}: {len(sensor_data)} samples\")\n", "        except Exception as e:\n", "            print(f\"  Warning: Failed to load {file_path.name}: {e}\")\n", "            continue\n", "    \n", "    if not all_data:\n", "        raise ValueError(\"No data could be loaded\")\n", "    \n", "    # Concatenate all data\n", "    full_data = np.concatenate(all_data)\n", "    total_duration = len(full_data) / 250\n", "    \n", "    print(f\"✓ Loaded {len(all_data)} files successfully\")\n", "    print(f\"✓ Total data: {len(full_data)} samples ({total_duration/3600:.1f} hours)\")\n", "    print(f\"✓ Sensor {target_sensor} ready for analysis\")\n", "    \n", "    return full_data, target_sensor\n", "\n", "def create_synthetic_data():\n", "    \"\"\"Create synthetic DAS data with known anomalies for testing.\"\"\"\n", "    print(\"Creating synthetic Valencia DAS data...\")\n", "    \n", "    duration_hours = 2\n", "    sample_rate = 250\n", "    n_samples = int(duration_hours * 3600 * sample_rate)\n", "    \n", "    t = np.arange(n_samples) / sample_rate\n", "    \n", "    # Base signals\n", "    traffic = 0.1 * np.sin(2 * np.pi * 5 * t) + 0.05 * np.sin(2 * np.pi * 12 * t)\n", "    rumble = 0.2 * np.sin(2 * np.pi * 0.5 * t) + 0.1 * np.sin(2 * np.pi * 1.5 * t)\n", "    \n", "    # Add strong anomalies\n", "    anomaly_events = [\n", "        {'time': 1800, 'duration': 45, 'freq': 8, 'amplitude': 2.0, 'type': 'high_freq_burst'},\n", "        {'time': 3600, 'duration': 60, 'freq': 2, 'amplitude': 3.0, 'type': 'low_freq_surge'}, \n", "        {'time': 5400, 'duration': 30, 'freq': 12, 'amplitude': 1.5, 'type': 'machinery_spike'},\n", "    ]\n", "    \n", "    for event in anomaly_events:\n", "        start_idx = int(event['time'] * sample_rate)\n", "        end_idx = start_idx + int(event['duration'] * sample_rate)\n", "        if end_idx < n_samples:\n", "            if event['type'] == 'high_freq_burst':\n", "                anomaly_signal = event['amplitude'] * np.sin(2 * np.pi * event['freq'] * t[start_idx:end_idx])\n", "            elif event['type'] == 'low_freq_surge':\n", "                ramp = np.linspace(0, 1, end_idx - start_idx)\n", "                anomaly_signal = event['amplitude'] * ramp * np.sin(2 * np.pi * event['freq'] * t[start_idx:end_idx])\n", "            else:  # machinery_spike\n", "                pulse_pattern = np.where(np.sin(2 * np.pi * 0.5 * t[start_idx:end_idx]) > 0, 1, 0)\n", "                anomaly_signal = event['amplitude'] * pulse_pattern * np.sin(2 * np.pi * event['freq'] * t[start_idx:end_idx])\n", "            \n", "            traffic[start_idx:end_idx] += anomaly_signal\n", "    \n", "    synthetic_data = traffic + rumble + np.random.normal(0, 0.02, n_samples)\n", "    \n", "    print(f\"✓ Created {duration_hours} hours of synthetic data\")\n", "    print(f\"✓ Anomaly events:\")\n", "    for event in anomaly_events:\n", "        print(f\"    {event['time']}s: {event['type']} ({event['duration']}s, {event['freq']} Hz)\")\n", "    \n", "    return synthetic_data, 1488\n", "\n", "print(\"✓ Data loading functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try to load real data first, fall back to synthetic\n", "try:\n", "    print(\"Attempting to load REAL Valencia DAS data...\")\n", "    data, sensor_id = load_valencia_data(max_files=6, target_sensor=None)\n", "    data_type = \"REAL\"\n", "    print(f\"✓ Using REAL data from sensor {sensor_id}\")\n", "except Exception as e:\n", "    print(f\"Failed to load real data: {e}\")\n", "    print(\"Falling back to synthetic data...\")\n", "    data, sensor_id = create_synthetic_data()\n", "    data_type = \"SYNTHETIC\"\n", "    print(f\"✓ Using SYNTHETIC data\")\n", "\n", "print(f\"\\nData loaded: {len(data)} samples ({len(data)/250/3600:.2f} hours)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Configure Analysis Parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Choose analysis parameters\n", "sample_rate = 30  # Hz - good compromise for DAS analysis\n", "analysis_duration = 600  # 10 minutes\n", "\n", "# Choose time segment\n", "total_duration = len(data) / 250\n", "print(f\"Total data duration: {total_duration/3600:.1f} hours\")\n", "\n", "if data_type == \"REAL\":\n", "    # For real data, try different interesting time periods\n", "    analysis_options = [\n", "        {\"start\": 3600, \"description\": \"1 hour mark (potential traffic changes)\"},\n", "        {\"start\": 7200, \"description\": \"2 hour mark (potential activity changes)\"},  \n", "        {\"start\": 14400, \"description\": \"4 hour mark (potential environmental changes)\"},\n", "        {\"start\": int(total_duration * 0.3), \"description\": \"30% through dataset\"},\n", "        {\"start\": int(total_duration * 0.7), \"description\": \"70% through dataset\"}\n", "    ]\n", "    \n", "    # Choose first valid option\n", "    analysis_start = None\n", "    for option in analysis_options:\n", "        if option[\"start\"] + analysis_duration < total_duration:\n", "            analysis_start = option[\"start\"]\n", "            description = option[\"description\"]\n", "            break\n", "    \n", "    if analysis_start is None:\n", "        analysis_start = int(total_duration * 0.5)\n", "        description = \"Middle of dataset (fallback)\"\n", "else:\n", "    # For synthetic data, choose segment with known anomaly\n", "    analysis_start = 3500  # Just before 3600s anomaly\n", "    description = \"Around 3600s synthetic anomaly\"\n", "\n", "print(f\"\\nAnalysis configuration:\")\n", "print(f\"  Data type: {data_type}\")\n", "print(f\"  Time segment: {analysis_start}s to {analysis_start + analysis_duration}s\")\n", "print(f\"  Description: {description}\")\n", "print(f\"  Sample rate: {sample_rate} Hz\")\n", "print(f\"  Duration: {analysis_duration} seconds\")\n", "\n", "# Get suggested parameters\n", "suggested_params = suggest_analysis_parameters(sample_rate, analysis_duration, 'land')\n", "print(f\"\\nSuggested analysis parameters:\")\n", "print(f\"  Context length: {suggested_params['context_length']}\")\n", "print(f\"  Prediction length: {suggested_params['prediction_length']}\")\n", "print(f\"  Stride: {suggested_params['stride']}\")\n", "print(f\"  Frequency bands: {list(suggested_params['freq_bands'].keys())}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}