# STEAD Dataset Loader with Event ID Sampling

This repository provides a comprehensive Python implementation for loading and using the STEAD (STanford EArthquake Dataset) from SEISBENCH with advanced sampling capabilities based on event IDs.

## Overview

The STEAD dataset is a global dataset of seismic signals containing:
- ~1.2 million time series of local seismic signals
- Both earthquake and non-earthquake events
- ~100,000 noise examples
- 450,000 earthquakes
- Rich metadata including magnitudes, depths, locations, and timing

## Features

- **Event ID-based sampling**: Sample data based on specific earthquake events
- **Balanced sampling**: Ensure equal representation of different events in batches
- **Flexible filtering**: Filter by magnitude, depth, event type, or custom criteria
- **PyTorch integration**: Native PyTorch Dataset and DataLoader support
- **Preprocessing**: Built-in normalization and resampling capabilities
- **Memory efficient**: Configurable caching strategies for large datasets

## Installation

### Prerequisites

```bash
pip install torch torchvision torchaudio
pip install seisbench
pip install numpy pandas matplotlib seaborn
```

### SEISBENCH Installation

```bash
# Install SEISBENCH
pip install seisbench

# Or from source
git clone https://github.com/seisbench/seisbench.git
cd seisbench
pip install .
```

## Quick Start

### Basic Usage

```python
from stead_dataloader import STEADDataset, create_stead_dataloader

# Load STEAD training dataset
dataset = STEADDataset(
    split='train',
    component_order='ZNE',  # Z, North, East components
    sampling_rate=100,      # Resample to 100 Hz
    normalize=True          # Apply z-score normalization
)

print(f"Dataset size: {len(dataset)}")
print(f"Unique events: {len(dataset.get_event_ids())}")

# Get a sample
sample = dataset[0]
waveform = sample['waveform']  # Shape: [3, n_samples]
event_id = sample['event_id']
metadata = sample['metadata']

print(f"Waveform shape: {waveform.shape}")
print(f"Event ID: {event_id}")
```

### Create DataLoader

```python
# Standard DataLoader
dataloader = create_stead_dataloader(
    split='train',
    batch_size=32,
    shuffle=True,
    num_workers=4
)

# Balanced sampling across events
balanced_dataloader = create_stead_dataloader(
    split='train',
    batch_size=32,
    balanced_sampling=True,  # Ensures diverse events per batch
    magnitude_range=(2.0, 6.0),
    max_samples_per_event=10
)

# Iterate through batches
for batch in dataloader:
    waveforms = batch['waveform']    # [batch_size, 3, n_samples]
    event_ids = batch['event_id']    # List of event IDs
    metadata = batch['metadata']     # List of metadata dicts
    break
```

## Advanced Usage

### Event ID Filtering

```python
# Load dataset and get available events
full_dataset = STEADDataset(split='train')
all_events = full_dataset.get_event_ids()

# Select specific events
selected_events = all_events[:10]  # First 10 events

# Create filtered dataset
filtered_dataset = STEADDataset(
    split='train',
    event_ids=selected_events,
    normalize=True
)
```

### Magnitude-based Filtering

```python
# Filter by magnitude range
dataset = STEADDataset(
    split='train',
    magnitude_range=(3.0, 5.0),  # Only magnitude 3-5 events
    max_samples_per_event=20,    # Limit samples per event
    normalize=True
)
```

### Custom Sampling Strategy

```python
from stead_dataloader import EventBalancedSampler

# Create custom sampler
dataset = STEADDataset(split='train', normalize=True)
sampler = EventBalancedSampler(dataset, batch_size=16, shuffle=True)

# Use with DataLoader
from torch.utils.data import DataLoader
dataloader = DataLoader(dataset, batch_sampler=sampler, num_workers=4)
```

## Dataset Splits

STEAD provides predefined train/dev/test splits:

```python
# Training set (60% of data)
train_dataset = STEADDataset(split='train')

# Development/validation set (10% of data)
dev_dataset = STEADDataset(split='dev')

# Test set (30% of data)
test_dataset = STEADDataset(split='test')

# Full dataset (all data)
full_dataset = STEADDataset(split=None)
```

## Configuration Options

### STEADDataset Parameters

- `split`: Dataset split ('train', 'dev', 'test', or None)
- `component_order`: Seismic component order ('ZNE', 'NEZ', etc.)
- `sampling_rate`: Target sampling rate in Hz (None for original)
- `normalize`: Apply z-score normalization (True/False)
- `event_ids`: List of specific event IDs to include
- `magnitude_range`: Tuple of (min_mag, max_mag) for filtering
- `max_samples_per_event`: Maximum samples per event ID
- `cache`: Caching strategy ('trace', 'full', or None)

### DataLoader Parameters

- `batch_size`: Number of samples per batch
- `shuffle`: Shuffle data (ignored if balanced_sampling=True)
- `balanced_sampling`: Use event-balanced sampling
- `num_workers`: Number of worker processes for data loading

## Examples

Run the provided examples to see the dataloader in action:

```bash
# Run basic examples
python stead_example.py

# This will demonstrate:
# 1. Basic dataset loading
# 2. Event ID filtering
# 3. Magnitude filtering
# 4. Balanced sampling
# 5. Dataset analysis
# 6. Waveform visualization
```

## Data Format

Each sample returns a dictionary with:

```python
{
    'waveform': torch.Tensor,     # Shape: [n_components, n_samples]
    'event_id': str,              # Unique event identifier
    'metadata': dict,             # Full metadata from STEAD
    'trace_idx': int              # Original trace index
}
```

### Metadata Fields

Common metadata fields include:
- `source_magnitude`: Earthquake magnitude
- `source_depth_km`: Earthquake depth in kilometers
- `source_latitude_deg`: Earthquake latitude
- `source_longitude_deg`: Earthquake longitude
- `trace_start_time`: Trace start time
- `station_*`: Station information
- `source_event_category`: Event category

## Performance Tips

1. **Use caching**: Set `cache='trace'` for small datasets or `cache='full'` for large ones
2. **Preload data**: Call `dataset.preload_waveforms()` before training
3. **Optimize workers**: Set `num_workers=4-8` for faster data loading
4. **Filter early**: Apply magnitude/event filters to reduce dataset size
5. **Batch size**: Use larger batch sizes (32-128) for better GPU utilization

## Memory Considerations

- STEAD dataset is ~70GB (waveforms) + 200MB (metadata)
- Use `max_samples_per_event` to limit memory usage
- Consider magnitude filtering to reduce dataset size
- Use `cache='trace'` for memory-efficient loading

## Troubleshooting

### Common Issues

1. **Dataset not found**: SEISBENCH will download STEAD automatically on first use
2. **Memory errors**: Reduce `max_samples_per_event` or use magnitude filtering
3. **Slow loading**: Increase `num_workers` or use caching
4. **Event ID not found**: Check available event IDs with `dataset.get_event_ids()`

### Network Issues

If download fails due to network restrictions:
```python
import seisbench
seisbench.use_backup_repository()  # Use backup server
```

## Citation

If you use this code or the STEAD dataset, please cite:

```bibtex
@article{mousavi2019stanford,
  title={STanford EArthquake Dataset (STEAD): A Global Data Set of Seismic Signals for AI},
  author={Mousavi, S Mostafa and Sheng, Yixiao and Zhu, Weiqiang and Beroza, Gregory C},
  journal={IEEE Access},
  volume={7},
  pages={179464--179476},
  year={2019},
  publisher={IEEE}
}

@article{woollam2022seisbench,
  title={SeisBench—A Toolbox for Machine Learning in Seismology},
  author={Woollam, Jack and M{\"u}nchmeyer, Jannes and Tilmann, Frederik and Rietbrock, Andreas and Lange, Dietrich and Bornstein, Thomas and Diehl, Tobias and Giunchi, Carlo and Haslinger, Florian and Jozinovi{\'c}, Dario and others},
  journal={Seismological Research Letters},
  volume={93},
  number={3},
  pages={1695--1709},
  year={2022},
  publisher={Seismological Society of America}
}
```

## License

This code is provided under the MIT License. The STEAD dataset has its own licensing terms - please check the SEISBENCH documentation for details.

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.
