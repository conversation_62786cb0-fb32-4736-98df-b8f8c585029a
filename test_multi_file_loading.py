#!/usr/bin/env python3
"""
Test script to verify multi-file loading functionality.
"""

import sys
import numpy as np
from pathlib import Path

# Add src directory to path
sys.path.append('src')

from chronos_loader import DASChronosDataset

def test_multi_file_loading():
    """Test loading and concatenating multiple DAS files."""
    print("=== Testing Multi-File Loading ===")
    
    # Find H5 files
    data_dir = Path("Data")
    h5_files = sorted(list(data_dir.rglob("*.h5")))
    
    print(f"Found {len(h5_files)} H5 files:")
    for i, file in enumerate(h5_files[:10]):
        print(f"  {i+1}. {file.name}")
    
    if len(h5_files) < 2:
        print("Need at least 2 files for multi-file testing")
        return False
    
    # Load individual files to check compatibility
    print(f"\nLoading individual files to check compatibility...")
    file_data = []
    file_shapes = []
    
    for i, file_path in enumerate(h5_files[:6]):  # Test first 6 files
        try:
            print(f"  Loading {file_path.name}...")
            dataset = DASChronosDataset(
                data_path=file_path,
                context_length=128,
                prediction_length=32,
                stride=16,
                normalize=False,
                apply_filtering=False
            )
            
            file_data.append(dataset.data)
            file_shapes.append(dataset.data.shape)
            duration = dataset.data.shape[1] / 250
            print(f"    Shape: {dataset.data.shape}, Duration: {duration:.1f}s")
            
        except Exception as e:
            print(f"    Failed to load {file_path.name}: {e}")
            continue
    
    if len(file_data) < 2:
        print("Could not load enough files for concatenation test")
        return False
    
    # Check if files are compatible for concatenation
    print(f"\nChecking file compatibility...")
    first_shape = file_shapes[0]
    compatible = True
    
    for i, shape in enumerate(file_shapes[1:], 1):
        if shape[0] != first_shape[0]:  # Number of sensors must match
            print(f"  File {i+1} has {shape[0]} sensors vs {first_shape[0]} in first file - INCOMPATIBLE")
            compatible = False
        else:
            print(f"  File {i+1}: {shape[0]} sensors, {shape[1]} timesteps - COMPATIBLE")
    
    if not compatible:
        print("Files are not compatible for concatenation")
        return False
    
    # Test concatenation
    print(f"\nTesting concatenation of {len(file_data)} files...")
    try:
        concatenated = np.concatenate(file_data, axis=1)
        total_duration = concatenated.shape[1] / 250
        
        print(f"✓ Concatenation successful!")
        print(f"  Final shape: {concatenated.shape}")
        print(f"  Total duration: {total_duration:.1f} seconds ({total_duration/60:.1f} minutes)")
        
        # Test sensor selection and normalization
        print(f"\nTesting sensor selection and normalization...")
        
        # Find active sensor
        active_sensor = None
        for i in range(min(100, concatenated.shape[0])):
            sensor_data = concatenated[i, :5000]  # First 20 seconds
            if np.std(sensor_data) > 1.0:
                active_sensor = i
                break
        
        if active_sensor is None:
            print("No active sensor found")
            return False
        
        print(f"Found active sensor: {active_sensor}")
        
        # Test normalization
        raw_data = concatenated[active_sensor, :]
        normalized_data = (raw_data - np.mean(raw_data)) / np.std(raw_data)
        
        print(f"Raw data range: [{np.min(raw_data):.2f}, {np.max(raw_data):.2f}]")
        print(f"Normalized range: [{np.min(normalized_data):.3f}, {np.max(normalized_data):.3f}]")
        
        print(f"\n✓ Multi-file loading test PASSED!")
        print(f"Ready for full hour anomaly analysis with {len(file_data)} files")
        
        return True
        
    except Exception as e:
        print(f"✗ Concatenation failed: {e}")
        return False

if __name__ == "__main__":
    success = test_multi_file_loading()
    sys.exit(0 if success else 1)
