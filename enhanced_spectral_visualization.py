import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches

def visualize_spectral_anomalies_enhanced(results, channel_name="Unknown", log_freq=True, save_fig=False):
    """
    Enhanced visualization with precise time-frequency anomaly locations.

    Parameters:
    -----------
    results : dict
        Results from analyze_spectral_anomalies
    channel_name : str
        Name of the channel for plot titles
    log_freq : bool
        Use logarithmic frequency scale (better for low frequencies)
    """
    n_bands = len(results['band_anomalies'])
    fig, axes = plt.subplots(2 + n_bands, 1, figsize=(18, 6 + 3*n_bands))
    
    start_time = results['start_time']
    duration = results['duration']
    sample_rate = results['target_sample_rate']
    
    # Set consistent time limits for all plots
    t_min = results['spectrogram_t'][0]
    t_max = results['spectrogram_t'][-1]
    
    scale_text = "Log Scale" if log_freq else "Linear Scale"
    fig.suptitle(f'Valencia DAS Enhanced Spectral Anomaly Analysis - {channel_name}\n'
                f'{start_time:.0f}-{start_time+duration:.0f}s ({duration:.0f}s) at {sample_rate} Hz ({scale_text})',
                fontsize=14, y=0.98)
    
    # 1. Original time series with anomaly markers
    ax1 = axes[0]
    time_axis = np.arange(len(results['segment_resampled'])) / sample_rate + start_time
    ax1.plot(time_axis, results['segment_resampled'], 'b-', alpha=0.7, linewidth=0.8)
    
    # Mark all anomaly time windows
    all_anomaly_times = set()
    for band_name, band_result in results['band_anomalies'].items():
        anomaly_times = band_result['window_starts'][band_result['anomaly_mask']]
        all_anomaly_times.update(anomaly_times)
    
    for t_anom in all_anomaly_times:
        ax1.axvline(t_anom, color='red', alpha=0.4, linewidth=1.5, zorder=2)
    
    ax1.set_title(f'Time Series ({len(all_anomaly_times)} anomaly windows marked)', fontsize=12)
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Strain Rate')
    ax1.set_xlim([t_min, t_max])
    ax1.grid(True, alpha=0.3)
    
    # 2. Enhanced spectrogram with precise anomaly rectangles
    ax2 = axes[1]
    im = ax2.pcolormesh(results['spectrogram_t'], results['spectrogram_f'], 
                       results['spectrogram_Sxx'], shading='gouraud', cmap='viridis')
    
    # Define distinct colors for frequency bands
    colors = ['#FF4444', '#FF8800', '#FFDD00', '#44FF44', '#0088FF', '#8844FF', '#FF44FF']
    
    # Estimate window duration from analysis parameters
    # This should match the actual context + prediction length used
    context_length = 64  # From analysis function
    prediction_length = 16  # From analysis function
    total_window_samples = context_length + prediction_length
    
    # Convert to time duration based on spectrogram time resolution
    spectrogram_dt = results['spectrogram_t'][1] - results['spectrogram_t'][0]
    window_duration = total_window_samples * spectrogram_dt
    
    print(f"Estimated anomaly window duration: {window_duration:.2f} seconds")
    
    # Process each frequency band
    legend_elements = []
    total_anomalies = 0
    
    for i, (band_name, (f_low, f_high)) in enumerate(results['freq_bands'].items()):
        color = colors[i % len(colors)]
        
        # Draw subtle frequency band background
        ax2.axhspan(f_low, f_high, alpha=0.1, color=color, zorder=1)
        
        # Draw frequency band boundaries
        ax2.axhline(f_low, color=color, linestyle='--', alpha=0.6, linewidth=1)
        ax2.axhline(f_high, color=color, linestyle='--', alpha=0.6, linewidth=1)
        
        # Add band label
        f_center = (f_low + f_high) / 2
        ax2.text(t_min + (t_max - t_min) * 0.02, f_center, 
                f'{band_name}', 
                verticalalignment='center', fontsize=10, fontweight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.8, edgecolor='white'))
        
        # Mark anomalies with precise rectangles
        if band_name in results['band_anomalies']:
            band_result = results['band_anomalies'][band_name]

            # Check if we have any anomalies to plot
            if len(band_result['window_starts']) > 0 and len(band_result['anomaly_mask']) > 0:
                anomaly_times = band_result['window_starts'][band_result['anomaly_mask']]
                n_anomalies = len(anomaly_times)
                total_anomalies += n_anomalies

                for j, t_anom in enumerate(anomaly_times):
                    # Draw rectangle at specific time-frequency location
                    rect = plt.Rectangle((t_anom - window_duration/2, f_low),
                                       window_duration, f_high - f_low,
                                       linewidth=2.5, edgecolor=color,
                                       facecolor='none', alpha=0.9, zorder=3)
                    ax2.add_patch(rect)

                    # Add numbered marker at center for identification
                    ax2.plot(t_anom, f_center, 'o', color='white',
                            markersize=8, markeredgecolor=color,
                            markeredgewidth=2, zorder=4)
                    ax2.text(t_anom, f_center, str(j+1),
                            ha='center', va='center', fontsize=8, fontweight='bold',
                            color=color, zorder=5)
            else:
                n_anomalies = 0
        
        # Create legend entry with anomaly count
        if band_name in results['band_anomalies']:
            band_result = results['band_anomalies'][band_name]
            anomaly_count = band_result.get('n_anomalies', 0)
        else:
            anomaly_count = 0

        legend_elements.append(plt.Line2D([0], [0], color=color, lw=4, alpha=0.8,
                                        label=f'{band_name} ({f_low}-{f_high} Hz): {anomaly_count} anomalies'))
    
    ax2.set_title(f'Spectrogram with Precise Anomaly Locations (Total: {total_anomalies} anomalies)', fontsize=12)
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Frequency (Hz)')
    ax2.set_xlim([t_min, t_max])

    # Set frequency limits based on actual frequency bands present
    max_band_freq = max([f_high for _, (f_low, f_high) in results['freq_bands'].items()])
    freq_limit = min(sample_rate/2, max(max_band_freq * 1.1, 25))  # At least 25 Hz or 10% above max band

    # Set frequency scale and limits
    if log_freq:
        # Use log scale for better visualization of low frequencies
        min_freq = max(0.01, min([f_low for _, (f_low, f_high) in results['freq_bands'].items()]))
        ax2.set_yscale('log')
        ax2.set_ylim([min_freq, freq_limit])

        # Add minor ticks for log scale
        from matplotlib.ticker import LogLocator, LogFormatter
        ax2.yaxis.set_major_locator(LogLocator(base=10, numticks=8))
        ax2.yaxis.set_minor_locator(LogLocator(base=10, subs=np.arange(2, 10) * 0.1, numticks=20))
        ax2.yaxis.set_major_formatter(LogFormatter(base=10, labelOnlyBase=False))

        print(f"Using log frequency scale: {min_freq:.3f} - {freq_limit:.1f} Hz")
    else:
        # Linear scale
        ax2.set_ylim([0, freq_limit])
        print(f"Using linear frequency scale: 0 - {freq_limit:.1f} Hz")
    
    # Add legend below the spectrogram
    ax2.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, -0.15), 
              ncol=min(2, len(legend_elements)), fontsize=10)
    
    # Add colorbar
    cbar = plt.colorbar(im, ax=ax2, label='Power (dB)', shrink=0.8)
    cbar.ax.tick_params(labelsize=9)
    
    # 3-N. Individual frequency band analyses with consistent time axis
    for i, (band_name, band_result) in enumerate(results['band_anomalies'].items()):
        ax = axes[2 + i]
        color = colors[i % len(colors)]
        
        # Plot band power over time
        ax.plot(results['spectrogram_t'], band_result['band_power'], 'b-', 
               linewidth=1.5, alpha=0.8, label='Band Power')
        
        # Plot MSE errors (scaled to fit)
        if len(band_result['mse_errors']) > 0:
            # Scale MSE errors to match power range for visualization
            power_range = np.max(band_result['band_power']) - np.min(band_result['band_power'])
            mse_scaled = (band_result['mse_errors'] - np.min(band_result['mse_errors'])) / \
                        (np.max(band_result['mse_errors']) - np.min(band_result['mse_errors']) + 1e-10) * \
                        power_range * 0.3 + np.min(band_result['band_power'])
            
            ax.plot(band_result['window_starts'], mse_scaled, 'g-', 
                   linewidth=1, alpha=0.7, label='MSE Error (scaled)')
            
            # Mark anomalies with rectangles
            anomaly_times = band_result['window_starts'][band_result['anomaly_mask']]
            for j, t_anom in enumerate(anomaly_times):
                # Draw vertical rectangle to highlight anomaly window
                ax.axvspan(t_anom - window_duration/2, t_anom + window_duration/2, 
                          alpha=0.3, color=color, zorder=1)
                
                # Add numbered marker
                ax.plot(t_anom, np.max(band_result['band_power']), 'v', 
                       color=color, markersize=10, markeredgecolor='white', 
                       markeredgewidth=1, zorder=4)
                ax.text(t_anom, np.max(band_result['band_power']) * 1.05, str(j+1), 
                       ha='center', va='bottom', fontsize=8, fontweight='bold',
                       color=color)
            
            # Add threshold line
            threshold_scaled = (band_result['threshold'] - np.min(band_result['mse_errors'])) / \
                              (np.max(band_result['mse_errors']) - np.min(band_result['mse_errors']) + 1e-10) * \
                              power_range * 0.3 + np.min(band_result['band_power'])
            ax.axhline(threshold_scaled, color='red', linestyle='--', alpha=0.7, 
                      label=f'Threshold')
        
        f_low, f_high = band_result['freq_range']
        ax.set_title(f'{band_name.title()} Band ({f_low}-{f_high} Hz) - '
                    f'{band_result["n_anomalies"]} anomalies ({100*band_result["anomaly_rate"]:.1f}%)')
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Power (dB)')
        ax.set_xlim([t_min, t_max])  # Consistent time axis
        ax.legend(fontsize=9)
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()

    # Save plot only if requested
    if save_fig:
        filename = f'valencia_enhanced_spectral_{channel_name}_{start_time:.0f}s_{duration:.0f}s_{sample_rate:.0f}Hz.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n✓ Enhanced spectral visualization saved as: {filename}")

    plt.show()
    print(f"\nVisualization details:")
    print(f"  Window duration: {window_duration:.2f} seconds")
    print(f"  Time range: {t_min:.1f} - {t_max:.1f} seconds")
    print(f"  Total anomalies: {total_anomalies}")
    
    # Print detailed anomaly summary
    print(f"\nDetailed anomaly breakdown:")
    for band_name, band_result in results['band_anomalies'].items():
        f_low, f_high = band_result['freq_range']
        anomaly_times = band_result['window_starts'][band_result['anomaly_mask']]
        print(f"  {band_name} ({f_low}-{f_high} Hz): {len(anomaly_times)} anomalies")
        if len(anomaly_times) > 0:
            print(f"    Times: {[f'{t:.1f}s' for t in anomaly_times[:5]]}" + 
                  (f" ... (+{len(anomaly_times)-5} more)" if len(anomaly_times) > 5 else ""))
    
    return fig, axes

def get_appropriate_freq_bands(sample_rate, environment='land'):
    """
    Get appropriate frequency bands based on sample rate and environment.

    Parameters:
    -----------
    sample_rate : float
        Target sample rate in Hz
    environment : str
        'land', 'sea', or 'transition'

    Returns:
    --------
    dict : Frequency bands appropriate for the sample rate
    """
    nyquist = sample_rate / 2

    if environment == 'land':
        # Land-focused frequency bands
        bands = {
            'infrasound': (0.01, 0.1),
            'low_rumble': (0.1, 1.0),
            'traffic_low': (1.0, 5.0),
            'traffic_high': (5.0, min(15.0, nyquist * 0.6)),
        }

        # Add higher frequency bands if sample rate allows
        if nyquist > 20:
            bands['machinery'] = (15.0, min(35.0, nyquist * 0.8))
        if nyquist > 45:
            bands['high_freq'] = (35.0, min(nyquist * 0.9, 80.0))

    elif environment == 'sea':
        # Marine-focused frequency bands
        bands = {
            'infrasound': (0.01, 0.05),
            'primary_microseism': (0.05, 0.15),
            'secondary_microseism': (0.1, 0.5),
            'swell': (0.5, 2.0),
            'wind_waves': (2.0, min(10.0, nyquist * 0.4)),
        }

        # Add higher bands if sample rate allows
        if nyquist > 15:
            bands['high_marine'] = (10.0, min(nyquist * 0.7, 25.0))

    else:  # transition
        # Mixed land-sea bands
        bands = {
            'infrasound': (0.01, 0.1),
            'microseism': (0.1, 0.5),
            'low_freq': (0.5, 2.0),
            'mixed_signals': (2.0, min(10.0, nyquist * 0.4)),
        }

        # Add higher bands if sample rate allows
        if nyquist > 15:
            bands['traffic_machinery'] = (10.0, min(nyquist * 0.7, 30.0))

    # Filter out bands that exceed Nyquist frequency
    valid_bands = {}
    for name, (f_low, f_high) in bands.items():
        if f_low < nyquist and f_high <= nyquist:
            valid_bands[name] = (f_low, f_high)
        elif f_low < nyquist:
            # Truncate band to Nyquist frequency
            valid_bands[name] = (f_low, nyquist * 0.95)

    return valid_bands

def suggest_analysis_parameters(sample_rate, duration_seconds, environment='land'):
    """
    Suggest appropriate analysis parameters based on sample rate and duration.

    Parameters:
    -----------
    sample_rate : float
        Target sample rate in Hz
    duration_seconds : float
        Duration of analysis window
    environment : str
        'land', 'sea', or 'transition'

    Returns:
    --------
    dict : Suggested parameters
    """
    nyquist = sample_rate / 2

    # Get appropriate frequency bands
    freq_bands = get_appropriate_freq_bands(sample_rate, environment)

    # Suggest context and prediction lengths based on sample rate
    if sample_rate >= 40:
        context_length = 128
        prediction_length = 32
        stride = 16
    elif sample_rate >= 20:
        context_length = 64
        prediction_length = 16
        stride = 8
    elif sample_rate >= 10:
        context_length = 32
        prediction_length = 8
        stride = 4
    else:
        context_length = 16
        prediction_length = 4
        stride = 2

    suggestions = {
        'freq_bands': freq_bands,
        'context_length': context_length,
        'prediction_length': prediction_length,
        'stride': stride,
        'max_frequency': nyquist * 0.9,
        'recommended_duration': {
            'min': max(60, context_length * 2 / sample_rate),
            'max': min(3600, duration_seconds)
        }
    }

    return suggestions

def compare_frequency_scales(results, channel_name="Unknown", save_fig=False):
    """
    Create side-by-side comparison of linear vs log frequency scales.

    Parameters:
    -----------
    results : dict
        Results from analyze_spectral_anomalies
    channel_name : str
        Name of the channel for plot titles
    """
    fig, axes = plt.subplots(1, 2, figsize=(20, 8))

    start_time = results['start_time']
    duration = results['duration']
    sample_rate = results['target_sample_rate']

    # Set consistent time limits
    t_min = results['spectrogram_t'][0]
    t_max = results['spectrogram_t'][-1]

    fig.suptitle(f'Frequency Scale Comparison - {channel_name}\n'
                f'{start_time:.0f}-{start_time+duration:.0f}s ({duration:.0f}s) at {sample_rate} Hz',
                fontsize=14)

    # Define colors for frequency bands
    colors = ['#FF4444', '#FF8800', '#FFDD00', '#44FF44', '#0088FF', '#8844FF', '#FF44FF']

    for scale_idx, (ax, log_scale, title) in enumerate([(axes[0], False, 'Linear Scale'),
                                                        (axes[1], True, 'Log Scale')]):

        # Plot spectrogram
        im = ax.pcolormesh(results['spectrogram_t'], results['spectrogram_f'],
                          results['spectrogram_Sxx'], shading='gouraud', cmap='viridis')

        # Add frequency bands and anomalies
        for i, (band_name, (f_low, f_high)) in enumerate(results['freq_bands'].items()):
            color = colors[i % len(colors)]

            # Draw frequency band boundaries
            ax.axhline(f_low, color=color, linestyle='--', alpha=0.6, linewidth=1)
            ax.axhline(f_high, color=color, linestyle='--', alpha=0.6, linewidth=1)
            ax.axhspan(f_low, f_high, alpha=0.1, color=color, zorder=1)

            # Add band label
            f_center = np.sqrt(f_low * f_high) if log_scale and f_low > 0 else (f_low + f_high) / 2
            ax.text(t_min + (t_max - t_min) * 0.02, f_center,
                   f'{band_name}',
                   verticalalignment='center', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.2', facecolor=color, alpha=0.8, edgecolor='white'))

            # Mark anomalies
            if band_name in results['band_anomalies']:
                band_result = results['band_anomalies'][band_name]
                if len(band_result['window_starts']) > 0 and len(band_result['anomaly_mask']) > 0:
                    anomaly_times = band_result['window_starts'][band_result['anomaly_mask']]
                    for t_anom in anomaly_times:
                        ax.axvline(t_anom, color=color, alpha=0.7, linewidth=2)

        # Set scale and limits
        ax.set_title(title, fontsize=12)
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Frequency (Hz)')
        ax.set_xlim([t_min, t_max])

        # Set frequency limits
        max_band_freq = max([f_high for _, (f_low, f_high) in results['freq_bands'].items()])
        freq_limit = min(sample_rate/2, max(max_band_freq * 1.1, 25))

        if log_scale:
            min_freq = max(0.01, min([f_low for _, (f_low, f_high) in results['freq_bands'].items()]))
            ax.set_yscale('log')
            ax.set_ylim([min_freq, freq_limit])

            # Add log scale formatting
            from matplotlib.ticker import LogLocator, LogFormatter
            ax.yaxis.set_major_locator(LogLocator(base=10, numticks=6))
            ax.yaxis.set_minor_locator(LogLocator(base=10, subs=np.arange(2, 10) * 0.1, numticks=15))
            ax.yaxis.set_major_formatter(LogFormatter(base=10, labelOnlyBase=False))
        else:
            ax.set_ylim([0, freq_limit])

        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, label='Power (dB)', shrink=0.8)
        cbar.ax.tick_params(labelsize=9)

    plt.tight_layout()

    # Save comparison plot only if requested
    if save_fig:
        filename = f'valencia_frequency_scale_comparison_{channel_name}_{start_time:.0f}s.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n✓ Frequency scale comparison saved as: {filename}")

    plt.show()
    print("Linear scale: Good for high-frequency details")
    print("Log scale: Better for low-frequency bands (microseisms, infrasound)")

    return fig, axes

def compare_whitened_spectrograms(results, channel_name="Unknown", save_fig=False):
    """
    Compare original vs whitened spectrograms side by side.

    Parameters:
    -----------
    results : dict
        Results from analyze_spectral_anomalies (must contain both Sxx and Sxx_original)
    channel_name : str
        Name of the channel for plot titles
    """
    if 'spectrogram_Sxx_original' not in results:
        print("Warning: Original spectrogram not available for comparison")
        return None, None

    fig, axes = plt.subplots(1, 2, figsize=(20, 8))

    start_time = results['start_time']
    duration = results['duration']
    sample_rate = results['target_sample_rate']

    # Set consistent time limits
    t_min = results['spectrogram_t'][0]
    t_max = results['spectrogram_t'][-1]

    fig.suptitle(f'Spectrogram Whitening Comparison - {channel_name}\n'
                f'{start_time:.0f}-{start_time+duration:.0f}s ({duration:.0f}s) at {sample_rate} Hz',
                fontsize=14)

    # Define colors for frequency bands
    colors = ['#FF4444', '#FF8800', '#FFDD00', '#44FF44', '#0088FF', '#8844FF', '#FF44FF']

    spectrograms = [
        (results['spectrogram_Sxx_original'], 'Original Spectrogram'),
        (results['spectrogram_Sxx'], 'Whitened Spectrogram (Mean Removed)')
    ]

    for ax_idx, (Sxx_data, title) in enumerate(spectrograms):
        ax = axes[ax_idx]

        # Plot spectrogram
        im = ax.pcolormesh(results['spectrogram_t'], results['spectrogram_f'],
                          Sxx_data, shading='gouraud', cmap='viridis')

        # Add frequency bands and anomalies
        for i, (band_name, (f_low, f_high)) in enumerate(results['freq_bands'].items()):
            color = colors[i % len(colors)]

            # Draw frequency band boundaries
            ax.axhline(f_low, color=color, linestyle='--', alpha=0.6, linewidth=1)
            ax.axhline(f_high, color=color, linestyle='--', alpha=0.6, linewidth=1)
            ax.axhspan(f_low, f_high, alpha=0.1, color=color, zorder=1)

            # Mark anomalies
            if band_name in results['band_anomalies']:
                band_result = results['band_anomalies'][band_name]
                if len(band_result['window_starts']) > 0 and len(band_result['anomaly_mask']) > 0:
                    anomaly_times = band_result['window_starts'][band_result['anomaly_mask']]
                    for t_anom in anomaly_times:
                        ax.axvline(t_anom, color=color, alpha=0.8, linewidth=2)

        # Set scale and limits
        ax.set_title(title, fontsize=12)
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Frequency (Hz)')
        ax.set_xlim([t_min, t_max])

        # Use log scale for better low-frequency visibility
        max_band_freq = max([f_high for _, (f_low, f_high) in results['freq_bands'].items()])
        freq_limit = min(sample_rate/2, max(max_band_freq * 1.1, 25))
        min_freq = max(0.01, min([f_low for _, (f_low, f_high) in results['freq_bands'].items()]))

        ax.set_yscale('log')
        ax.set_ylim([min_freq, freq_limit])

        # Add log scale formatting
        from matplotlib.ticker import LogLocator, LogFormatter
        ax.yaxis.set_major_locator(LogLocator(base=10, numticks=6))
        ax.yaxis.set_minor_locator(LogLocator(base=10, subs=np.arange(2, 10) * 0.1, numticks=15))
        ax.yaxis.set_major_formatter(LogFormatter(base=10, labelOnlyBase=False))

        # Add colorbar with appropriate range
        cbar = plt.colorbar(im, ax=ax, label='Power (dB)', shrink=0.8)
        cbar.ax.tick_params(labelsize=9)

        # Print power range info
        print(f"{title}: Power range [{np.min(Sxx_data):.1f}, {np.max(Sxx_data):.1f}] dB")

    plt.tight_layout()

    # Save comparison plot only if requested
    if save_fig:
        filename = f'valencia_whitening_comparison_{channel_name}_{start_time:.0f}s.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n✓ Whitening comparison saved as: {filename}")

    plt.show()
    print("Original: Shows absolute power levels")
    print("Whitened: Highlights temporal variations (anomalies more visible)")

    return fig, axes

def create_interactive_anomaly_explorer(results, channel_name="Unknown"):
    """
    Create an interactive pop-out window for exploring anomalies in detail.

    Parameters:
    -----------
    results : dict
        Results from analyze_spectral_anomalies
    channel_name : str
        Name of the channel for plot titles
    """
    import matplotlib.pyplot as plt
    from matplotlib.widgets import SpanSelector
    import matplotlib.patches as patches

    # Create a new figure that will pop out
    fig = plt.figure(figsize=(16, 12))
    fig.suptitle(f'Interactive Anomaly Explorer - {channel_name}', fontsize=16)

    # Create subplots
    gs = fig.add_gridspec(4, 2, height_ratios=[1, 1, 1, 1], hspace=0.3, wspace=0.3)

    # Main time series plot (top, spans both columns)
    ax_main = fig.add_subplot(gs[0, :])

    # Individual frequency band plots
    band_axes = []
    for i in range(min(6, len(results['band_anomalies']))):  # Up to 6 bands
        row = 1 + i // 2
        col = i % 2
        if row < 4:  # Don't exceed subplot grid
            ax = fig.add_subplot(gs[row, col])
            band_axes.append(ax)

    # Get data
    start_time = results['start_time']
    duration = results['duration']
    sample_rate = results['target_sample_rate']
    segment = results['segment_resampled']

    # Time axis for the raw signal
    t_signal = np.linspace(start_time, start_time + duration, len(segment))

    # Colors for frequency bands
    colors = ['#FF4444', '#FF8800', '#FFDD00', '#44FF44', '#0088FF', '#8844FF', '#FF44FF']

    # Plot main time series
    ax_main.plot(t_signal, segment, 'b-', alpha=0.7, linewidth=0.8, label='DAS Signal')
    ax_main.set_xlabel('Time (s)')
    ax_main.set_ylabel('Amplitude')
    ax_main.set_title('Raw DAS Time Series with Anomaly Markers', fontsize=14)
    ax_main.grid(True, alpha=0.3)

    # Add anomaly markers to main plot
    anomaly_info = []
    for i, (band_name, band_result) in enumerate(results['band_anomalies'].items()):
        if i >= len(colors):
            break

        color = colors[i]

        if len(band_result['window_starts']) > 0 and len(band_result['anomaly_mask']) > 0:
            anomaly_times = band_result['window_starts'][band_result['anomaly_mask']]

            for j, t_anom in enumerate(anomaly_times):
                # Add vertical line for each anomaly
                ax_main.axvline(t_anom, color=color, alpha=0.8, linewidth=2,
                               linestyle='--', label=f'{band_name}' if j == 0 else "")

                # Add annotation with anomaly info
                y_pos = np.interp(t_anom, t_signal, segment) if len(t_signal) > 0 else 0
                ax_main.annotate(f'{band_name}\n#{j+1}',
                               xy=(t_anom, y_pos),
                               xytext=(5, 10), textcoords='offset points',
                               bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                               fontsize=8, ha='left')

                # Store anomaly info for detailed analysis
                anomaly_info.append({
                    'time': t_anom,
                    'band': band_name,
                    'color': color,
                    'freq_range': band_result['freq_range']
                })

    ax_main.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    # Plot individual frequency band power over time
    for i, (band_name, band_result) in enumerate(results['band_anomalies'].items()):
        if i >= len(band_axes):
            break

        ax = band_axes[i]
        color = colors[i % len(colors)]

        # Get band power and time
        if 'band_power' in band_result:
            band_power = band_result['band_power']
            t_spec = results['spectrogram_t']

            # Plot band power
            ax.plot(t_spec, band_power, color=color, linewidth=1.5, alpha=0.8)
            ax.fill_between(t_spec, band_power, alpha=0.3, color=color)

            # Mark anomalies
            if len(band_result['window_starts']) > 0 and len(band_result['anomaly_mask']) > 0:
                anomaly_times = band_result['window_starts'][band_result['anomaly_mask']]
                anomaly_powers = []

                for t_anom in anomaly_times:
                    # Find closest time index
                    if len(t_spec) > 0:
                        closest_idx = np.argmin(np.abs(t_spec - t_anom))
                        if closest_idx < len(band_power):
                            power_val = band_power[closest_idx]
                            anomaly_powers.append(power_val)

                            # Mark anomaly point
                            ax.plot(t_anom, power_val, 'o', color='red', markersize=8,
                                   markeredgecolor='white', markeredgewidth=2, zorder=5)

                # Plot anomaly threshold
                if 'threshold' in band_result:
                    threshold = band_result['threshold']
                    ax.axhline(threshold, color='red', linestyle=':', alpha=0.7,
                              label=f'Threshold: {threshold:.4f}')

            ax.set_title(f'{band_name} ({band_result["freq_range"][0]:.1f}-{band_result["freq_range"][1]:.1f} Hz)',
                        fontsize=10)
            ax.set_xlabel('Time (s)')
            ax.set_ylabel('Power (dB)')
            ax.grid(True, alpha=0.3)
            ax.legend(fontsize=8)

    # Add interactive span selector for zooming
    def onselect(xmin, xmax):
        """Callback for span selector - zoom all plots to selected range."""
        print(f"Selected time range: {xmin:.1f} - {xmax:.1f} seconds")

        # Zoom main plot
        ax_main.set_xlim(xmin, xmax)

        # Zoom frequency band plots
        for ax in band_axes:
            ax.set_xlim(xmin, xmax)

        # Find anomalies in selected range
        selected_anomalies = [a for a in anomaly_info if xmin <= a['time'] <= xmax]

        if selected_anomalies:
            print(f"Anomalies in selected range:")
            for anom in selected_anomalies:
                print(f"  {anom['time']:.1f}s: {anom['band']} ({anom['freq_range'][0]:.1f}-{anom['freq_range'][1]:.1f} Hz)")
        else:
            print("No anomalies in selected range")

        fig.canvas.draw()

    # Add span selector to main plot (compatible with different matplotlib versions)
    try:
        # Try newer matplotlib API
        span = SpanSelector(ax_main, onselect, direction='horizontal',
                           useblit=True, props=dict(alpha=0.3, facecolor='yellow'))
    except TypeError:
        try:
            # Try older matplotlib API
            span = SpanSelector(ax_main, onselect, direction='horizontal',
                               useblit=True, rectprops=dict(alpha=0.3, facecolor='yellow'))
        except TypeError:
            # Fallback to minimal parameters
            span = SpanSelector(ax_main, onselect, direction='horizontal')

    # Add reset zoom button functionality
    def reset_zoom(event):
        """Reset zoom to show full time range."""
        if event.key == 'r':
            t_min = start_time
            t_max = start_time + duration

            ax_main.set_xlim(t_min, t_max)
            for ax in band_axes:
                ax.set_xlim(t_min, t_max)

            fig.canvas.draw()
            print("Zoom reset to full range")

    fig.canvas.mpl_connect('key_press_event', reset_zoom)

    # Add instructions
    instructions = (
        "🔍 INTERACTIVE CONTROLS:\n"
        "• Click and drag on main plot to zoom to time range\n"
        "• Press 'r' to reset zoom to full range\n"
        "• Anomalies marked with dashed lines and dots\n"
        "• Each color represents a different frequency band"
    )

    fig.text(0.02, 0.02, instructions, fontsize=10,
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8),
             verticalalignment='bottom')

    plt.tight_layout()

    # Make sure the window pops out and is interactive
    plt.show(block=False)  # Non-blocking so script continues

    # Print summary
    print(f"\n🎯 INTERACTIVE ANOMALY EXPLORER LAUNCHED")
    print(f"Total anomalies found: {len(anomaly_info)}")
    print(f"Time range: {start_time:.1f} - {start_time + duration:.1f} seconds")
    print(f"Frequency bands analyzed: {len(results['band_anomalies'])}")

    if anomaly_info:
        print(f"\nAnomalies detected:")
        for i, anom in enumerate(anomaly_info):
            print(f"  {i+1}. t={anom['time']:.1f}s: {anom['band']} band")
    else:
        print("\nNo anomalies detected in this time segment")

    return fig, (ax_main, band_axes), anomaly_info

print("✓ Enhanced spectral visualization function ready")
print("✓ Frequency band suggestion functions ready")
print("✓ Log scale frequency visualization ready")
print("✓ Spectrogram whitening comparison ready")
print("✓ Interactive anomaly explorer ready")
