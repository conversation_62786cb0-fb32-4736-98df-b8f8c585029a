#!/usr/bin/env python3
"""
Visualization script for active DAS sensors with real strain rate data.
This focuses on sensors that have actual signal data.
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# Add src directory to path
sys.path.append('src')

try:
    from chronos_loader import DASChronosDataset, DASChronosDataLoader
    print("✓ Successfully imported chronos_loader modules")
except ImportError as e:
    print(f"✗ Failed to import chronos_loader: {e}")
    sys.exit(1)

def setup_plotting():
    """Set up matplotlib for better plots."""
    plt.style.use('default')
    sns.set_palette("husl")
    plt.rcParams['figure.figsize'] = (15, 10)
    plt.rcParams['font.size'] = 10
    plt.rcParams['axes.grid'] = True
    plt.rcParams['grid.alpha'] = 0.3

def find_active_sensors(dataset, min_std=1.0, sample_size=1000):
    """Find sensors with actual signal (non-zero standard deviation)."""
    print(f"Finding active sensors...")
    
    n_sensors = dataset.data.shape[0]
    active_sensors = []
    
    # Sample data to check which sensors are active
    sample_data = dataset.data[:, :sample_size]
    
    for i in range(n_sensors):
        sensor_data = sample_data[i, :]
        sensor_std = np.std(sensor_data)
        
        if sensor_std > min_std:
            active_sensors.append(i)
    
    print(f"Found {len(active_sensors)} active sensors out of {n_sensors} total")
    print(f"Active sensor indices (first 20): {active_sensors[:20]}")
    
    return active_sensors

def visualize_active_das_data(dataset, active_sensors, max_sensors=10):
    """Visualize DAS data from active sensors."""
    print(f"\n=== Visualizing Active DAS Data ===")
    
    # Select subset of active sensors for visualization
    selected_sensors = active_sensors[:max_sensors]
    
    # Get data subset
    data_subset = dataset.data[selected_sensors, :5000]  # First 5000 timesteps
    time_axis = np.arange(data_subset.shape[1]) / 250  # Assuming 250 Hz sampling
    
    fig, axes = plt.subplots(3, 2, figsize=(18, 12))
    fig.suptitle('Active DAS Sensors - Real Strain Rate Data', fontsize=16)
    
    # 1. Waterfall plot
    ax1 = axes[0, 0]
    im1 = ax1.imshow(data_subset, aspect='auto', cmap='seismic', 
                     extent=[0, time_axis[-1], selected_sensors[-1], selected_sensors[0]],
                     vmin=np.percentile(data_subset, 5), 
                     vmax=np.percentile(data_subset, 95))
    ax1.set_title('Waterfall Plot - Active Sensors')
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Sensor Index')
    plt.colorbar(im1, ax=ax1, label='Strain Rate')
    
    # 2. Time series for selected sensors
    ax2 = axes[0, 1]
    colors = plt.cm.tab10(np.linspace(0, 1, min(5, len(selected_sensors))))
    for i, sensor_idx in enumerate(selected_sensors[:5]):
        ax2.plot(time_axis[:1000], data_subset[i, :1000], 
                color=colors[i], label=f'Sensor {sensor_idx}', alpha=0.8)
    ax2.set_title('Time Series - First 4 seconds')
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Strain Rate')
    ax2.legend()
    
    # 3. Distribution of values
    ax3 = axes[1, 0]
    all_values = data_subset.flatten()
    finite_values = all_values[np.isfinite(all_values)]
    ax3.hist(finite_values, bins=100, alpha=0.7, edgecolor='black')
    ax3.set_title('Distribution of Strain Rate Values')
    ax3.set_xlabel('Strain Rate')
    ax3.set_ylabel('Frequency')
    ax3.set_yscale('log')
    
    # Add statistics
    mean_val = np.mean(finite_values)
    std_val = np.std(finite_values)
    ax3.axvline(mean_val, color='red', linestyle='--', label=f'Mean: {mean_val:.2f}')
    ax3.axvline(mean_val + std_val, color='orange', linestyle='--', alpha=0.7, label=f'+1σ: {mean_val + std_val:.2f}')
    ax3.axvline(mean_val - std_val, color='orange', linestyle='--', alpha=0.7, label=f'-1σ: {mean_val - std_val:.2f}')
    ax3.legend()
    
    # 4. Sensor activity levels
    ax4 = axes[1, 1]
    sensor_stds = [np.std(data_subset[i, :]) for i in range(len(selected_sensors))]
    ax4.bar(range(len(selected_sensors)), sensor_stds, alpha=0.7)
    ax4.set_title('Sensor Activity Levels (Std Dev)')
    ax4.set_xlabel('Sensor Index (in selection)')
    ax4.set_ylabel('Standard Deviation')
    ax4.set_xticks(range(len(selected_sensors)))
    ax4.set_xticklabels([f'S{selected_sensors[i]}' for i in range(len(selected_sensors))], rotation=45)
    
    # 5. Frequency content (simple)
    ax5 = axes[2, 0]
    # Take FFT of first active sensor
    sample_signal = data_subset[0, :1000]  # 4 seconds at 250 Hz
    fft_vals = np.abs(np.fft.fft(sample_signal))
    freqs = np.fft.fftfreq(len(sample_signal), 1/250)
    
    # Plot only positive frequencies up to 50 Hz
    pos_mask = (freqs >= 0) & (freqs <= 50)
    ax5.plot(freqs[pos_mask], fft_vals[pos_mask])
    ax5.set_title(f'Frequency Content - Sensor {selected_sensors[0]}')
    ax5.set_xlabel('Frequency (Hz)')
    ax5.set_ylabel('Amplitude')
    ax5.set_xlim(0, 50)
    
    # 6. Cross-correlation between sensors
    ax6 = axes[2, 1]
    if len(selected_sensors) >= 2:
        # Compute cross-correlation between first two active sensors
        sig1 = data_subset[0, :1000]
        sig2 = data_subset[1, :1000]
        
        # Normalize signals
        sig1_norm = (sig1 - np.mean(sig1)) / np.std(sig1)
        sig2_norm = (sig2 - np.mean(sig2)) / np.std(sig2)
        
        # Compute cross-correlation
        correlation = np.correlate(sig1_norm, sig2_norm, mode='full')
        lags = np.arange(-len(sig2_norm) + 1, len(sig1_norm))
        
        ax6.plot(lags / 250, correlation)  # Convert to time
        ax6.set_title(f'Cross-correlation: Sensors {selected_sensors[0]} & {selected_sensors[1]}')
        ax6.set_xlabel('Lag (s)')
        ax6.set_ylabel('Correlation')
        ax6.axvline(0, color='red', linestyle='--', alpha=0.7)
    else:
        ax6.text(0.5, 0.5, 'Need at least 2 active sensors', 
                ha='center', va='center', transform=ax6.transAxes)
    
    plt.tight_layout()
    plt.savefig('active_das_data_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✓ Active DAS data visualization saved as 'active_das_data_analysis.png'")

def visualize_chronos_batch_active(dataset, active_sensors, batch_size=4):
    """Visualize Chronos batch with active sensors."""
    print(f"\n=== Visualizing Chronos Batch with Active Sensors ===")
    
    # Create DataLoader with focus on active sensors
    dataloader = DASChronosDataLoader.create_train_loader(
        dataset, batch_size=batch_size, shuffle=True, num_workers=0
    )
    
    # Get batches until we find ones with active sensors
    active_batch = None
    for i, batch in enumerate(dataloader):
        sensor_indices = batch['sensor_idx'].numpy()
        # Check if any sensors in this batch are active
        if any(idx in active_sensors for idx in sensor_indices):
            active_batch = batch
            break
        if i > 100:  # Don't search forever
            break
    
    if active_batch is None:
        print("Could not find batch with active sensors")
        return
    
    past_values = active_batch['past_values'].numpy()
    future_values = active_batch['future_values'].numpy()
    sensor_indices = active_batch['sensor_idx'].numpy()
    
    print(f"Found batch with sensors: {sensor_indices}")
    print(f"Active sensors in batch: {[idx for idx in sensor_indices if idx in active_sensors]}")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Chronos Batch - Active DAS Sensors', fontsize=16)
    
    colors = plt.cm.tab10(np.linspace(0, 1, batch_size))
    
    # 1. Context sequences
    ax1 = axes[0, 0]
    for i in range(batch_size):
        is_active = sensor_indices[i] in active_sensors
        alpha = 1.0 if is_active else 0.3
        linestyle = '-' if is_active else '--'
        label = f'Sensor {sensor_indices[i]}' + (' (active)' if is_active else ' (inactive)')
        
        ax1.plot(past_values[i], color=colors[i], alpha=alpha, 
                linestyle=linestyle, label=label)
    
    ax1.set_title('Context Sequences')
    ax1.set_xlabel('Time Steps')
    ax1.set_ylabel('Normalized Strain Rate')
    ax1.legend()
    
    # 2. Target sequences
    ax2 = axes[0, 1]
    for i in range(batch_size):
        is_active = sensor_indices[i] in active_sensors
        alpha = 1.0 if is_active else 0.3
        linestyle = '-' if is_active else '--'
        label = f'Sensor {sensor_indices[i]}' + (' (active)' if is_active else ' (inactive)')
        
        ax2.plot(future_values[i], color=colors[i], alpha=alpha,
                linestyle=linestyle, label=label)
    
    ax2.set_title('Target Sequences')
    ax2.set_xlabel('Time Steps')
    ax2.set_ylabel('Normalized Strain Rate')
    ax2.legend()
    
    # 3. Combined sequence for most active sensor
    ax3 = axes[1, 0]
    # Find the most active sensor in the batch
    active_in_batch = [i for i, idx in enumerate(sensor_indices) if idx in active_sensors]
    if active_in_batch:
        most_active_idx = active_in_batch[0]
        context_len = past_values.shape[1]
        
        full_sequence = np.concatenate([past_values[most_active_idx], future_values[most_active_idx]])
        time_axis = np.arange(len(full_sequence))
        
        ax3.plot(time_axis[:context_len], past_values[most_active_idx], 
                'b-', linewidth=2, label='Context')
        ax3.plot(time_axis[context_len:], future_values[most_active_idx], 
                'r-', linewidth=2, label='Target')
        ax3.axvline(context_len, color='black', linestyle='--', alpha=0.7, label='Prediction Point')
        
        ax3.set_title(f'Context → Target (Active Sensor {sensor_indices[most_active_idx]})')
        ax3.set_xlabel('Time Steps')
        ax3.set_ylabel('Normalized Strain Rate')
        ax3.legend()
    else:
        ax3.text(0.5, 0.5, 'No active sensors in batch', 
                ha='center', va='center', transform=ax3.transAxes)
    
    # 4. Statistics comparison
    ax4 = axes[1, 1]
    context_means = np.mean(past_values, axis=1)
    target_means = np.mean(future_values, axis=1)
    
    x_pos = np.arange(batch_size)
    width = 0.35
    
    colors_active = ['green' if sensor_indices[i] in active_sensors else 'gray' for i in range(batch_size)]
    
    ax4.bar(x_pos - width/2, context_means, width, label='Context Mean', 
           color=colors_active, alpha=0.7)
    ax4.bar(x_pos + width/2, target_means, width, label='Target Mean', 
           color=colors_active, alpha=0.5)
    
    ax4.set_title('Batch Statistics (Green=Active, Gray=Inactive)')
    ax4.set_xlabel('Sample Index')
    ax4.set_ylabel('Mean Normalized Strain Rate')
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels([f'S{sensor_indices[i]}' for i in range(batch_size)])
    ax4.legend()
    
    plt.tight_layout()
    plt.savefig('chronos_batch_active_sensors.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✓ Chronos batch visualization saved as 'chronos_batch_active_sensors.png'")

def main():
    """Main visualization function."""
    print("=== Active DAS Data Visualization ===")
    
    setup_plotting()
    
    # Find H5 file
    data_dir = Path("Data")
    h5_files = list(data_dir.rglob("*.h5"))
    
    if not h5_files:
        print("✗ No H5 files found in Data directory")
        return False
    
    test_file = h5_files[0]
    print(f"Using file: {test_file}")
    
    try:
        # Create dataset with normalization
        dataset = DASChronosDataset(
            data_path=test_file,
            context_length=128,
            prediction_length=32,
            stride=64,
            normalize=True,
            normalization_method='z_score',
            apply_filtering=False  # Start without filtering
        )
        
        print(f"✓ Dataset created with {len(dataset)} samples")
        
        # Find active sensors
        active_sensors = find_active_sensors(dataset)
        
        if len(active_sensors) == 0:
            print("✗ No active sensors found!")
            return False
        
        # Visualize active DAS data
        visualize_active_das_data(dataset, active_sensors)
        
        # Visualize Chronos batch with active sensors
        visualize_chronos_batch_active(dataset, active_sensors)
        
        print(f"\n=== Visualization Complete ===")
        print("Generated files:")
        print("  - active_das_data_analysis.png")
        print("  - chronos_batch_active_sensors.png")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during visualization: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
