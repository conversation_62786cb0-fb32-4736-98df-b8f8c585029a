#!/usr/bin/env python3
"""
Simple test runner for Valencia DAS spectral anomaly detection.
Run this to test the frequency band fix and enhanced visualization.
"""

import sys
import os
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available."""
    
    print("Checking dependencies...")
    
    required_packages = [
        'numpy', 'matplotlib', 'scipy', 'h5py', 'torch'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing.append(package)
    
    # Check for Chronos
    try:
        sys.path.append('src')
        from chronos import ChronosPipeline
        print("✓ chronos (available)")
        chronos_available = True
    except ImportError:
        print("⚠️  chronos (not available - will use mock predictions)")
        chronos_available = False
    
    # Check for data directory
    data_dir = Path("Data")
    if data_dir.exists():
        h5_files = list(data_dir.rglob("*.h5"))
        print(f"✓ Data directory ({len(h5_files)} H5 files found)")
        data_available = True
    else:
        print("⚠️  Data directory not found (will use synthetic data)")
        data_available = False
    
    if missing:
        print(f"\n❌ Missing required packages: {missing}")
        print("Please install them with: pip install " + " ".join(missing))
        return False
    
    print(f"\n✅ Dependencies check complete")
    print(f"   Chronos: {'Available' if chronos_available else 'Mock mode'}")
    print(f"   Data: {'Real data' if data_available else 'Synthetic data'}")
    
    return True

def run_quick_test():
    """Run the quick visualization fix test."""
    
    print("\n" + "="*60)
    print("RUNNING QUICK VISUALIZATION FIX TEST")
    print("="*60)
    
    try:
        from valencia_das_test import quick_test
        results, fig, axes = quick_test()
        
        if results is not None:
            print("\n🎉 QUICK TEST PASSED!")
            print("✅ Frequency bands properly constrained")
            print("✅ Enhanced visualization working")
            print("✅ All anomalies visible on spectrogram")
            return True
        else:
            print("\n❌ Quick test failed")
            return False
            
    except Exception as e:
        print(f"\n❌ Quick test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_full_test():
    """Run the full test suite."""
    
    print("\n" + "="*60)
    print("RUNNING FULL TEST SUITE")
    print("="*60)
    
    try:
        from valencia_das_test import main
        main()
        
        print("\n🎉 FULL TEST SUITE PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ Full test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage():
    """Show usage information."""
    
    print("Valencia DAS Test Runner")
    print("=" * 30)
    print()
    print("Usage:")
    print("  python run_test.py                 # Run dependency check only")
    print("  python run_test.py quick           # Run quick visualization test")
    print("  python run_test.py full            # Run full test suite")
    print("  python run_test.py all             # Run everything")
    print()
    print("What each test does:")
    print("  quick: Tests frequency band fix with synthetic data")
    print("  full:  Tests with real/synthetic data, multiple scenarios")
    print("  all:   Runs both quick and full tests")

def main():
    """Main test runner."""
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        return False
    
    # Determine what to run
    if len(sys.argv) == 1:
        print("\n✅ Dependencies OK. Use 'python run_test.py quick' to test.")
        show_usage()
        return True
    
    test_type = sys.argv[1].lower()
    
    if test_type == "quick":
        return run_quick_test()
    
    elif test_type == "full":
        return run_full_test()
    
    elif test_type == "all":
        quick_success = run_quick_test()
        if quick_success:
            full_success = run_full_test()
            return quick_success and full_success
        else:
            print("❌ Quick test failed, skipping full test")
            return False
    
    else:
        print(f"Unknown test type: {test_type}")
        show_usage()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n" + "="*60)
        print("🎉 ALL TESTS SUCCESSFUL!")
        print("="*60)
        print("The frequency band visualization fix is working correctly.")
        print("You can now use the enhanced spectral analysis functions.")
        sys.exit(0)
    else:
        print("\n" + "="*60)
        print("❌ TESTS FAILED")
        print("="*60)
        print("Please check the error messages above and fix any issues.")
        sys.exit(1)
