#!/usr/bin/env python3
"""
Quick fix for PSD computation to handle variable segment lengths.
"""

import numpy as np
from scipy.signal import welch

def compute_psd_safely(segments, fs=250):
    """
    Compute PSDs safely handling variable segment lengths.
    """
    if not segments:
        return None, None
    
    # Find minimum segment length
    min_length = min(len(seg) for seg in segments)
    
    # Use appropriate nperseg
    nperseg = min(1024, min_length // 4)
    if nperseg < 64:  # Minimum reasonable nperseg
        nperseg = 64
    
    psds = []
    f_ref = None
    
    for segment in segments:
        # Truncate segment if needed
        if len(segment) > min_length:
            segment = segment[:min_length]
        
        try:
            f, psd = welch(segment, fs=fs, nperseg=nperseg)
            
            # Set reference frequency grid from first successful computation
            if f_ref is None:
                f_ref = f
            
            # Only use if frequency grid matches
            if len(f) == len(f_ref):
                psds.append(10 * np.log10(psd + 1e-12))
        except Exception as e:
            print(f"Warning: Failed to compute PSD for segment: {e}")
            continue
    
    if not psds:
        return None, None
    
    return f_ref, np.array(psds)

def validate_anomalies_spectrally(anomaly_segments, normal_segments, fs=250):
    """
    Validate anomalies using spectral analysis.
    """
    print(f"Validating {len(anomaly_segments)} anomaly vs {len(normal_segments)} normal segments")
    
    # Compute PSDs safely
    f_anom, anomaly_psds = compute_psd_safely(anomaly_segments, fs)
    f_norm, normal_psds = compute_psd_safely(normal_segments, fs)
    
    if f_anom is None or f_norm is None:
        print("Failed to compute PSDs")
        return None
    
    if len(f_anom) != len(f_norm):
        print("Frequency grids don't match")
        return None
    
    # Compute mean PSDs
    anomaly_psd_mean = np.mean(anomaly_psds, axis=0)
    normal_psd_mean = np.mean(normal_psds, axis=0)
    
    # Compute spectral ratio
    spectral_ratio = anomaly_psd_mean - normal_psd_mean
    
    # Find maximum difference
    max_diff_idx = np.argmax(np.abs(spectral_ratio))
    max_diff_freq = f_anom[max_diff_idx]
    max_diff_value = spectral_ratio[max_diff_idx]
    
    # Validation assessment
    if abs(max_diff_value) > 3:
        validation = "VALID - Strong spectral signature"
    elif abs(max_diff_value) > 1:
        validation = "LIKELY - Moderate spectral signature"
    else:
        validation = "QUESTIONABLE - Weak spectral signature"
    
    results = {
        'frequencies': f_anom,
        'anomaly_psd_mean': anomaly_psd_mean,
        'normal_psd_mean': normal_psd_mean,
        'spectral_ratio': spectral_ratio,
        'max_diff_freq': max_diff_freq,
        'max_diff_value': max_diff_value,
        'validation': validation,
        'n_anomaly_psds': len(anomaly_psds),
        'n_normal_psds': len(normal_psds)
    }
    
    print(f"Max spectral difference: {max_diff_value:+.1f} dB at {max_diff_freq:.1f} Hz")
    print(f"Validation: {validation}")
    
    return results

if __name__ == "__main__":
    print("PSD computation fix functions loaded")
    print("Use validate_anomalies_spectrally(anomaly_segments, normal_segments) in your notebook")
